"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetStringRegKey = void 0;
const windowRegistry = process.platform === 'win32' ? require('../build/Release/winregistry.node') : null;
function GetStringRegKey(hive, path, name) {
    if (windowRegistry) {
        return windowRegistry.GetStringRegKey(hive, path, name);
    }
    throw new Error('GetStringRegKey is only available on Windows.');
}
exports.GetStringRegKey = GetStringRegKey;
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/node_modules/@vscode/windows-registry/dist/index.js.map