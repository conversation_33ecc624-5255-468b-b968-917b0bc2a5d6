(()=>{var e={273:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,".pORror3dhFo_jlOpYDCf{align-items:center;background:var(--vscode-input-background);border:1px solid transparent;border-radius:2px;display:flex;flex-grow:1}.pORror3dhFo_jlOpYDCf:focus-within{border-color:var(--vscode-focusBorder)}.pORror3dhFo_jlOpYDCf>input{background:none;border:0;color:var(--vscode-input-foreground);flex-grow:1;font-family:var(--vscode-font-family);font-size:var(--vscode-editor-font-size);font-weight:var(--vscode-editor-font-weight);padding:3px 4px}.pORror3dhFo_jlOpYDCf>input::-moz-placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input::placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input:focus{outline:0}.pORror3dhFo_jlOpYDCf>button{align-self:stretch;flex-shrink:0;margin-bottom:2px;margin-top:2px;padding-bottom:1px;padding-top:1px}.TxnreUUAKeaIj08v1D74{outline:1px solid var(--vscode-inputValidation-errorBorder)!important}",""]),a.locals={wrapper:"pORror3dhFo_jlOpYDCf",error:"TxnreUUAKeaIj08v1D74"};const s=a},838:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,"._10qE_dwQvcTIkxXSevC{align-items:center;background:var(--vscode-editorWidget-background);box-shadow:0 0 8px 2px var(--vscode-widget-shadow);box-sizing:border-box;display:flex;height:33px;padding:4px;position:relative}",""]),a.locals={f:"_10qE_dwQvcTIkxXSevC"};const s=a},646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,".xmE80NYmNXD_PVlCFkpw{background:var(--vscode-inputValidation-errorBackground);border:1px solid var(--vscode-inputValidation-errorBorder);padding:4px;position:absolute;top:calc(100% - 4px);z-index:1}",""]),a.locals={error:"xmE80NYmNXD_PVlCFkpw"};const s=a},94:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,".kMe1rh3sGlRge1xNo2FI{align-items:center;background:none;border:1px solid transparent;border-radius:3px;color:var(--vscode-editorWidget-foreground);cursor:pointer;display:flex;margin-left:2px;margin-right:2px;outline:0!important;padding:1px}.kMe1rh3sGlRge1xNo2FI+.kMe1rh3sGlRge1xNo2FI{margin-left:0}.kMe1rh3sGlRge1xNo2FI:hover{background-color:var(--vscode-inputOption-hoverBackground)}.kMe1rh3sGlRge1xNo2FI:focus{border-color:var(--vscode-focusBorder)}.kMe1rh3sGlRge1xNo2FI[aria-checked=true]{background:var(--vscode-inputOption-activeBackground)!important;border:1px solid var(--vscode-inputOption-activeBorder);color:var(--vscode-inputOption-activeForeground)}.kMe1rh3sGlRge1xNo2FI>svg{height:16px;width:16px}",""]),a.locals={button:"kMe1rh3sGlRge1xNo2FI"};const s=a},71:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.f0Jk8dUahhi2qYS9k4oW{display:flex;flex-direction:column;height:100vh}.zHL1SBvvBysxPwQUMdES{flex-shrink:0;padding-bottom:10px}.Cekf7yByuJxmXr1F30ko{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"f0Jk8dUahhi2qYS9k4oW",filter:"zHL1SBvvBysxPwQUMdES",rows:"Cekf7yByuJxmXr1F30ko"};const s=a},13:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.s60FX4Gsndji9tYAIKUs{display:flex;flex-direction:column;height:100vh}.b9nxfCTXGDNxo2IiwNIH{flex-shrink:0;padding-bottom:10px}.cBcdOYmT9_99aVdm9PZF{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"s60FX4Gsndji9tYAIKUs",filter:"b9nxfCTXGDNxo2IiwNIH",rows:"cBcdOYmT9_99aVdm9PZF"};const s=a},925:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(173),o=r.n(n),i=r(806),a=r.n(i)()(o());a.push([e.id,".oKKFacfbvSqqq_aC8AE3{flex-grow:1;font-family:var(--vscode-editor-font-family);overflow:auto}.SmGXcbNWaLnOSaUk6_wl{cursor:default;display:flex;height:23px;-webkit-user-select:none;-moz-user-select:none;user-select:none}.SmGXcbNWaLnOSaUk6_wl:focus{background:var(--vscode-list-focusBackground);color:var(--vscode-list-focusForeground);outline:0}.SmGXcbNWaLnOSaUk6_wl>div{margin:2px 4px}.MujOaFcerHdWdwd6a6Up{cursor:pointer;margin-left:244px!important}.QgpWiq7tKfenPd7TZWkz,.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{text-align:right;width:110px}.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{cursor:pointer}.enzbXVI4PCTd9u4rZnht svg{display:inline-block;height:1em;margin-right:.25em}.QgpWiq7tKfenPd7TZWkz{color:var(--vscode-terminal-ansiYellow);flex-shrink:0;z-index:0}.QgpWiq7tKfenPd7TZWkz,.QgpWiq7tKfenPd7TZWkz>span{position:relative}.ly1Zy3MRQIzbgri0u5Nx{align-items:center;color:var(--vscode-terminal-foreground);display:flex;flex-grow:1;overflow:hidden;padding-left:10px}.ly1Zy3MRQIzbgri0u5Nx.eyDJMWoZxGo2OH8Utijk{opacity:.5}.ly1Zy3MRQIzbgri0u5Nx a{color:var(--vscode-terminal-foreground);cursor:pointer;text-decoration:none}.ly1Zy3MRQIzbgri0u5Nx a:focus,.ly1Zy3MRQIzbgri0u5Nx a:hover{text-decoration:underline}.ly1Zy3MRQIzbgri0u5Nx a:focus{outline:1px solid var(--vscode-focusBorder)}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{overflow:hidden;white-space:nowrap}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{text-overflow:ellipsis}.NFJunN7lzsRC155ophiH{flex-shrink:0;max-width:calc(100% - 20px)}.e_Y8B9frar9b2mXzvehf{direction:rtl;flex-grow:1;flex-shrink:1;font-family:var(--vscode-font-family);font-size:.8em;margin-left:2em;opacity:.8}.fvv0GAqFPWwrnW2jN8rt{background:none;border:0;flex-shrink:0;opacity:.7;outline:0}.fvv0GAqFPWwrnW2jN8rt,.fvv0GAqFPWwrnW2jN8rt svg{cursor:pointer;width:1em}.SmGXcbNWaLnOSaUk6_wl:hover .fvv0GAqFPWwrnW2jN8rt{opacity:1}.IWaoWposwXCYN2K4Z1vv{background:hsla(0,0%,100%,.1);border-bottom:2px solid hsla(0,0%,100%,.2);bottom:0;left:0;position:absolute;right:0;top:0;transform-origin:100%;z-index:-1}.vscode-light .IWaoWposwXCYN2K4Z1vv{background:rgba(0,0,0,.2)}.vscode-high-contrast .IWaoWposwXCYN2K4Z1vv{background:#fff}",""]),a.locals={rows:"oKKFacfbvSqqq_aC8AE3",row:"SmGXcbNWaLnOSaUk6_wl",footer:"MujOaFcerHdWdwd6a6Up",duration:"QgpWiq7tKfenPd7TZWkz",heading:"enzbXVI4PCTd9u4rZnht",timing:"XyAbSpz9jylz8ZS39BwX",location:"ly1Zy3MRQIzbgri0u5Nx",virtual:"eyDJMWoZxGo2OH8Utijk",file:"e_Y8B9frar9b2mXzvehf",fn:"NFJunN7lzsRC155ophiH",expander:"fvv0GAqFPWwrnW2jN8rt",impactBar:"IWaoWposwXCYN2K4Z1vv"};const s=a},806:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(n)for(var s=0;s<this.length;s++){var l=this[s][0];null!=l&&(a[l]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);n&&a[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),r&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=r):u[2]=r),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},173:e=>{"use strict";e.exports=function(e){return e[1]}},604:e=>{"use strict";var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var i={},a=[],s=0;s<e.length;s++){var l=e[s],c=n.base?l[0]+n.base:l[0],u=i[c]||0,d="".concat(c," ").concat(u);i[c]=u+1;var _=r(d),p={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==_)t[_].references++,t[_].updater(p);else{var f=o(p,n);n.byIndex=s,t.splice(s,0,{identifier:d,updater:f,references:1})}a.push(d)}return a}function o(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var i=n(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var s=r(i[a]);t[s].references--}for(var l=n(e,o),c=0;c<i.length;c++){var u=r(i[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=l}}},863:e=>{"use strict";var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},896:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},124:(e,t,r)=>{"use strict";e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},101:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},917:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},113:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M8.85352 11.7021H7.85449L7.03809 9.54297H3.77246L3.00439 11.7021H2L4.9541 4H5.88867L8.85352 11.7021ZM6.74268 8.73193L5.53418 5.4502C5.49479 5.34277 5.4554 5.1709 5.41602 4.93457H5.39453C5.35872 5.15299 5.31755 5.32487 5.271 5.4502L4.07324 8.73193H6.74268Z"></path><path d="M13.756 11.7021H12.8752V10.8428H12.8537C12.4706 11.5016 11.9066 11.8311 11.1618 11.8311C10.6139 11.8311 10.1843 11.686 9.87273 11.396C9.56479 11.106 9.41082 10.721 9.41082 10.2412C9.41082 9.21354 10.016 8.61556 11.2262 8.44727L12.8752 8.21631C12.8752 7.28174 12.4974 6.81445 11.7419 6.81445C11.0794 6.81445 10.4815 7.04004 9.94793 7.49121V6.58887C10.4886 6.24512 11.1117 6.07324 11.8171 6.07324C13.1097 6.07324 13.756 6.75716 13.756 8.125V11.7021ZM12.8752 8.91992L11.5485 9.10254C11.1403 9.15983 10.8324 9.26188 10.6247 9.40869C10.417 9.55192 10.3132 9.80794 10.3132 10.1768C10.3132 10.4453 10.4081 10.6655 10.5978 10.8374C10.7912 11.0057 11.0472 11.0898 11.3659 11.0898C11.8027 11.0898 12.1626 10.9377 12.4455 10.6333C12.7319 10.3254 12.8752 9.93685 12.8752 9.46777V8.91992Z"></path></svg>'},84:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.976 10.072l4.357-4.357.62.618L8.284 11h-.618L3 6.333l.619-.618 4.357 4.357z"></path></svg>'},974:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.072 8.024L5.715 3.667l.618-.62L11 7.716v.618L6.333 13l-.618-.619 4.357-4.357z"></path></svg>'},423:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M9.13 15l-.53-.77a1.85 1.85 0 0 0-.28-2.54 3.51 3.51 0 0 1-1.19-2c-1.56 2.23-.75 3.46 0 4.55l-.55.76A4.4 4.4 0 0 1 3 10.46S2.79 8.3 5.28 6.19c0 0 2.82-2.61 1.84-4.54L7.83 1a6.57 6.57 0 0 1 2.61 6.94 2.57 2.57 0 0 0 .56-.81l.87-.07c.07.12 1.84 2.93.89 5.3A4.72 4.72 0 0 1 9.13 15zm-2-6.95l.87.39a3 3 0 0 0 .92 2.48 2.64 2.64 0 0 1 1 2.8A3.241 3.241 0 0 0 11.8 12a4.87 4.87 0 0 0-.41-3.63 1.85 1.85 0 0 1-1.84.86l-.35-.68a5.31 5.31 0 0 0-.89-5.8C8.17 4.87 6 6.83 5.93 6.94 3.86 8.7 4 10.33 4 10.4a3.47 3.47 0 0 0 1.59 3.14C5 12.14 5 10.46 7.16 8.05h-.03z"></path></svg>'},143:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.012 2h.976v3.113l2.56-1.557.486.885L11.47 6l2.564 1.559-.485.885-2.561-1.557V10h-.976V6.887l-2.56 1.557-.486-.885L9.53 6 6.966 4.441l.485-.885 2.561 1.557V2zM2 10h4v4H2v-4z"></path></svg>'}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nc=void 0,(()=>{"use strict";var e,t,n,o,i,a,s,l,c={},u=[],d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_=Array.isArray;function p(e,t){for(var r in t)e[r]=t[r];return e}function f(e){var t=e.parentNode;t&&t.removeChild(e)}function h(t,r,n){var o,i,a,s={};for(a in r)"key"==a?o=r[a]:"ref"==a?i=r[a]:s[a]=r[a];if(arguments.length>2&&(s.children=arguments.length>3?e.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===s[a]&&(s[a]=t.defaultProps[a]);return v(t,s,o,i,null)}function v(e,r,o,i,a){var s={type:e,props:r,key:o,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==a?++n:a,__i:-1};return null==a&&null!=t.vnode&&t.vnode(s),s}function m(e){return e.children}function g(e,t){this.props=e,this.context=t}function y(e,t){if(null==t)return e.__?y(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?y(e):null}function b(e){var t,r;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e){e.__e=e.__c.base=r.__e;break}return b(e)}}function x(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!w.__r++||i!==t.debounceRendering)&&((i=t.debounceRendering)||a)(w)}function w(){var e,t,r,n,i,a,l,c,u;for(o.sort(s);e=o.shift();)e.__d&&(t=o.length,n=void 0,i=void 0,a=void 0,c=(l=(r=e).__v).__e,(u=r.__P)&&(n=[],i=[],(a=p({},l)).__v=l.__v+1,P(u,a,l,r.__n,void 0!==u.ownerSVGElement,null!=l.__h?[c]:null,n,null==c?y(l):c,l.__h,i),a.__.__k[a.__i]=a,I(n,a,i),a.__e!=c&&b(a)),o.length>t&&o.sort(s));w.__r=0}function k(e,t,r,n,o,i,a,s,l,d,p){var f,h,g,b,x,w,k,T,A,F=0,M=n&&n.__k||u,I=M.length,z=I,L=t.length;for(r.__k=[],f=0;f<L;f++)null!=(b=r.__k[f]=null==(b=t[f])||"boolean"==typeof b||"function"==typeof b?null:b.constructor==String||"number"==typeof b||"bigint"==typeof b?v(null,b,null,null,b):_(b)?v(m,{children:b},null,null,null):b.__b>0?v(b.type,b.props,b.key,b.ref?b.ref:null,b.__v):b)?(b.__=r,b.__b=r.__b+1,b.__i=f,-1===(T=S(b,M,k=f+F,z))?g=c:(g=M[T]||c,M[T]=void 0,z--),P(e,b,g,o,i,a,s,l,d,p),x=b.__e,(h=b.ref)&&g.ref!=h&&(g.ref&&E(g.ref,null,b),p.push(h,b.__c||x,b)),null==w&&null!=x&&(w=x),(A=g===c||null===g.__v)?-1==T&&F--:T!==k&&(T===k+1?F++:T>k?z>L-k?F+=T-k:F--:F=T<k&&T==k-1?T-k:0),k=f+F,"function"==typeof b.type?(T!==k||g.__k===b.__k?l=N(b,l,e):void 0!==b.__d?l=b.__d:x&&(l=x.nextSibling),b.__d=void 0):x&&(l=T!==k||A?C(e,x,l):x.nextSibling),"function"==typeof r.type&&(r.__d=l)):(g=M[f])&&null==g.key&&g.__e&&(g.__e==l&&(l=y(g),"function"==typeof r.type&&(r.__d=l)),H(g,g,!1),M[f]=null);for(r.__e=w,f=I;f--;)null!=M[f]&&("function"==typeof r.type&&null!=M[f].__e&&M[f].__e==l&&(r.__d=M[f].__e.nextSibling),H(M[f],M[f]))}function N(e,t,r){for(var n,o=e.__k,i=0;o&&i<o.length;i++)(n=o[i])&&(n.__=e,t="function"==typeof n.type?N(n,t,r):C(r,n.__e,t));return t}function C(e,t,r){return t!=r&&e.insertBefore(t,r||null),t.nextSibling}function S(e,t,r,n){var o=e.key,i=e.type,a=r-1,s=r+1,l=t[r];if(null===l||l&&o==l.key&&i===l.type)return r;if(n>(null!=l?1:0))for(;a>=0||s<t.length;){if(a>=0){if((l=t[a])&&o==l.key&&i===l.type)return a;a--}if(s<t.length){if((l=t[s])&&o==l.key&&i===l.type)return s;s++}}return-1}function T(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||d.test(t)?r:r+"px"}function A(e,t,r,n,o){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||T(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||T(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=Date.now(),e.addEventListener(t,i?M:F,i)):e.removeEventListener(t,i?M:F,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,r))}}function F(e){var r=this.l[e.type+!1];if(e.t){if(e.t<=r.u)return}else e.t=Date.now();return r(t.event?t.event(e):e)}function M(e){return this.l[e.type+!0](t.event?t.event(e):e)}function P(e,r,n,o,i,a,s,l,c,u){var d,f,h,v,y,b,x,w,N,C,S,T,A,F,M,P=r.type;if(void 0!==r.constructor)return null;null!=n.__h&&(c=n.__h,l=r.__e=n.__e,r.__h=null,a=[l]),(d=t.__b)&&d(r);e:if("function"==typeof P)try{if(w=r.props,N=(d=P.contextType)&&o[d.__c],C=d?N?N.props.value:d.__:o,n.__c?x=(f=r.__c=n.__c).__=f.__E:("prototype"in P&&P.prototype.render?r.__c=f=new P(w,C):(r.__c=f=new g(w,C),f.constructor=P,f.render=L),N&&N.sub(f),f.props=w,f.state||(f.state={}),f.context=C,f.__n=o,h=f.__d=!0,f.__h=[],f._sb=[]),null==f.__s&&(f.__s=f.state),null!=P.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=p({},f.__s)),p(f.__s,P.getDerivedStateFromProps(w,f.__s))),v=f.props,y=f.state,f.__v=r,h)null==P.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==P.getDerivedStateFromProps&&w!==v&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(w,C),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(w,f.__s,C)||r.__v===n.__v)){for(r.__v!==n.__v&&(f.props=w,f.state=f.__s,f.__d=!1),r.__e=n.__e,r.__k=n.__k,r.__k.forEach((function(e){e&&(e.__=r)})),S=0;S<f._sb.length;S++)f.__h.push(f._sb[S]);f._sb=[],f.__h.length&&s.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(w,f.__s,C),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(v,y,b)}))}if(f.context=C,f.props=w,f.__P=e,f.__e=!1,T=t.__r,A=0,"prototype"in P&&P.prototype.render){for(f.state=f.__s,f.__d=!1,T&&T(r),d=f.render(f.props,f.state,f.context),F=0;F<f._sb.length;F++)f.__h.push(f._sb[F]);f._sb=[]}else do{f.__d=!1,T&&T(r),d=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++A<25);f.state=f.__s,null!=f.getChildContext&&(o=p(p({},o),f.getChildContext())),h||null==f.getSnapshotBeforeUpdate||(b=f.getSnapshotBeforeUpdate(v,y)),k(e,_(M=null!=d&&d.type===m&&null==d.key?d.props.children:d)?M:[M],r,n,o,i,a,s,l,c,u),f.base=r.__e,r.__h=null,f.__h.length&&s.push(f),x&&(f.__E=f.__=null)}catch(e){r.__v=null,c||null!=a?(r.__e=l,r.__h=!!c,a[a.indexOf(l)]=null):(r.__e=n.__e,r.__k=n.__k),t.__e(e,r,n)}else null==a&&r.__v===n.__v?(r.__k=n.__k,r.__e=n.__e):r.__e=z(n.__e,r,n,o,i,a,s,c,u);(d=t.diffed)&&d(r)}function I(e,r,n){r.__d=void 0;for(var o=0;o<n.length;o++)E(n[o],n[++o],n[++o]);t.__c&&t.__c(r,e),e.some((function(r){try{e=r.__h,r.__h=[],e.some((function(e){e.call(r)}))}catch(e){t.__e(e,r.__v)}}))}function z(t,r,n,o,i,a,s,l,u){var d,p,h,v=n.props,m=r.props,g=r.type,b=0;if("svg"===g&&(i=!0),null!=a)for(;b<a.length;b++)if((d=a[b])&&"setAttribute"in d==!!g&&(g?d.localName===g:3===d.nodeType)){t=d,a[b]=null;break}if(null==t){if(null===g)return document.createTextNode(m);t=i?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,m.is&&m),a=null,l=!1}if(null===g)v===m||l&&t.data===m||(t.data=m);else{if(a=a&&e.call(t.childNodes),p=(v=n.props||c).dangerouslySetInnerHTML,h=m.dangerouslySetInnerHTML,!l){if(null!=a)for(v={},b=0;b<t.attributes.length;b++)v[t.attributes[b].name]=t.attributes[b].value;(h||p)&&(h&&(p&&h.__html==p.__html||h.__html===t.innerHTML)||(t.innerHTML=h&&h.__html||""))}if(function(e,t,r,n,o){var i;for(i in r)"children"===i||"key"===i||i in t||A(e,i,null,r[i],n);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||r[i]===t[i]||A(e,i,t[i],r[i],n)}(t,m,v,i,l),h)r.__k=[];else if(k(t,_(b=r.props.children)?b:[b],r,n,o,i&&"foreignObject"!==g,a,s,a?a[0]:n.__k&&y(n,0),l,u),null!=a)for(b=a.length;b--;)null!=a[b]&&f(a[b]);l||("value"in m&&void 0!==(b=m.value)&&(b!==t.value||"progress"===g&&!b||"option"===g&&b!==v.value)&&A(t,"value",b,v.value,!1),"checked"in m&&void 0!==(b=m.checked)&&b!==t.checked&&A(t,"checked",b,v.checked,!1))}return t}function E(e,r,n){try{"function"==typeof e?e(r):e.current=r}catch(e){t.__e(e,n)}}function H(e,r,n){var o,i;if(t.unmount&&t.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||E(o,null,r)),null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){t.__e(e,r)}o.base=o.__P=null,e.__c=void 0}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&H(o[i],r,n||"function"!=typeof e.type);n||null==e.__e||f(e.__e),e.__=e.__e=e.__d=void 0}function L(e,t,r){return this.constructor(e,r)}e=u.slice,t={__e:function(e,t,r,n){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,n||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},n=0,g.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=p({},this.state),"function"==typeof e&&(e=e(p({},r),this.props)),e&&p(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),x(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),x(this))},g.prototype.render=m,o=[],a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,s=function(e,t){return e.__v.__b-t.__v.__b},w.__r=0,l=0;class W{static root(){return new W({id:-1,category:0,selfTime:0,aggregateTime:0,ticks:0,callFrame:{functionName:"(root)",lineNumber:-1,columnNumber:-1,scriptId:"0",url:""}})}get id(){return this.location.id}get callFrame(){return this.location.callFrame}get src(){return this.location.src}get category(){return this.location.category}constructor(e,t){this.location=e,this.parent=t,this.children={},this.aggregateTime=0,this.selfTime=0,this.ticks=0,this.childrenSize=0}addNode(e){this.selfTime+=e.selfTime,this.aggregateTime+=e.aggregateTime}toJSON(){return{children:this.children,childrenSize:this.childrenSize,aggregateTime:this.aggregateTime,selfTime:this.selfTime,ticks:this.ticks,id:this.id,category:this.category,callFrame:this.callFrame}}}const O=(e,t,r,n=t)=>{let o=e.children[t.locationId];o||(o=new W(r.locations[t.locationId],e),e.childrenSize++,e.children[t.locationId]=o),o.addNode(n),t.parent&&O(o,r.nodes[t.parent],r,n)};var R,U,j,D,q=0,B=[],X=[],Z=t.__b,V=t.__r,$=t.diffed,G=t.__c,Y=t.unmount;function K(e,r){t.__h&&t.__h(U,e,q||r),q=0;var n=U.__H||(U.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({__V:X}),n.__[e]}function Q(e){return q=1,function(e,t,r){var n=K(R++,2);if(n.t=e,!n.__c&&(n.__=[de(void 0,t),function(e){var t=n.__N?n.__N[0]:n.__[0],r=n.t(t,e);t!==r&&(n.__N=[r,n.__[1]],n.__c.setState({}))}],n.__c=U,!U.u)){var o=function(e,t,r){if(!n.__c.__H)return!0;var o=n.__c.__H.__.filter((function(e){return e.__c}));if(o.every((function(e){return!e.__N})))return!i||i.call(this,e,t,r);var a=!1;return o.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),!(!a&&n.__c.props===e)&&(!i||i.call(this,e,t,r))};U.u=!0;var i=U.shouldComponentUpdate,a=U.componentWillUpdate;U.componentWillUpdate=function(e,t,r){if(this.__e){var n=i;i=void 0,o(e,t,r),i=n}a&&a.call(this,e,t,r)},U.shouldComponentUpdate=o}return n.__N||n.__}(de,e)}function J(e,r){var n=K(R++,3);!t.__s&&ue(n.__H,r)&&(n.__=e,n.i=r,U.__H.__h.push(n))}function ee(e,r){var n=K(R++,4);!t.__s&&ue(n.__H,r)&&(n.__=e,n.i=r,U.__h.push(n))}function te(e){return q=5,re((function(){return{current:e}}),[])}function re(e,t){var r=K(R++,7);return ue(r.__H,t)?(r.__V=e(),r.i=t,r.__h=e,r.__V):r.__}function ne(e,t){return q=8,re((function(){return e}),t)}function oe(e){var t=U.context[e.__c],r=K(R++,9);return r.c=e,t?(null==r.__&&(r.__=!0,t.sub(U)),t.props.value):e.__}function ie(){for(var e;e=B.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(le),e.__H.__h.forEach(ce),e.__H.__h=[]}catch(r){e.__H.__h=[],t.__e(r,e.__v)}}t.__b=function(e){U=null,Z&&Z(e)},t.__r=function(e){V&&V(e),R=0;var t=(U=e.__c).__H;t&&(j===U?(t.__h=[],U.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=X,e.__N=e.i=void 0}))):(t.__h.forEach(le),t.__h.forEach(ce),t.__h=[],R=0)),j=U},t.diffed=function(e){$&&$(e);var r=e.__c;r&&r.__H&&(r.__H.__h.length&&(1!==B.push(r)&&D===t.requestAnimationFrame||((D=t.requestAnimationFrame)||se)(ie)),r.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==X&&(e.__=e.__V),e.i=void 0,e.__V=X}))),j=U=null},t.__c=function(e,r){r.some((function(e){try{e.__h.forEach(le),e.__h=e.__h.filter((function(e){return!e.__||ce(e)}))}catch(n){r.some((function(e){e.__h&&(e.__h=[])})),r=[],t.__e(n,e.__v)}})),G&&G(e,r)},t.unmount=function(e){Y&&Y(e);var r,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{le(e)}catch(e){r=e}})),n.__H=void 0,r&&t.__e(r,n.__v))};var ae="function"==typeof requestAnimationFrame;function se(e){var t,r=function(){clearTimeout(n),ae&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(r,100);ae&&(t=requestAnimationFrame(r))}function le(e){var t=U,r=e.__c;"function"==typeof r&&(e.__c=void 0,r()),U=t}function ce(e){var t=U;e.__c=e.__(),U=t}function ue(e,t){return!e||e.length!==t.length||t.some((function(t,r){return t!==e[r]}))}function de(e,t){return"function"==typeof t?t(e):t}var _e,pe=r(113),fe=r(143);!function(e){e[e.String=0]="String",e[e.Number=1]="Number"}(_e||(_e={}));const he={[_e.Number]:{":":e=>t=>t===Number(e),"=":e=>t=>t===Number(e),">":e=>t=>t>Number(e),"<":e=>t=>t<Number(e),"<=":e=>t=>t<=Number(e),">=":e=>t=>t>=Number(e),"<>":e=>t=>t!==Number(e),"!=":e=>t=>t!==Number(e)},[_e.String]:{":":e=>t=>t===e,"=":e=>t=>t===e,"!=":e=>t=>t!==e,"<>":e=>t=>t!==e,"~=":e=>{const t=/^\/(.+)\/([a-z])*$/.exec(e),r=t?new RegExp(t[1],t[2]):new RegExp(e);return e=>(r.lastIndex=0,r.test(e))}}};class ve extends Error{constructor(e,t){super(e),this.index=t}}const me=new Set(Object.values(he).map((e=>Object.keys(e))).reduce(((e,t)=>[...e,...t]),[]));class ge{get eof(){return this.data?.length===this.length||Array.isArray(this._read)}get loaded(){return this.data||[]}static fromArray(e,t){return this.fromTopLevelArray(e,(e=>ge.fromArray(t(e),t)))}static fromTopLevelArray(e,t){const r=new ge(e.length,(()=>Promise.resolve(e)),t);return r.data=e,r}static fromProvider(e,t,r){return ge.fromProvider(e,t,r)}constructor(e,t,r){this.length=e,this._getChildren=r,this.asyncLoads=[],this.children=new Map,Array.isArray(t)||t instanceof Array?this.data=t:this._read=t}setSort(e){e!==this.sortFn&&(this.sortFn=e,this.eof?this.data&&this.data.sort(e):(this.data=void 0,this.asyncLoads=[]))}getChildren(e){let t=this.children.get(e);return t||(t=this._getChildren(e),this.children.set(e,t)),t.setSort(this.sortFn),t}didReadUpTo(e){if(this.eof||!this._read)return!0;const t=this.asyncLoads[this.asyncLoads.length-1];return!!(t&&t.upTo>=e)}async read(e){if(!this._read)return Promise.resolve(this.loaded);const t=this.asyncLoads[this.asyncLoads.length-1]||{upTo:0,p:Promise.resolve()};if(t.upTo>=e)return t.p;const r=t.p.then((async()=>{const r=await this._read(t.upTo,e,this.sortFn);return this.data?.length?this.data=this.data.concat(r):this.data=r,this.data}));return this.asyncLoads.push({upTo:e,p:r}),r}}const ye=(e,t,r,n)=>{let o=!1;t(r)&&(n.selected.add(r),n.selectedAndParents.add(r),o=!0);const i=e.getChildren(r);for(const e of i.loaded)ye(i,t,e,n)&&(n.selectedAndParents.add(r),o=!0);return o};var be=r(604),xe=r.n(be),we=r(101),ke=r.n(we),Ne=r(863),Ce=r.n(Ne),Se=r(124),Te=r.n(Se),Ae=r(896),Fe=r.n(Ae),Me=r(917),Pe=r.n(Me),Ie=r(273),ze={};ze.styleTagTransform=Pe(),ze.setAttributes=Te(),ze.insert=Ce().bind(null,"head"),ze.domAPI=ke(),ze.insertStyleElement=Fe(),xe()(Ie.A,ze);const Ee=Ie.A&&Ie.A.locals?Ie.A.locals:void 0,He=(...e)=>e.filter(Boolean).join(" "),Le=({value:e,hasError:t,min:r,type:n,onChange:o,placeholder:i="Filter for function",foot:a})=>{const s=ne((e=>{o(e.target.value)}),[o]);return h("div",{className:Ee.wrapper},h("input",{className:He(t&&Ee.error),type:n,min:r,value:e,placeholder:i,onPaste:s,onKeyUp:s}),a)};var We=r(838),Oe={};Oe.styleTagTransform=Pe(),Oe.setAttributes=Te(),Oe.insert=Ce().bind(null,"head"),Oe.domAPI=ke(),Oe.insertStyleElement=Fe(),xe()(We.A,Oe);const Re=We.A&&We.A.locals?We.A.locals:void 0,Ue=({children:e})=>h("div",{className:Re.f},e);var je=r(646),De={};De.styleTagTransform=Pe(),De.setAttributes=Te(),De.insert=Ce().bind(null,"head"),De.domAPI=ke(),De.insertStyleElement=Fe(),xe()(je.A,De);const qe=je.A&&je.A.locals?je.A.locals:void 0;var Be=r(94),Xe={};Xe.styleTagTransform=Pe(),Xe.setAttributes=Te(),Xe.insert=Ce().bind(null,"head"),Xe.domAPI=ke(),Xe.insertStyleElement=Fe(),xe()(Be.A,Xe);const Ze=Be.A&&Be.A.locals?Be.A.locals:void 0,Ve=({icon:e,label:t,checked:r,onChange:n,onClick:o})=>{const i=ne((()=>{o?.(),n?.(!r)}),[r,o,n]);return h("button",{className:Ze.button,type:"button",role:"switch",alt:t,title:t,"aria-label":t,"aria-checked":r?"true":"false",dangerouslySetInnerHTML:{__html:e},onClick:i})},$e=function(e,t){var r={__c:t="__cC"+l++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,n;return this.getChildContext||(r=[],(n={})[t]=this,this.getChildContext=function(){return n},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.some((function(e){e.__e=!0,x(e)}))},this.sub=function(e){r.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r.splice(r.indexOf(e),1),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r}(acquireVsCodeApi()),Ge=(e,t)=>{const r=oe($e),[n,o]=Q(r.getState()?.[e]??t);return((t,o)=>{const i=te(!0);J((()=>{i.current?i.current=!1:r.setState({...r.getState(),[e]:n})}),o)})(0,[n]),[n,o]},Ye=()=>({placeholder:e,data:t,onChange:r,foot:n})=>{const[o,i]=Q(!1),[a,s]=Q(!1),[l,c]=Ge("filterText",""),[u,d]=Q(void 0);return J((()=>{try{r((e=>{const t=((e,t,r=he)=>{const n=[],o=[];for(let i=0;i<e.length;i++){const a=e[i];switch(a.token){case 1:const s=t.datasource.properties[a.text];if(!s){const e=Object.keys(t.datasource.properties).join(", ");throw new ve(`Unknown column @${a.text}, have: ${e}`,a.start)}const l=e[++i];if(2!==l?.token)throw new ve(`Missing operator for column @${a.text}`,a.start);if(!r[s.type][l.text])throw new ve(`Unknown operator for @${a.text}, have: ${Object.keys(r[s.type]).join(", ")}`,l.start);const c=e[++i];if(3!==c?.token)throw new ve(`Missing operand for column @${c.text}`,a.start);const u=r[s.type][l.text](c.text);n.push((e=>u(s.accessor(e))));break;case 0:o.push(a.text.trim());break;default:throw new Error(`Illegal token ${a.token}`)}}const i=o.join(" ").trim();if(i){const e=`/${t.regex?i:(a=i,a.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"))}/`+(t.caseSensitive?"":"i"),o=r[_e.String]["~="](e);n.push((e=>o(t.datasource.genericMatchStr(e))))}var a;return e=>{for(const t of n)if(!t(e))return!1;return!0}})((e=>{const t=[];let r=0;const n=(t,n)=>{let o="";const i=r;for(;r<e.length;){const t=e[r];if("\\"!==t){if(!n(t,r))break;o+=t,r++}else o+=e[++r],r++}return{token:t,text:o,start:i,length:r-i}};let o=0;for("@"===e[0]&&(o=1,r++);r<e.length;){const i=e[r];switch(o){case 0:const a=e.indexOf(" @",r);-1===a?t.push(n(0,(()=>!0))):(t.push(n(0,((e,t)=>t<=a))),r++,o=1);break;case 1:t.push(n(1,(e=>e>="A"&&e<="z"))),o=2;break;case 2:t.push(n(2,(e=>me.has(e)))),o=3;break;case 3:const s='"'!==i&&"'"!==i;s||r++,t.push(n(3,(e=>s?" "!==e:e!==i))),o=0,s||r++;break;default:throw new Error(`Illegal state ${o}`)}}return t})(e.input),e),r={selected:new Set,selectedAndParents:new Set,all:!e.input.trim()};for(const n of e.datasource.data.loaded)ye(e.datasource.data,t,n,r);return r})({input:l,regex:o,caseSensitive:a,datasource:t})),d(void 0)}catch(e){d(e.message)}}),[o,a,l,t]),h(Ue,null,h(Le,{value:l,placeholder:e,onChange:c,hasError:!!u,foot:h(m,null,h(Ve,{icon:pe,label:"Match Case",checked:a,onChange:s}),h(Ve,{icon:fe,label:"Use Regular Expression",checked:o,onChange:i}))}),u&&h("div",{className:qe.error},u),n)};var Ke=r(71),Qe={};Qe.styleTagTransform=Pe(),Qe.setAttributes=Te(),Qe.insert=Ce().bind(null,"head"),Qe.domAPI=ke(),Qe.insertStyleElement=Fe(),xe()(Ke.A,Qe);const Je=Ke.A&&Ke.A.locals?Ke.A.locals:void 0;var et=r(13),tt={};tt.styleTagTransform=Pe(),tt.setAttributes=Te(),tt.insert=Ce().bind(null,"head"),tt.domAPI=ke(),tt.insertStyleElement=Fe(),xe()(et.A,tt);const rt=et.A&&et.A.locals?et.A.locals:void 0;var nt=r(423);var ot=r(84);const it=({i:e,...t})=>h("span",{dangerouslySetInnerHTML:{__html:e},style:{color:"var(--vscode-icon-foreground)"},...t}),at=e=>{if(e.callFrame.url){if(!e.src?.source.path){let t=`${e.callFrame.url}`;return e.callFrame.lineNumber>=0&&(t+=`:${e.callFrame.lineNumber}`),t}return e.src.relativePath?`${e.src.relativePath}:${e.src.lineNumber}`:`${e.src.source.path}:${e.src.lineNumber}`}},st=new Intl.NumberFormat(void 0,{maximumFractionDigits:2,minimumFractionDigits:2});Symbol("unset");var lt=r(925),ct={};ct.styleTagTransform=Pe(),ct.setAttributes=Te(),ct.insert=Ce().bind(null,"head"),ct.domAPI=ke(),ct.insertStyleElement=Fe(),xe()(lt.A,ct);const ut=lt.A&&lt.A.locals?lt.A.locals:void 0,dt=({row:e,renderRow:t,style:r})=>h("div",{style:r},re((()=>t(e)),[e])),_t=(e,t)=>`position:absolute;left:0;right:0;height:${t}px;top:${e*t}px`,pt=(e,t,r)=>{r((r=>{if(r.get(e)===t){const t=new Map(r);return t.delete(e),t}return r}))},ft=({depth:e,position:t,node:r,dataProvider:n,promise:o,onLoadMore:i})=>{const[a,s]=Q(!!o);return n.eof?null:(J((()=>{o?o.finally((()=>s(!1))):s(!1)}),[o]),h("div",{className:ut.row,"data-row-id":`loading-${t}`,tabIndex:0,role:"treeitem","aria-posinset":t,"aria-level":e+1},h("div",{className:ut.footer,style:{paddingLeft:15*e}},a?"Loading...":h(m,null,h("a",{role:"button",onClick:()=>i(r,n)},"Load more rows")))))};var ht=r(974);const vt=e=>{const t=[e.id];for(let r=e.parent;r;r=r.parent)t.push(r.id);return t.join("-")},mt=({impact:e})=>h("div",{className:ut.impactBar,style:{transform:`scaleX(${e})`}}),gt=(e,t)=>t.selfTime-e.selfTime,yt=(e,t)=>t.aggregateTime-e.aggregateTime,bt=(()=>{const e=({containerRef:e=te(null),data:t,className:r,renderRow:n,rowHeight:o,overscanCount:i})=>{const[a,s]=Q([]),l=t.length*o,c=ne((()=>{const{current:r}=e;if(!r)return;const n=r.scrollTop,a=Math.max(0,Math.floor(n/o)-i),l=Math.min(t.length-1,a+Math.ceil(r.clientHeight/o)+2*i);s(function(e,t){const r=[];for(let n=e;n<t;n++)r.push(n);return r}(a,l+1))}),[t,o,i]);return((e,t,r)=>{J((()=>{if(!t)return;const n=new ResizeObserver((t=>{for(const r of t)e(r)}));return n.observe(t,r),()=>n.disconnect()}),[e,t,r])})(c,e.current),ee((()=>c()),[c]),h("div",{ref:e,className:r,style:{height:"100%",overflow:"auto"},onScroll:c},h("div",{style:{height:l,position:"relative"}},a.map((e=>h(dt,{renderRow:n,row:t[e],style:_t(e,o),key:e})))))};return({data:t,header:r,query:n,sortFn:o,row:i})=>{const a=te(new Map),[s,l]=Q(new Map),c=te(null),[u,d]=Q(void 0),[_,p]=Q(new Set),f=re((()=>{const e=o?t.loaded.slice().sort(o):t.loaded;for(const r of e)t.setSort(o),a.current.set(r,t);return e}),[t,o]),v=re((()=>{const e=f.filter((e=>n.selectedAndParents.has(e))).map((e=>({node:e,position:1,depth:0,provider:t})));for(let t=0;t<e.length;t++){const{node:r,depth:o,isFooter:i,entireSubtree:s}=e[t];if(!i&&_.has(r)){const i=a.current.get(r)?.getChildren(r);if(i){for(const e of i.loaded)a.current.set(e,i);const l=[];for(const e of i.loaded)(n.all||n.selectedAndParents.has(e)||s)&&l.push({node:e,position:t+1,depth:o+1,provider:i,entireSubtree:s||n.selected.has(e)});n.all&&l.push({isFooter:!0,node:r,position:t+l.length,depth:o+1,provider:i}),e.splice(t+1,0,...l)}}}return e}),[f,_,o,n,s]),g=ne(((e,t)=>{const r=a.current.get(t);let n;switch(e.key){case"Enter":case"Space":p(((e,t)=>{const r=new Set([...e]);return r.has(t)?r.delete(t):r.add(t),r})(_,t)),e.preventDefault();break;case"ArrowDown":n=v[v.findIndex((e=>e.node===t))+1]?.node;break;case"ArrowUp":n=v[v.findIndex((e=>e.node===t))-1]?.node;break;case"ArrowLeft":_.has(t)?p(((e,t)=>{const r=new Set([...e]);return r.delete(t),r})(_,t)):n=t.parent;break;case"ArrowRight":{const e=r?.getChildren(t);e?.length&&!_.has(t)?p(((e,t)=>{const r=new Set([...e,t]);return r.add(t),r})(_,t)):n=v.find((e=>e.node?.parent===t))?.node;break}case"Home":c.current&&(c.current.scrollTop=0),n=v[0]?.node;break;case"End":c.current&&(c.current.scrollTop=c.current.scrollHeight),n=v[v.length-1]?.node;break;case"*":{const e=new Set(_);if(u&&u.parent){const t=u?.parent,r=t&&a.current.get(t);for(const t of r?.getChildren(u).loaded||[])e.add(t);p(e)}break}}n&&(d(n),e.preventDefault())}),[v,_]);J((()=>{l((e=>{let t;for(const r of _){const n=a.current.get(r)?.getChildren(r);if(n&&!n.didReadUpTo(100)){t??=new Map(e);const o=n.read(100).then((()=>pt(r,o,l)));t.set(r,o)}}return t||e}))}),[_,o]),J((()=>c.current?.setAttribute("role","tree")),[c.current]),ee((()=>{const e=c.current;e&&u&&setTimeout((()=>{const t=e.querySelector(`[data-row-id="${(e=>{const t=[e.id];for(let r=e.parent;r;r=r.parent)t.push(r.id);return t.join("-")})(u)}"]`);t?.focus()}))}),[u]);const y=(e,t)=>{p((r=>{const n=new Set(r);return e?n.add(t):n.delete(t),n.size!==r.size?n:r}))},b=ne(((e,t)=>{const r=t,n=e;l((e=>{const t=new Map(e),o=r.read(r.loaded.length+100).then((()=>pt(n,o,l)));return t.set(n,o),t}))}),[]),x=ne((e=>e.isFooter?h(ft,{node:e.node,depth:e.depth,position:e.position,promise:s.get(e.node),dataProvider:e.provider,onLoadMore:b}):h(i,{onKeyDown:g,node:e.node,depth:e.depth,position:e.position,numChildren:e.provider.getChildren(e.node).length,expanded:_.has(e.node),onExpanded:y,onFocus:d})),[_,p,g]);return h(m,null,r,h(e,{containerRef:c,className:ut.rows,data:v,renderRow:x,rowHeight:25,overscanCount:30}))}})(),xt=({data:e,query:t})=>{const[r,n]=Q((()=>gt));return h(bt,{data:e,sortFn:r,query:t,header:h(wt,{sortFn:r,onChangeSort:n}),row:Nt})},wt=({sortFn:e,onChangeSort:t})=>h("div",{className:ut.row},h("div",{id:"self-time-header",className:He(ut.heading,ut.timing),"aria-sort":e===gt?"descending":void 0,onClick:ne((()=>t((()=>e===gt?void 0:gt))),[e])},e===gt&&h(it,{i:ot}),"Self Time"),h("div",{id:"total-time-header",className:He(ut.heading,ut.timing),"aria-sort":e===yt?"descending":void 0,onClick:ne((()=>t((()=>e===yt?void 0:yt))),[e])},e===yt&&h(it,{i:ot}),"Total Time"),h("div",{className:ut.heading},"File")),kt=({node:e,depth:t,numChildren:r,expanded:n,position:o,onKeyDown:i,onFocus:a,onClick:s,onExpanded:l,children:c,rowText:u,locationText:d,virtual:_=!d})=>{const p=ne((()=>l(!n,e)),[n,e]),f=ne((t=>{i?.(t,e)}),[i,e]),v=ne((()=>{a?.(e)}),[a,e]);let m=e;for(;m.parent;)m=m.parent;const g=h("span",{className:ut.expander},r>0?h(it,{i:n?ot:ht}):null);return h("div",{className:ut.row,style:{cursor:r>0?"pointer":"default"},"data-row-id":vt(e),onKeyDown:f,onFocus:v,onClick:p,tabIndex:0,role:"treeitem","aria-posinset":o,"aria-level":t+1,"aria-expanded":n},c,d?h("div",{className:ut.location,style:{marginLeft:15*t}},g," ",h("span",{className:ut.fn,style:{maxWidth:"80%"}},u),h("span",{className:ut.file},h("a",{href:"#",onClick:s},d))):h("div",{className:He(ut.location,_&&ut.virtual),style:{marginLeft:15*t}},g," ",h("span",{className:ut.fn},u)))},Nt=e=>{const{node:t}=e;let r=e.node;for(;r.parent;)r=r.parent;const n=oe($e),o=ne((e=>n.postMessage({type:"openDocument",callFrame:t.callFrame,location:t.src,toSide:e.altKey})),[n,t]);return h(kt,{...e,onClick:o,rowText:t.callFrame.functionName,locationText:at(t)},h("div",{className:ut.duration,"aria-labelledby":"self-time-header"},h(mt,{impact:t.selfTime/r.selfTime}),st.format(t.selfTime/1e3),"ms"),h("div",{className:ut.duration,"aria-labelledby":"total-time-header"},h(mt,{impact:t.aggregateTime/r.aggregateTime}),st.format(t.aggregateTime/1e3),"ms"))},Ct=(e=>{const t=W.root();for(const r of e.nodes)O(t,r,e),t.addNode(r);return t})(MODEL),St=Object.values(Ct.children),Tt=document.createElement("div");Tt.classList.add(rt.wrapper),document.body.appendChild(Tt),function(r,n,o){var i,a,s,l;t.__&&t.__(r,n),a=(i="function"==typeof o)?null:o&&o.__k||n.__k,s=[],l=[],P(n,r=(!i&&o||n).__k=h(m,null,[r]),a||c,c,void 0!==n.ownerSVGElement,!i&&o?[o]:a?null:n.firstChild?e.call(n.childNodes):null,s,!i&&o?o:a?a.__e:n.firstChild,i,l),I(s,r,l)}(h((({data:e,body:t,filterFooter:r})=>{const n=re(Ye,[]),[o,i]=Q(void 0),a=re((()=>r?h(r,{viewType:"jsProfileVisualizer.cpuprofile.flame",requireExtension:"ms-vscode.vscode-js-profile-flame"}):void 0),[r]);return h(m,null,h("div",{className:Je.filter},h(n,{data:e,onChange:i,placeholder:"Filter functions or files, or start a query()",foot:a})),h("div",{className:Je.rows},o&&h(t,{query:o,data:e.data})))}),{data:{data:ge.fromArray(St,(e=>Object.values(e.children))),genericMatchStr:e=>[e.callFrame.functionName,e.callFrame.url,e.src?.source.path??""].join(" "),properties:{function:{type:_e.String,accessor:e=>e.callFrame.functionName},url:{type:_e.String,accessor:e=>e.callFrame.url},path:{type:_e.String,accessor:e=>e.src?.relativePath??e.callFrame.url},line:{type:_e.Number,accessor:e=>e.src?e.src.lineNumber:e.callFrame.lineNumber},selfTime:{type:_e.Number,accessor:e=>e.selfTime},totalTime:{type:_e.Number,accessor:e=>e.aggregateTime},id:{type:_e.Number,accessor:e=>e.id}}},body:({query:e,data:t})=>h(xt,{query:e,data:t}),filterFooter:({viewType:e,requireExtension:t})=>{const r=oe($e),n=ne((()=>r.postMessage({type:"reopenWith",viewType:e,requireExtension:t})),[r]);return h(Ve,{icon:nt,label:"Show flame graph",checked:!1,onClick:n})}}),Tt)})()})();