<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
	<displayName />
	<description />
	<resources>
		<stringTable>
			<string id="Application">Visual Studio Code</string>
			<string id="Supported_1_101">Visual Studio Code &gt;= 1.101</string>
			<string id="Supported_1_67">Visual Studio Code &gt;= 1.67</string>
			<string id="Supported_1_96">Visual Studio Code &gt;= 1.96</string>
			<string id="Supported_1_99">Visual Studio Code &gt;= 1.99</string>
			<string id="Category_extensionsConfigurationTitle">Extensões</string>
			<string id="Category_interactiveSessionConfigurationTitle">Chat</string>
			<string id="Category_updateConfigurationTitle">Atualizar</string>
			<string id="Category_telemetryConfigurationTitle">Telemetria</string>
			<string id="ExtensionGalleryServiceUrl">ExtensionGalleryServiceUrl</string>
			<string id="ExtensionGalleryServiceUrl_extensions_gallery_serviceUrl">Configurar a URL do serviço do Marketplace ao qual se conectar</string>
			<string id="ChatToolsAutoApprove">ChatToolsAutoApprove</string>
			<string id="ChatToolsAutoApprove_chat_tools_autoApprove_description">Controla se o uso da ferramenta deve ser aprovado automaticamente. Permite que todas as ferramentas sejam executadas automaticamente sem a confirmação do usuário, substituindo as configurações específicas da ferramenta, como a aprovação automática do terminal. Use com cuidado: examine cuidadosamente as ferramentas selecionadas e tenha cuidado extra com possíveis fontes de injeção de solicitação!</string>
			<string id="ChatMCP">ChatMCP</string>
			<string id="ChatMCP_chat_mcp_enabled">Habilita a integração com servidores de Protocolo de Contexto de Modelo para fornecer ferramentas e funcionalidades adicionais.</string>
			<string id="ChatAgentExtensionTools">ChatAgentExtensionTools</string>
			<string id="ChatAgentExtensionTools_chat_extensionToolsPolicy">Habilitar o uso de ferramentas contribuídas por extensões de terceiros.</string>
			<string id="ChatAgentMode">ChatAgentMode</string>
			<string id="ChatAgentMode_chat_agent_enabled_description">Habilitar o modo de agente para {0}. Quando essa opção está habilitada, o modo de agente pode ser ativado por meio da lista suspensa na exibição.</string>
			<string id="McpGalleryServiceUrl">McpGalleryServiceUrl</string>
			<string id="McpGalleryServiceUrl_mcp_gallery_serviceUrl">Configurar URL de serviço da galeria do MCP para se conectar a</string>
			<string id="ChatPromptFiles">ChatPromptFiles</string>
			<string id="ChatPromptFiles_chat_promptFiles_policy">Habilita arquivos de solicitação e instruções reutilizáveis em sessões de chat, edições e chat embutidos.</string>
			<string id="UpdateMode">UpdateMode</string>
			<string id="UpdateMode_updateMode">Configure o recebimento de atualizações automáticas. Exige uma reinicialização após a alteração. As atualizações são obtidas de um serviço online da Microsoft.</string>
			<string id="UpdateMode_none">Desabilitar atualizações.</string>
			<string id="UpdateMode_manual">Desabilitar as verificações automáticas de atualização em segundo plano. As atualizações estarão disponíveis se você verificar as atualizações manualmente.</string>
			<string id="UpdateMode_start">Verificar se há atualizações somente na inicialização. Desabilitar as verificações automáticas de atualização em segundo plano.</string>
			<string id="UpdateMode_default">Habilitar verificações de atualização automática. O código verificará se há atualizações automaticamente e periodicamente.</string>
			<string id="TelemetryLevel">TelemetryLevel</string>
			<string id="TelemetryLevel_telemetry_telemetryLevel_policyDescription">Controla o nível de telemetria.</string>
			<string id="EnableFeedback">EnableFeedback</string>
			<string id="EnableFeedback_telemetry_feedback_enabled">Habilitar mecanismos de comentários, como o repórter de problemas, pesquisas e opções de comentários em recursos como o Copilot Chat.</string>
			<string id="AllowedExtensions">AllowedExtensions</string>
			<string id="AllowedExtensions_extensions_allowed_policy">Especifique uma lista de extensões que têm permissão para ser usadas. Isso ajuda a manter um ambiente de desenvolvimento seguro e consistente ao restringir o uso de extensões não autorizadas. Mais informações: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
		</stringTable>
		<presentationTable>
			<presentation id="ExtensionGalleryServiceUrl"><textBox refId="ExtensionGalleryServiceUrl"><label>ExtensionGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatToolsAutoApprove"><checkBox refId="ChatToolsAutoApprove">ChatToolsAutoApprove</checkBox></presentation>
			<presentation id="ChatMCP"><checkBox refId="ChatMCP">ChatMCP</checkBox></presentation>
			<presentation id="ChatAgentExtensionTools"><checkBox refId="ChatAgentExtensionTools">ChatAgentExtensionTools</checkBox></presentation>
			<presentation id="ChatAgentMode"><checkBox refId="ChatAgentMode">ChatAgentMode</checkBox></presentation>
			<presentation id="McpGalleryServiceUrl"><textBox refId="McpGalleryServiceUrl"><label>McpGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatPromptFiles"><checkBox refId="ChatPromptFiles">ChatPromptFiles</checkBox></presentation>
			<presentation id="UpdateMode"><dropdownList refId="UpdateMode" /></presentation>
			<presentation id="TelemetryLevel"><textBox refId="TelemetryLevel"><label>TelemetryLevel:</label></textBox></presentation>
			<presentation id="EnableFeedback"><checkBox refId="EnableFeedback">EnableFeedback</checkBox></presentation>
			<presentation id="AllowedExtensions"><multiTextBox refId="AllowedExtensions" /></presentation>
		</presentationTable>
	</resources>
</policyDefinitionResources>
