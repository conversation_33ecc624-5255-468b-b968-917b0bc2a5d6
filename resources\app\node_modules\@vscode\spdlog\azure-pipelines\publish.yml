name: $(Date:yyyyMMdd)$(Rev:.r)

trigger:
  branches:
    include:
      - main
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishPackage
    displayName: 🚀 Publish node-spdlog
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    npmPackages:
      - name: node-spdlog

        buildSteps:
          - script: |
              git submodule update --init
              npm ci
            displayName: Install dependencies

        testPlatforms:
          - name: Linux
            nodeVersions:
              - 18.x
          - name: MacOS
            nodeVersions:
              - 18.x
          - name: Windows
            nodeVersions:
              - 18.x

        testSteps:
          - script: |
              git submodule update --init
              npm ci
            displayName: Install dependencies

          - script: npm run test
            displayName: Compile & test npm package

        apiScanSoftwareName: 'vscode-spdlog'
        apiScanSoftwareVersion: '0'
        publishPackage: ${{ parameters.publishPackage }}