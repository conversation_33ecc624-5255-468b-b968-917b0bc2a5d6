{"compilerOptions": {"sourceMap": true, "inlineSources": true, "noImplicitAny": true, "module": "es6", "moduleResolution": "node", "target": "es3", "forceConsistentCasingInFileNames": true, "importHelpers": true, "noEmitHelpers": true, "skipLibCheck": false, "alwaysStrict": true, "skipDefaultLibCheck": true, "declaration": true, "declarationDir": "shared/AppInsightsCore/types", "outDir": "./dist-esm", "rootDir": "shared/AppInsightsCore/src", "suppressImplicitAnyIndexErrors": true, "allowSyntheticDefaultImports": true}, "include": ["./src/applicationinsights-core-js.ts", "./src/JavaScriptSDK/**/*.ts", "./src/JavaScriptSDK.Enums/**/*.ts", "./src/JavaScriptSDK.Interfaces/**/*.ts"], "exclude": ["node_modules/"]}