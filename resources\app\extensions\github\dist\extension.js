/*! For license information please see extension.js.LICENSE.txt */
import{createRequire as e}from"node:module";import*as t from"vscode";var n,r,i={956:(e,t,n)=>{n.r(t),n.d(t,{ActiveStatus:()=>k.f,AppInsightsCore:()=>h,BaseTelemetryPlugin:()=>E.s,DiagnosticLogger:()=>u.wq,EventLatency:()=>g,EventPersistence:()=>b,EventPropertyType:()=>y,EventsDiscardedReason:()=>T.x,FullVersionString:()=>f.xE,InternalAppInsightsCore:()=>c._,LoggingSeverity:()=>P,MinChannelPriorty:()=>S,NotificationManager:()=>C.h,PerfEvent:()=>a.Q6,PerfManager:()=>a.NS,ProcessTelemetryContext:()=>_.W0,SenderPostManager:()=>z.v,TraceLevel:()=>w,Undefined:()=>U.bA,ValueKind:()=>m,ValueSanitizer:()=>I,Version:()=>f.Rx,_InternalLogMessage:()=>u.WD,__getRegisteredEvents:()=>D.El,_appendHeader:()=>x.LU,_getAllResponseHeaders:()=>x.w3,_logInternalMessage:()=>u.Oc,_testHookMaxUnloadHooksCb:()=>X.d,_throwInternal:()=>u.ZP,_warnToConsole:()=>u.OG,addEventHandler:()=>D.So,addEventListeners:()=>D.lQ,addPageHideEventListener:()=>D.Fc,addPageShowEventListener:()=>D.oS,addPageUnloadEventListener:()=>D.ee,areCookiesSupported:()=>N.gi,arrForEach:()=>o.Iuo,arrIndexOf:()=>o.rDm,arrMap:()=>o.W$7,arrReduce:()=>o.KTd,attachEvent:()=>D.Q3,blockDynamicConversion:()=>M.V9,convertAllHeadersToMap:()=>x.IL,cookieAvailable:()=>N.gi,createCookieMgr:()=>N.xN,createDynamicConfig:()=>s.e,createEnumStyle:()=>p.H,createGuid:()=>f.gj,createProcessTelemetryContext:()=>_.i8,createTraceParent:()=>$.wk,createUniqueNamespace:()=>H.Z,createUnloadHandlerContainer:()=>O.P,dateNow:()=>o.f0d,detachEvent:()=>D.Ym,disallowsSameSiteNone:()=>N.It,doPerf:()=>a.r2,dumpObj:()=>o.mmD,eventOff:()=>D.ML,eventOn:()=>D.mB,extend:()=>f.X$,findW3cTraceParent:()=>$.ef,forceDynamicConversion:()=>M.Hf,formatErrorMessageXdr:()=>x.HU,formatErrorMessageXhr:()=>x.r4,formatTraceParent:()=>$.L0,generateW3CId:()=>R.cL,getCommonSchemaMetaData:()=>f.Go,getConsole:()=>L.U5,getCookieValue:()=>f.UM,getCrypto:()=>L.MY,getDocument:()=>o.YEm,getDynamicConfigHandler:()=>M.QA,getExceptionName:()=>x.lL,getFieldValueType:()=>f.cq,getGlobal:()=>o.mS$,getGlobalInst:()=>o.zS2,getHistory:()=>o.JKf,getIEVersion:()=>L.L0,getISOString:()=>x._u,getJSON:()=>L.hm,getLocation:()=>L.g$,getMsCrypto:()=>L.iN,getNavigator:()=>o.w3n,getPerformance:()=>o.FJj,getResponseText:()=>x.Lo,getSetValue:()=>x.c2,getTenantId:()=>f.EO,getTime:()=>f.WB,getWindow:()=>o.zkX,hasDocument:()=>o.Wtk,hasHistory:()=>o.twz,hasJSON:()=>L.Z,hasNavigator:()=>o.w9M,hasOwnProperty:()=>o.v0u,hasWindow:()=>o.Vdv,isArray:()=>o.cyL,isArrayValid:()=>f.wJ,isBeaconsSupported:()=>L.Uf,isBoolean:()=>o.Lmq,isChromium:()=>f.F2,isDate:()=>o.$PY,isDocumentObjectAvailable:()=>f.g8,isError:()=>o.bJ7,isFetchSupported:()=>L.R7,isFunction:()=>o.Tnt,isGreaterThanZero:()=>f.ei,isIE:()=>L.lT,isLatency:()=>f.Hh,isNotTruthy:()=>o.woc,isNullOrUndefined:()=>o.hXl,isNumber:()=>o.EtT,isObject:()=>o.Gvm,isReactNative:()=>L.lV,isSampledFlag:()=>$.N7,isString:()=>o.KgX,isTruthy:()=>o.zzB,isTypeof:()=>o.Edw,isUint8ArrayAvailable:()=>f.h3,isUndefined:()=>o.b07,isValidSpanId:()=>$.wN,isValidTraceId:()=>$.hX,isValidTraceParent:()=>$.mJ,isValueAssigned:()=>f.yD,isValueKind:()=>f.m0,isWindowObjectAvailable:()=>f.P$,isXhrSupported:()=>L.xk,mergeEvtNamespace:()=>D.Hm,newGuid:()=>R.aq,newId:()=>A.Si,normalizeJsName:()=>x.cH,objDefineAccessors:()=>o.raO,objForEachKey:()=>o.zav,objFreeze:()=>o.N6t,objKeys:()=>o.cGk,objSeal:()=>o.jsL,onConfigChange:()=>s.a,openXhr:()=>f.H$,optimizeObject:()=>x.hW,parseResponse:()=>F.x,parseTraceParent:()=>$.ZI,perfNow:()=>o.UUD,prependTransports:()=>x.jL,proxyAssign:()=>x.qz,proxyFunctionAs:()=>x.RF,proxyFunctions:()=>x.o$,random32:()=>A.VN,randomValue:()=>A.Z1,removeEventHandler:()=>D.zh,removeEventListeners:()=>D.Wg,removePageHideEventListener:()=>D.sq,removePageShowEventListener:()=>D.vF,removePageUnloadEventListener:()=>D.Ds,safeGetCookieMgr:()=>N.um,safeGetLogger:()=>u.y0,sanitizeProperty:()=>f.TC,setEnableEnvMocks:()=>L.cU,setProcessTelemetryTimings:()=>f.u9,setValue:()=>x.KY,strContains:()=>x.Ju,strEndsWith:()=>o.Cv9,strFunction:()=>U.hW,strObject:()=>U._1,strPrototype:()=>U.vR,strStartsWith:()=>o.tGl,strTrim:()=>o.EHq,strUndefined:()=>U.bA,throwError:()=>o.$8,toISOString:()=>x._u,useXDomainRequest:()=>L.PV});var r=n(659),i=n(8279),o=n(269),a=n(8156),s=n(9749),u=n(3775),c=n(2774),l=n(937),f=n(4822),d=n(1739),v=(0,o.ZHX)({endpointUrl:l.S,propertyStorageOverride:{isVal:function(e){return!e||e.getProperty&&e.setProperty||(0,o.$8)("Invalid property storage override passed."),!0}}}),h=function(e){function t(){var n=e.call(this)||this;return(0,i.A)(t,n,(function(e,t){e[d.mE]=function(n,r,i,c){(0,a.r2)(e,(function(){return"AppInsightsCore.initialize"}),(function(){try{t[d.mE]((0,s.e)(n,v,i||e[d.Uw],!1).cfg,r,i,c)}catch(t){var a=e[d.Uw],l=(0,o.mmD)(t);-1!==l[d.Sj]("channels")&&(l+="\n - Channels must be provided through config.channels only!"),(0,u.ZP)(a,1,514,"SDK Initialization Failed - no telemetry will be sent: "+l)}}),(function(){return{config:n,extensions:r,logger:i,notificationManager:c}}))},e.track=function(n){(0,a.r2)(e,(function(){return"AppInsightsCore.track"}),(function(){var r=n;if(r){r[d.dg]=r[d.dg]||{},r[d.dg].trackStart=(0,f.WB)(),(0,f.Hh)(r.latency)||(r.latency=1);var i=r.ext=r.ext||{};i.sdk=i.sdk||{},i.sdk.ver=f.xE;var o=r.baseData=r.baseData||{};o[l._0]=o[l._0]||{};var a=o[l._0];a[l.hj]=a[l.hj]||e.pluginVersionString||l.m5}t.track(r)}),(function(){return{item:n}}),!n.sync)},e[d.h4]=function(e){return t[d.h4](e||"InternalLog")}})),n}return(0,r.qU)(t,e),t.__ieDyn=1,t}(c._),p=n(4282),m=(0,p.H)({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,Pii_IPv6ScrubLastHextets:14,Pii_DropValue:15,CustomerContent_GenericContent:32}),g=(0,p.H)({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}),y=(0,p.H)({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),b=(0,p.H)({Normal:1,Critical:2}),w=(0,p.H)({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),I=function(){function e(e){var t=this,n={},r=[],i=[];function a(e,t){var a,s=n[e];if(s&&(a=s[t]),!a&&null!==a){if((0,o.KgX)(e)&&(0,o.KgX)(t))if(i[d.oI]>0){for(var u=0;u<i[d.oI];u++)if(i[u][d.hF](e,t)){a={canHandle:!0,fieldHandler:i[u]};break}}else 0===r[d.oI]&&(a={canHandle:!0});if(!a&&null!==a)for(a=null,u=0;u<r[d.oI];u++)if(r[u][d.hF](e,t)){a={canHandle:!0,handler:r[u],fieldHandler:null};break}s||(s=n[e]={}),s[t]=a}return a}function s(e,t,n,r,i,a){if(e.handler)return e.handler.property(t,n,i,a);if(!(0,o.hXl)(i[d.QV])){if(!(4096&~r&&(0,f.m0)(i[d.QV])))return null;i[d.pF]=i[d.pF].toString()}return c(e.fieldHandler,t,n,r,i)}function u(e,t,n){return(0,f.yD)(n)?{value:n}:null}function c(e,n,r,i,a){if(a&&e){var s=e.getSanitizer(n,r,i,a[d.QV],a.propertyType);if(s)if(4===i){var l={},v=a[d.pF];(0,o.zav)(v,(function(t,i){var o=n+"."+r;if((0,f.yD)(i)){var a=u(0,0,i);(a=c(e,o,t,(0,f.cq)(i),a))&&(l[t]=a[d.pF])}})),a[d.pF]=l}else{var h={path:n,name:r,type:i,prop:a,sanitizer:t};a=s.call(t,h)}}return a}e&&i.push(e),t.clearCache=function(){n={}},t.addSanitizer=function(e){e&&((0,o.Nq2)(r,e)||r.push(e),n={})},t.addFieldSanitizer=function(e){e&&((0,o.Nq2)(i,e)||i.push(e),n={})},t[d.Rl]=function(e){if(e){var t=(0,o.rDm)(r,e);-1!==t&&(r.splice(t,1),n={}),(0,o.Iuo)(r,(function(t){t&&t[d.Rl]&&t[d.Rl](e)}))}},t[d.Mr]=function(e){if(e){var t=(0,o.rDm)(i,e);-1!==t&&(i.splice(t,1),n={}),(0,o.Iuo)(r,(function(t){t&&t[d.Mr]&&t[d.Mr](e)}))}},t.isEmpty=function(){return(0,o.R3R)(r)+(0,o.R3R)(i)===0},t[d.hF]=function(e,t){var n=a(e,t);return!!n&&n[d.nw]},t[d.pF]=function(e,t,n,r){var i=a(e,t);if(i&&i[d.nw]){if(!i||!i[d.nw])return null;if(i.handler)return i.handler[d.pF](e,t,n,r);if(!(0,o.KgX)(t)||(0,o.hXl)(n)||n===l.m5)return null;var c=null,v=(0,f.cq)(n);if(8192&~v)1!==v&&2!==v&&3!==v&&4096&~v?4===v&&(c=u(0,0,r?JSON.stringify(n):n)):c=u(0,0,n);else{var h=-8193&v;if(c=n,!(0,f.yD)(c[d.pF])||1!==h&&2!==h&&3!==h&&4096&~h)return null}if(c)return s(i,e,t,v,c,r)}return null},t.property=function(e,t,n,r){var i=a(e,t);if(!i||!i[d.nw])return null;if(!(0,o.KgX)(t)||(0,o.hXl)(n)||!(0,f.yD)(n[d.pF]))return null;var u=(0,f.cq)(n[d.pF]);return 0===u?null:s(i,e,t,u,n,r)}}return e.getFieldType=f.cq,e}(),S=100,P=(0,p.H)({DISABLED:0,CRITICAL:1,WARNING:2,DEBUG:3}),C=n(1356),E=n(8257),_=n(2317),T=n(3662),k=n(4875),D=n(6149),x=n(3673),R=n(9882),A=n(6535),L=n(7292),U=n(5664),N=n(5034),H=n(4276),O=n(836),$=n(1864),M=n(9147),z=n(856),F=n(1190),X=n(8969)},937:(e,t,n)=>{n.d(t,{S:()=>i,_0:()=>a,hj:()=>o,m5:()=>r});var r="",i="https://browser.events.data.microsoft.com/OneCollector/1.0/",o="version",a="properties"},4822:(e,t,n)=>{n.d(t,{EO:()=>I,F2:()=>U,Go:()=>E,H$:()=>N,Hh:()=>P,P$:()=>b,Rx:()=>l,TC:()=>C,UM:()=>_,WB:()=>D,X$:()=>k,cq:()=>L,ei:()=>H,g8:()=>y,gj:()=>T,h3:()=>S,m0:()=>x,u9:()=>A,wJ:()=>R,xE:()=>f,yD:()=>w});var r,i=n(269),o=n(7292),a=n(9882),s=n(5664),u=n(937),c=n(1739),l="4.3.4",f="1DS-Web-JS-"+l,d=s.Wy.hasOwnProperty,v="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",h="withCredentials",p="timeout",m=((r={})[0]=0,r[2]=6,r[1]=1,r[3]=7,r[4098]=6,r[4097]=1,r[4099]=7,r),g=null,y=(0,i.Wtk)(),b=(0,i.Vdv)();function w(e){return!(e===u.m5||(0,i.hXl)(e))}function I(e){if(e){var t=(0,i.HzD)(e,"-");if(t>-1)return(0,i.ZWZ)(e,t)}return u.m5}function S(){return null===g&&(g=!(0,i.b07)(Uint8Array)&&!function(){var e=(0,i.w3n)();if(!(0,i.b07)(e)&&e.userAgent){var t=e.userAgent.toLowerCase();if((t[c.Sj]("safari")>=0||t[c.Sj]("firefox")>=0)&&t[c.Sj]("chrome")<0)return!0}return!1}()&&!(0,o.lV)()),g}function P(e){return!!(e&&(0,i.EtT)(e)&&e>=1&&e<=4)}function C(e,t,n){if(!t&&!w(t)||"string"!=typeof e)return null;var r=typeof t;if("string"===r||"number"===r||"boolean"===r||(0,i.cyL)(t))t={value:t};else if("object"!==r||d.call(t,"value")){if((0,i.hXl)(t[c.pF])||t[c.pF]===u.m5||!(0,i.KgX)(t[c.pF])&&!(0,i.EtT)(t[c.pF])&&!(0,i.Lmq)(t[c.pF])&&!(0,i.cyL)(t[c.pF]))return null}else t={value:n?JSON.stringify(t):t};if((0,i.cyL)(t[c.pF])&&!R(t[c.pF]))return null;if(!(0,i.hXl)(t[c.QV])){if((0,i.cyL)(t[c.pF])||!x(t[c.QV]))return null;t[c.pF]=t[c.pF].toString()}return t}function E(e,t,n){var r=-1;if(!(0,i.b07)(e))if(t>0&&(32===t?r=8192:t<=13&&(r=t<<5)),function(e){return e>=0&&e<=9}(n))-1===r&&(r=0),r|=n;else{var o=m[L(e)]||-1;-1!==r&&-1!==o?r|=o:6===o&&(r=o)}return r}function _(e,t,n){var r;return void 0===n&&(n=!0),e&&(r=e.get(t),n&&r&&decodeURIComponent&&(r=decodeURIComponent(r))),r||u.m5}function T(e){void 0===e&&(e="D");var t=(0,a.aq)();return"B"===e?t="{"+t+"}":"P"===e?t="("+t+")":"N"===e&&(t=t.replace(/-/g,u.m5)),t}function k(e,t,n,r,o){var a={},s=!1,u=0,l=arguments[c.oI],f=arguments;for((0,i.Lmq)(f[0])&&(s=f[0],u++);u<l;u++)e=f[u],(0,i.zav)(e,(function(e,t){s&&t&&(0,i.Gvm)(t)?(0,i.cyL)(t)?(a[e]=a[e]||[],(0,i.Iuo)(t,(function(t,n){t&&(0,i.Gvm)(t)?a[e][n]=k(!0,a[e][n],t):a[e][n]=t}))):a[e]=k(!0,a[e],t):a[e]=t}));return a}var D=i.UUD;function x(e){return 0===e||e>0&&e<=13||32===e}function R(e){return e[c.oI]>0}function A(e,t){var n=e;n[c.dg]=n[c.dg]||{},n[c.dg][c.Jg]=n[c.dg][c.Jg]||{},n[c.dg][c.Jg][t]=D()}function L(e){var t=0;if(null!=e){var n=typeof e;"string"===n?t=1:"number"===n?t=2:"boolean"===n?t=3:n===s._1&&(t=4,(0,i.cyL)(e)?(t=4096,e[c.oI]>0&&(t|=L(e[0]))):d.call(e,"value")&&(t=8192|L(e[c.pF])))}return t}function U(){return!!(0,i.zS2)("chrome")}function N(e,t,n,r,i,o){function a(e,t,n){try{e[t]=n}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var s=new XMLHttpRequest;return r&&a(s,v,r),n&&a(s,h,n),s.open(e,t,!i),n&&a(s,h,n),!i&&o&&a(s,p,o),s}function H(e){return e>0}},1739:(e,t,n)=>{n.d(t,{Jg:()=>f,Mr:()=>h,QV:()=>c,Rl:()=>v,Sj:()=>o,Uw:()=>i,dg:()=>a,h4:()=>s,hF:()=>d,mE:()=>r,nw:()=>p,oI:()=>l,pF:()=>u});var r="initialize",i="logger",o="indexOf",a="timings",s="pollInternalLogs",u="value",c="kind",l="length",f="processTelemetryStart",d="handleField",v="rmSanitizer",h="rmFieldSanitizer",p="canHandle"},8916:(e,t,n)=>{n.r(t),n.d(t,{BE_PROFILE:()=>o,NRT_PROFILE:()=>i,PostChannel:()=>It,RT_PROFILE:()=>r});var r="REAL_TIME",i="NEAR_REAL_TIME",o="BEST_EFFORT",a=n(659),s=n(8279),u=n(4822),c=n(269),l=n(8156),f=n(6149),d=n(4276),v=n(9749),h=n(2317),p=n(3673),m=n(3662),g=n(3775),y=n(8257),b=n(8205),w="",I="drop",S="requeue",P="no-cache, no-store",C="application/x-json-stream",E="cache-control",_="content-type",T="client-version",k="client-id",D="time-delta-to-apply-millis",x="upload-time",R="apikey",A="AuthMsaDeviceTicket",L="WebAuthToken",U="AuthXToken",N="msfpc",H="trace",O="user",$="allowRequestSending",M="firstRequestSent",z="shouldAddClockSkewHeaders",F="getClockSkewHeaderValue",X="setClockSkew",q="length",B="concat",G="iKey",K="count",V="events",j="push",Z="split",W="splice",Q="toLowerCase",J="hdrs",Y="useHdrs",ee="initialize",te="setTimeoutOverride",ne="clearTimeoutOverride",re="overrideEndpointUrl",ie="avoidOptions",oe="enableCompoundKey",ae="disableXhrSync",se="disableFetchKeepAlive",ue="useSendBeacon",ce="fetchCredentials",le="alwaysUseXhrOverride",fe="serializeOfflineEvt",de="getOfflineRequestDetails",ve="createPayload",he="createOneDSPayload",pe="payloadBlob",me="headers",ge="_thePayload",ye="urlString",be="batches",we="sendType",Ie="addHeader",Se="canSendRequest",Pe="sendQueuedRequests",Ce="isCompletelyIdle",Ee="setUnloading",_e="resume",Te="sendSynchronousBatch",ke="_transport",De="getWParam",xe="isBeacon",Re="timings",Ae="isTeardown",Le="isSync",Ue="data",Ne="_sendReason",He="setKillSwitchTenants",Oe="_backOffTransmission",$e="identifier",Me="eventsLimitInMem",ze="autoFlushEventsLimit",Fe="baseData",Xe="sendAttempt",qe="latency",Be="sync";function Ge(e){var t=(e.ext||{}).intweb;return t&&(0,u.yD)(t[N])?t[N]:null}function Ke(e){for(var t=null,n=0;null===t&&n<e[q];n++)t=Ge(e[n]);return t}var Ve=function(){function e(t,n){var r=n?[][B](n):[],i=this,o=Ke(r);i[G]=function(){return t},i.Msfpc=function(){return o||w},i[K]=function(){return r[q]},i[V]=function(){return r},i.addEvent=function(e){return!!e&&(r[j](e),o||(o=Ge(e)),!0)},i[Z]=function(n,i){var a;if(n<r[q]){var s=r[q]-n;(0,c.hXl)(i)||(s=i<s?i:s),a=r[W](n,s),o=Ke(r)}return new e(t,a)}}return e.create=function(t,n){return new e(t,n)},e}(),je=n(7292),Ze=n(856),We=n(5664),Qe=function(){function e(){var t=!0,n=!0,r=!0,i="use-collector-delta",o=!1;(0,s.A)(e,this,(function(e){e[$]=function(){return t},e[M]=function(){r&&(r=!1,o||(t=!1))},e[z]=function(){return n},e[F]=function(){return i},e[X]=function(e){o||(e?(i=e,n=!0,o=!0):n=!1,t=!0)}}))}return e.__ieDyn=1,e}(),Je=function(){function e(){var t={};(0,s.A)(e,this,(function(e){e[He]=function(e,n){if(e&&n)try{var r=(a=e[Z](","),s=[],a&&(0,c.Iuo)(a,(function(e){s[j]((0,c.EHq)(e))})),s);if("this-request-only"===n)return r;for(var i=1e3*parseInt(n,10),o=0;o<r[q];++o)t[r[o]]=(0,c.f0d)()+i}catch(e){return[]}var a,s;return[]},e.isTenantKilled=function(e){var n=t,r=(0,c.EHq)(e);return void 0!==n[r]&&n[r]>(0,c.f0d)()||(delete n[r],!1)}}))}return e.__ieDyn=1,e}();function Ye(e){var t,n=Math.floor(1200*Math.random())+2400;return t=Math.pow(2,e)*n,Math.min(t,6e5)}var et,tt=2e6,nt=Math.min(tt,65e3),rt="metadata",it="f",ot=/\./,at=function(){function e(t,n,r,i,o,a){var f="data",d="baseData",v=!!i,h=!0,p=n,m={},g=!!a,y=o||u.Go;(0,s.A)(e,this,(function(e){function n(e,t,i,o,a,s,l){(0,c.zav)(e,(function(e,f){var d=null;if(f||(0,u.yD)(f)){var h=i,g=e,y=a,b=t;if(v&&!o&&ot.test(e)){var w=e.split("."),I=w.length;if(I>1){y&&(y=y.slice());for(var S=0;S<I-1;S++){var P=w[S];b=b[P]=b[P]||{},h+="."+P,y&&y.push(P)}g=w[I-1]}}var C=o&&function(e,t){var n=m[e];return void 0===n&&(e.length>=7&&(n=(0,c.tGl)(e,"ext.metadata")||(0,c.tGl)(e,"ext.web")),m[e]=n),n}(h);if(d=!C&&p&&p.handleField(h,g)?p.value(h,g,f,r):(0,u.TC)(g,f,r)){var E=d.value;if(b[g]=E,s&&s(y,g,d),l&&"object"==typeof E&&!(0,c.cyL)(E)){var _=y;_&&(_=_.slice()).push(g),n(f,E,h+"."+g,o,_,s,l)}}}}))}e.createPayload=function(e,t,n,r,i,o){return{apiKeys:[],payloadBlob:w,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:e,isTeardown:t,isSync:n,isBeacon:r,sendType:o,sendReason:i}},e.appendPayload=function(n,r,i){var o=n&&r&&!n.overflow;return o&&(0,l.r2)(t,(function(){return"Serializer:appendPayload"}),(function(){for(var t=r.events(),o=n.payloadBlob,a=n.numEvents,s=!1,u=[],l=[],f=n.isBeacon,d=f?65e3:3984588,v=f?nt:tt,h=0,p=0;h<t.length;){var m=t[h];if(m){if(a>=i){n.overflow=r.split(h);break}var g=e.getEventBlob(m);if(g&&g.length<=v){var y=g.length;if(o.length+y>d){n.overflow=r.split(h);break}o&&(o+="\n"),o+=g,++p>20&&((0,c.hKY)(o,0,1),p=0),s=!0,a++}else g?u.push(m):l.push(m),t.splice(h,1),h--}h++}if(u.length>0&&n.sizeExceed.push(Ve.create(r.iKey(),u)),l.length>0&&n.failedEvts.push(Ve.create(r.iKey(),l)),s){n.batches.push(r),n.payloadBlob=o,n.numEvents=a;var b=r.iKey();-1===(0,c.rDm)(n.apiKeys,b)&&n.apiKeys.push(b)}}),(function(){return{payload:n,theBatch:{iKey:r.iKey(),evts:r.events()},max:i}})),o},e.getEventBlob=function(e){try{return(0,l.r2)(t,(function(){return"Serializer.getEventBlob"}),(function(){var t={};t.name=e.name,t.time=e.time,t.ver=e.ver,t.iKey="o:"+(0,u.EO)(e.iKey);var r,i={};g||(r=function(e,t,n){!function(e,t,n,r,i){if(i&&t){var o=e(i.value,i.kind,i.propertyType);if(o>-1){var a=t[rt];a||(a=t[rt]={f:{}});var s=a[it];if(s||(s=a[it]={}),n)for(var u=0;u<n.length;u++){var l=n[u];s[l]||(s[l]={f:{}});var f=s[l][it];f||(f=s[l][it]={}),s=f}s=s[r]={},(0,c.cyL)(i.value)?s.a={t:o}:s.t=o}}}(y,i,e,t,n)});var o=e.ext;o&&(t.ext=i,(0,c.zav)(o,(function(e,t){n(t,i[e]={},"ext."+e,!0,null,null,!0)})));var a=t[f]={};a.baseType=e.baseType;var s=a[d]={};return n(e.baseData,s,d,!1,[d],r,h),n(e.data,a,f,!1,[],r,h),JSON.stringify(t)}),(function(){return{item:e}}))}catch(e){return null}}}))}return e.__ieDyn=1,e}();function st(e,t){return{set:function(n,r){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];return(0,c.vKV)([e,t],n,r,i)}}}var ut="sendAttempt",ct="?cors=true&"+_[Q]()+"="+C,lt=((et={})[1]=S,et[100]=S,et[200]="sent",et[8004]=I,et[8003]=I,et),ft={},dt={};function vt(e,t,n){ft[e]=t,!1!==n&&(dt[t]=e)}function ht(e,t){var n=!1;if(e&&t){var r=(0,c.cGk)(e);if(r&&r[q]>0)for(var i=t[Q](),o=0;o<r[q];o++){var a=r[o];if(a&&(0,c.v0u)(t,a)&&a[Q]()===i){n=!0;break}}}return n}function pt(e,t,n,r){t&&n&&n[q]>0&&(r&&ft[t]?(e[J][ft[t]]=n,e[Y]=!0):e.url+="&"+t+"="+n)}vt(A,A,!1),vt(T,T),vt(k,"Client-Id"),vt(R,R),vt(D,D),vt(x,x),vt(U,U);var mt=function(){function e(t,n,r,i){var o,a,f,d,h,m,y,b,S,A,L,U,H,O,Q,$e,Me,ze,Fe,Xe,qe,Be,Ge,Ke,et,tt,nt,rt,it,ot,ft,vt,mt=!1;(0,s.A)(e,this,(function(e){function s(e,t){try{return ft&&ft.getSenderInst(e,t)}catch(e){}return null}function gt(){try{return{enableSendPromise:!1,isOneDs:!0,disableCredentials:!1,fetchCredentials:vt,disableXhr:!1,disableBeacon:!mt,disableBeaconSync:!mt,disableFetchKeepAlive:qe,timeWrapper:it,addNoResponse:Ge,senderOnCompleteCallBack:{xdrOnComplete:yt,fetchOnComplete:bt,xhrOnComplete:wt,beaconOnRetry:St}}}catch(e){}return null}function yt(e,t,n){var r=(0,p.Lo)(e);It(t,200,{},r),Ut(r)}function bt(e,t,n,r){var i={},o=e[me];o&&o.forEach((function(e,t){i[t]=e})),function(e,n,r){It(t,e,n,r),Ut(r)}(e.status,i,n||w)}function wt(e,t,n){var r=(0,p.Lo)(e);It(t,e.status,(0,p.w3)(e,!0),r),Ut(r)}function It(e,t,n,r){try{e(t,n,r)}catch(e){(0,g.ZP)(y,2,518,(0,c.mmD)(e))}}function St(e,t,n){var r=200,i=e[ge],o=e[ye]+(Ge?"&NoResponseBody=true":w);try{var a=(0,c.w3n)();if(i){var s=!!S.getPlugin("LocalStorage"),u=[],l=[];(0,c.Iuo)(i[be],(function(e){if(u&&e&&e[K]()>0)for(var t=e[V](),n=0;n<t[q];n++){if(!a.sendBeacon(o,O.getEventBlob(t[n]))){u[j](e[Z](n));break}l[j](e[n])}else u[j](e[Z](0))})),l[q]>0&&(i.sentEvts=l),s||Nt(u,8003,i[we],!0)}else r=0}catch(e){(0,g.OG)(y,"Failed to send telemetry using sendBeacon API. Ex:"+(0,c.mmD)(e)),r=0}finally{It(t,r,{},w)}}function Pt(e){return 2===e||3===e}function Ct(e){return Me&&Pt(e)&&(e=2),e}function Et(){return!f&&h<n}function _t(){var e=H;return H=[],e}function Tt(e,t,n){var r=!1;return e&&e[q]>0&&!f&&b[t]&&O&&(r=0!==t||Et()&&(n>0||d[$]())),r}function kt(e){var t={};return e&&(0,c.Iuo)(e,(function(e,n){t[n]={iKey:e[G](),evts:e[V]()}})),t}function Dt(e,n,r,i,o){if(e&&0!==e[q])if(f)Nt(e,1,i);else{i=Ct(i);try{var s=e,d=0!==i;(0,l.r2)(S,(function(){return"HttpManager:_sendBatches"}),(function(s){s&&(e=e.slice(0));for(var c=[],l=null,f=(0,u.WB)(),v=b[i]||(d?b[1]:b[0]),h=v&&v[ke],p=Be&&(Me||Pt(i)||3===h||v._isSync&&2===h);Tt(e,i,n);){var m=e.shift();m&&m[K]()>0&&(a.isTenantKilled(m[G]())?c[j](m):(l=l||O[ve](n,r,d,p,o,i),O.appendPayload(l,m,t)?null!==l.overflow&&(e=[l.overflow][B](e),l.overflow=null,At(l,f,(0,u.WB)(),o),f=(0,u.WB)(),l=null):(At(l,f,(0,u.WB)(),o),f=(0,u.WB)(),e=[m][B](e),l=null)))}l&&At(l,f,(0,u.WB)(),o),e[q]>0&&(H=e[B](H)),Nt(c,8004,i)}),(function(){return{batches:kt(s),retryCount:n,isTeardown:r,isSynchronous:d,sendReason:o,useSendBeacon:Pt(i),sendType:i}}),!d)}catch(e){(0,g.ZP)(y,2,48,"Unexpected Exception sending batch: "+(0,c.mmD)(e))}}}function xt(e,t){var n={url:o,hdrs:{},useHdrs:!1};t?(n[J]=(0,u.X$)(n[J],U),n.useHdrs=(0,c.cGk)(n.hdrs)[q]>0):(0,c.zav)(U,(function(e,t){dt[e]?pt(n,dt[e],t,!1):(n[J][e]=t,n[Y]=!0)})),pt(n,k,"NO_AUTH",t),pt(n,T,u.xE,t);var r=w;(0,c.Iuo)(e.apiKeys,(function(e){r[q]>0&&(r+=","),r+=e})),pt(n,R,r,t),pt(n,x,(0,c.f0d)().toString(),t);var i=function(e){for(var t=0;t<e.batches[q];t++){var n=e[be][t].Msfpc();if(n)return encodeURIComponent(n)}return w}(e);if((0,u.yD)(i)&&(n.url+="&ext.intweb.msfpc="+i),d[z]()&&pt(n,D,d[F](),t),S[De]){var a=S[De]();a>=0&&(n.url+="&w="+a)}for(var s=0;s<L[q];s++)n.url+="&"+L[s].name+"="+L[s].value;return n}function Rt(e,t,n){e[t]=e[t]||{},e[t][m.identifier]=n}function At(t,n,i,o){if(t&&t.payloadBlob&&t.payloadBlob[q]>0){var s=!!et,f=b[t.sendType];!Pt(t[we])&&t[xe]&&2===t.sendReason&&(f=b[2]||b[3]||f);var v=ze;(t.isBeacon||3===f[ke])&&(v=!1);var p=xt(t,v);v=v||p[Y];var w=(0,u.WB)();(0,l.r2)(S,(function(){return"HttpManager:_doPayloadSend"}),(function(){for(var b=0;b<t.batches[q];b++)for(var I=t[be][b][V](),T=0;T<I[q];T++){var k=I[T];if(Q){var D=k[Re]=k[Re]||{};Rt(D,"sendEventStart",w),Rt(D,"serializationStart",n),Rt(D,"serializationCompleted",i)}k[ut]>0?k[ut]++:k[ut]=1}Nt(t[be],1e3+(o||0),t[we],!0);var x={data:t[pe],urlString:p.url,headers:p[J],_thePayload:t,_sendReason:o,timeout:Fe,disableXhrSync:Xe,disableFetchKeepAlive:qe};v&&(ht(x[me],E)||(x[me][E]=P),ht(x[me],_)||(x[me][_]=C));var R=null;f&&(R=function(n){d[M]();var i=function(n,i){!function(t,n,i,o){var s,l=9e3,f=null,v=!1,p=!1;try{var g=!0;if(typeof t!==We.bA){if(n){d[X](n["time-delta-millis"]);var y=n["kill-duration"]||n["kill-duration-seconds"];(0,c.Iuo)(a[He](n["kill-tokens"],y),(function(e){(0,c.Iuo)(i[be],(function(t){if(t[G]()===e){f=f||[];var n=t[Z](0);i.numEvents-=n[K](),f[j](n)}}))}))}if(200==t||204==t)return void(l=200);((s=t)>=300&&s<500&&429!=s||501==s||505==s||i.numEvents<=0)&&(g=!1),l=9e3+t%1e3}if(g){l=100;var b=i.retryCnt;0===i[we]&&(b<r?(v=!0,Lt((function(){0===i[we]&&h--,Dt(i[be],b+1,i[Ae],Me?2:i[we],5)}),Me,Ye(b))):(p=!0,Me&&(l=8001)))}}finally{v||(d[X](),function(t,n,r,i){try{i&&m[Oe]();var o=t[be];200===n&&(o=t.sentEvts||t[be],i||t[Le]||m._clearBackOff(),function(e){if(Q){var t=(0,u.WB)();(0,c.Iuo)(e,(function(e){e&&e[K]()>0&&function(e,t){Q&&(0,c.Iuo)(e,(function(e){Rt(e[Re]=e[Re]||{},"sendEventCompleted",t)}))}(e[V](),t)}))}}(o)),Nt(o,n,t[we],!0)}finally{0===t[we]&&(h--,5!==r&&e.sendQueuedRequests(t[we],r))}}(i,l,o,p)),Nt(f,8004,i[we])}}(n,i,t,o)},s=t[Ae]||t[Le];try{f.sendPOST(n,i,s),tt&&tt(x,n,s,t[xe])}catch(e){(0,g.OG)(y,"Unexpected exception sending payload. Ex:"+(0,c.mmD)(e)),It(i,0,{})}}),(0,l.r2)(S,(function(){return"HttpManager:_doPayloadSend.sender"}),(function(){if(R)if(0===t[we]&&h++,s&&!t.isBeacon&&3!==f[ke]){var e={data:x[Ue],urlString:x[ye],headers:(0,u.X$)({},x[me]),timeout:x.timeout,disableXhrSync:x[ae],disableFetchKeepAlive:x[se]},n=!1;(0,l.r2)(S,(function(){return"HttpManager:_doPayloadSend.sendHook"}),(function(){try{et(e,(function(e){n=!0,A||e[ge]||(e[ge]=e[ge]||x[ge],e[Ne]=e[Ne]||x[Ne]),R(e)}),t.isSync||t[Ae])}catch(e){n||R(x)}}))}else R(x)}))}),(function(){return{thePayload:t,serializationStart:n,serializationCompleted:i,sendReason:o}}),t[Le])}t.sizeExceed&&t.sizeExceed[q]>0&&Nt(t.sizeExceed,8003,t[we]),t.failedEvts&&t.failedEvts[q]>0&&Nt(t.failedEvts,8002,t[we])}function Lt(e,t,n){t?e():it.set(e,n)}function Ut(e){var t=nt;try{for(var n=0;n<t[q];n++)try{t[n](e)}catch(e){(0,g.ZP)(y,1,519,"Response handler failed: "+e)}if(e){var r=JSON.parse(e);(0,u.yD)(r.webResult)&&(0,u.yD)(r.webResult[N])&&$e.set("MSFPC",r.webResult[N],31536e3)}}catch(e){}}function Nt(e,t,n,r){if(e&&e[q]>0&&i){var o=i[(s=t,c=lt[s],(0,u.yD)(c)||(c="oth",s>=9e3&&s<=9999?c="rspFail":s>=8e3&&s<=8999?c=I:s>=1e3&&s<=1999&&(c="send")),c)];if(o){var a=0!==n;(0,l.r2)(S,(function(){return"HttpManager:_sendBatchesNotification"}),(function(){Lt((function(){try{o.call(i,e,t,a,n)}catch(e){(0,g.ZP)(y,1,74,"send request notification failed: "+e)}}),r||a,0)}),(function(){return{batches:kt(e),reason:t,isSync:a,sendSync:r,sendType:n}}),!a)}}var s,c}!function(){var e;o=null,a=new Je,f=!1,d=new Qe,mt=!1,h=0,m=null,y=null,b=null,S=null,A=!0,L=[],U={},H=[],O=null,Q=!1,$e=null,Me=!1,ze=!1,Fe=e,Xe=e,qe=e,Be=e,Ge=e,Ke=[],et=e,tt=e,nt=[],rt=!1,it=st(),ot=!1,ft=null}(),e[ee]=function(e,t,n){rt||(S=t,$e=t.getCookieMgr(),y=(m=n).diagLog(),(0,c.Yny)(Ke,(0,v.a)(e,(function(e){var r,i=e.cfg,a=e.cfg.extensionConfig[n.identifier];it=st(a[te],a[ne]),(0,u.yD)(i.anonCookieName)?function(e,t,n){for(var r=0;r<e[q];r++)if(e[r].name===t)return void(e[r].value=n);e[j]({name:t,value:n})}(L,"anoncknm",i.anonCookieName):function(e,t){for(var n=0;n<e[q];n++)if("anoncknm"===e[n].name)return void e[W](n,1)}(L),et=a.payloadPreprocessor,tt=a.payloadListener;var l=a.httpXHROverride,f=a[re]?a[re]:i.endpointUrl;o=f+ct,ze=!!(0,c.b07)(a[ie])||!a[ie],Q=!a.disableEventTimings;var d=a.valueSanitizer,v=a.stringifyObjects,h=!!i[oe];(0,c.b07)(a[oe])||(h=!!a[oe]),Fe=a.xhrTimeout,Xe=!!a[ae],qe=!!a[se],Ge=!1!==a.addNoResponse,ot=!!a.excludeCsMetaData,t.getPlugin("LocalStorage")&&(qe=!0),mt=!(0,je.lV)(),O=new at(S,d,v,h,u.Go,ot),(0,c.hXl)(a[ue])||(mt=!!a[ue]),a[ce]&&(vt=a[ce]);var m=gt();ft?ft.SetConfig(m):(ft=new Ze.v)[ee](m,y);var w=l,I=a[le]?l:null,P=a[le]?l:null,C=[3,2];if(!l){A=!1;var E=[];(0,je.lV)()?(E=[2,1],C=[2,1,3]):E=[1,2,3],(l=s(E=(0,p.jL)(E,a.transports),!1))||(0,g.OG)(y,"No available transport to send events"),w=s(E,!0)}I||(I=s(C=(0,p.jL)(C,a.unloadTransports),!0)),Be=!A&&(mt&&(0,je.Uf)()||!qe&&(0,je.R7)(!0)),(r={})[0]=l,r[1]=w||s([1,2,3],!0),r[2]=I||w||s([1],!0),r[3]=P||s([2,3],!0)||w||s([1],!0),b=r}))),rt=!0)},e.addResponseHandler=function(e){return nt[j](e),{rm:function(){var t=nt.indexOf(e);t>=0&&nt[W](t,1)}}},e[fe]=function(e){try{if(O)return O.getEventBlob(e)}catch(e){}return w},e[de]=function(){try{return xt(O&&O[ve](0,!1,!1,!1,1,0),ze)}catch(e){}return null},e[he]=function(e,n){try{var r=[];(0,c.Iuo)(e,(function(e){n&&(e=(0,p.hW)(e));var t=Ve.create(e[G],[e]);r[j](t)}));for(var i=null;r[q]>0&&O;){var o=r.shift();o&&o[K]()>0&&(i=i||O[ve](0,!1,!1,!1,1,0),O.appendPayload(i,o,t))}var a=xt(i,ze),s={data:i[pe],urlString:a.url,headers:a[J],timeout:Fe,disableXhrSync:Xe,disableFetchKeepAlive:qe};return ze&&(ht(s[me],E)||(s[me][E]=P),ht(s[me],_)||(s[me][_]=C)),s}catch(e){}return null},e._getDbgPlgTargets=function(){return[b[0],a,O,b,gt(),o]},e[Ie]=function(e,t){U[e]=t},e.removeHeader=function(e){delete U[e]},e[Se]=function(){return Et()&&d[$]()},e[Pe]=function(e,t){(0,c.b07)(e)&&(e=0),Me&&(e=Ct(e),t=2),Tt(H,e,0)&&Dt(_t(),0,!1,e,t||0)},e[Ce]=function(){return!f&&0===h&&0===H[q]},e[Ee]=function(e){Me=e},e.addBatch=function(e){if(e&&e[K]()>0){if(a.isTenantKilled(e[G]()))return!1;H[j](e)}return!0},e.teardown=function(){H[q]>0&&Dt(_t(),0,!0,2,2),(0,c.Iuo)(Ke,(function(e){e&&e.rm&&e.rm()})),Ke=[]},e.pause=function(){f=!0},e[_e]=function(){f=!1,e[Pe](0,4)},e[Te]=function(e,t,n){e&&e[K]()>0&&((0,c.hXl)(t)&&(t=1),Me&&(t=Ct(t),n=2),Dt([e],0,!1,t,n||0))}}))}return e.__ieDyn=1,e}(),gt=1e4,yt="eventsDiscarded",bt=void 0,wt=(0,c.ZHX)({eventsLimitInMem:{isVal:u.ei,v:gt},immediateEventLimit:{isVal:u.ei,v:500},autoFlushEventsLimit:{isVal:u.ei,v:0},disableAutoBatchFlushLimit:!1,httpXHROverride:{isVal:function(e){return e&&e.sendPOST},v:bt},overrideInstrumentationKey:bt,overrideEndpointUrl:bt,disableTelemetry:!1,ignoreMc1Ms0CookieProcessing:!1,setTimeoutOverride:bt,clearTimeoutOverride:bt,payloadPreprocessor:bt,payloadListener:bt,disableEventTimings:bt,valueSanitizer:bt,stringifyObjects:bt,enableCompoundKey:bt,disableOptimizeObj:!1,fetchCredentials:bt,transports:bt,unloadTransports:bt,useSendBeacon:bt,disableFetchKeepAlive:bt,avoidOptions:!1,xhrTimeout:bt,disableXhrSync:bt,alwaysUseXhrOverride:!1,maxEventRetryAttempts:{isVal:c.EtT,v:6},maxUnloadEventRetryAttempts:{isVal:c.EtT,v:2},addNoResponse:bt,excludeCsMetaData:bt}),It=function(e){function t(){var n,a=e.call(this)||this;a.identifier="PostChannel",a.priority=1011,a.version="4.3.4";var y,w,I,S,P,C,E,_,T,k,D,x,R,U,N,$,M,z,F,X,Q,J,Y,re,ie,oe=!1,ae=[],se=!1,ue=0,ce=0,le={},ve=r;return(0,s.A)(t,a,(function(e,t){function a(){(0,f.Ds)(null,z),(0,f.sq)(null,z),(0,f.vF)(null,z)}function s(e){var t="";return e&&e[q]&&(0,c.Iuo)(e,(function(e){t&&(t+="\n"),t+=e})),t}function pe(e){var t="";try{ye(e),t=_[fe](e)}catch(e){}return t}function me(e){"beforeunload"!==(e||(0,c.zkX)().event).type&&(N=!0,_[Ee](N)),He(2,2)}function ge(e){N=!1,_[Ee](N)}function ye(e){e.ext&&e.ext[H]&&delete e.ext[H],e.ext&&e.ext[O]&&e.ext[O].id&&delete e.ext[O].id,U&&(e.ext=(0,p.hW)(e.ext),e[Fe]&&(e[Fe]=(0,p.hW)(e[Fe])),e[Ue]&&(e[Ue]=(0,p.hW)(e[Ue])))}function we(e,t){if(e[Xe]||(e[Xe]=0),e[qe]||(e[qe]=1),ye(e),e[Be])if(C||se)e[qe]=3,e[Be]=!1;else if(_)return U&&(e=(0,p.hW)(e)),void _[Te](Ve.create(e[G],[e]),!0===e[Be]?1:e[Be],3);var n=e[qe],r=ce,i=I;4===n&&(r=ue,i=w);var o=!1;if(r<i)o=!je(e,t);else{var a=1,s=20;4===n&&(a=4,s=1),o=!0,function(e,t,n,r){for(;n<=t;){var i=Ge(e,t,!0);if(i&&i[K]()>0){var o=i[Z](0,r),a=o[K]();if(a>0)return 4===n?ue-=a:ce-=a,it(yt,[o],m.x.QueueFull),!0}n++}return Ze(),!1}(e[G],e[qe],a,s)&&(o=!je(e,t))}o&&rt(yt,[e],m.x.QueueFull)}function ke(e,t,n){var r=We(e,t,n);return _[Pe](t,n),r}function xe(){return ce>0}function Re(){if(x>=0&&We(x,0,R)&&_[Pe](0,R),ue>0&&!P&&!se){var e=le[ve][2];e>=0&&(P=Le((function(){P=null,ke(4,0,1),Re()}),e))}var t=le[ve][1];!S&&!y&&t>=0&&!se&&(xe()?S=Le((function(){S=null,ke(0===E?3:1,0,1),E++,E%=2,Re()}),t):E=0)}function Ae(){n=null,oe=!1,ae=[],y=null,se=!1,ue=0,w=500,ce=0,I=gt,le={},ve=r,S=null,P=null,C=0,E=0,T={},k=0,Y=!1,D=0,x=-1,R=null,U=!0,N=!1,$=6,M=2,z=null,re=null,ie=!1,F=st(),_=new mt(500,2,1,{requeue:tt,send:ot,sent:at,drop:ut,rspFail:ct,oth:lt}),et(),T[4]={batches:[],iKeyMap:{}},T[3]={batches:[],iKeyMap:{}},T[2]={batches:[],iKeyMap:{}},T[1]={batches:[],iKeyMap:{}},ft()}function Le(e,t){0===t&&C&&(t=1);var n=1e3;return C&&(n=Ye(C-1)),F.set(e,t*n)}function Ne(){return null!==S&&(S.cancel(),S=null,E=0,!0)}function He(e,t){Ne(),y&&(y.cancel(),y=null),se||ke(1,e,t)}function Ge(e,t,n){var r=T[t];r||(r=T[t=1]);var i=r.iKeyMap[e];return!i&&n&&(i=Ve.create(e),r.batches[j](i),r.iKeyMap[e]=i),i}function Ke(t,n){_[Se]()&&!C&&(k>0&&ce>k&&(n=!0),n&&null==y&&e.flush(t,(function(){}),20))}function je(e,t){U&&(e=(0,p.hW)(e));var n=e[qe],r=Ge(e[G],n,!0);return!!r.addEvent(e)&&(4!==n?(ce++,t&&0===e[Xe]&&Ke(!e.sync,D>0&&r[K]()>=D)):ue++,!0)}function Ze(){for(var e=0,t=0,n=function(n){var r=T[n];r&&r[be]&&(0,c.Iuo)(r[be],(function(r){4===n?e+=r[K]():t+=r[K]()}))},r=1;r<=4;r++)n(r);ce=t,ue=e}function We(t,n,r){var i=!1,o=0===n;return!o||_[Se]()?(0,l.r2)(e.core,(function(){return"PostChannel._queueBatches"}),(function(){for(var e=[],n=4;n>=t;){var r=T[n];r&&r.batches&&r.batches[q]>0&&((0,c.Iuo)(r[be],(function(t){_.addBatch(t)?i=i||t&&t[K]()>0:e=e[B](t[V]()),4===n?ue-=t[K]():ce-=t[K]()})),r[be]=[],r.iKeyMap={}),n--}e[q]>0&&rt(yt,e,m.x.KillSwitch),i&&x>=t&&(x=-1,R=0)}),(function(){return{latency:t,sendType:n,sendReason:r}}),!o):(x=x>=0?Math.min(x,t):t,R=Math.max(R,r)),i}function Qe(e,t){ke(1,0,t),Ze(),Je((function(){e&&e(),ae[q]>0?y=Le((function(){y=null,Qe(ae.shift(),t)}),0):(y=null,Re())}))}function Je(e){_[Ce]()?e():y=Le((function(){y=null,Je(e)}),.25)}function et(){(le={})[r]=[2,1,0],le[i]=[6,3,0],le[o]=[18,9,0]}function tt(t,n){var r=[],i=$;N&&(i=M),(0,c.Iuo)(t,(function(t){t&&t[K]()>0&&(0,c.Iuo)(t[V](),(function(t){t&&(t[Be]&&(t[qe]=4,t[Be]=!1),t[Xe]<i?((0,u.u9)(t,e[$e]),we(t,!1)):r[j](t))}))})),r[q]>0&&rt(yt,r,m.x.NonRetryableStatus),N&&He(2,2)}function nt(t,n){var r=J||{},i=r[t];if(i)try{i.apply(r,n)}catch(n){(0,g.ZP)(e.diagLog(),1,74,t+" notification failed: "+n)}}function rt(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];t&&t[q]>0&&nt(e,[t][B](n))}function it(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];t&&t[q]>0&&(0,c.Iuo)(t,(function(t){t&&t[K]()>0&&nt(e,[t.events()][B](n))}))}function ot(e,t,n){e&&e[q]>0&&nt("eventsSendRequest",[t>=1e3&&t<=1999?t-1e3:0,!0!==n])}function at(e,t){it("eventsSent",e,t),Re()}function ut(e,t){it(yt,e,t>=8e3&&t<=8999?t-8e3:m.x.Unknown)}function ct(e){it(yt,e,m.x.NonRetryableStatus),Re()}function lt(e,t){it(yt,e,m.x.Unknown),Re()}function ft(){D=Q?0:Math.max(1500,I/6)}Ae(),e._getDbgPlgTargets=function(){return[_,n]},e[ee]=function(r,i,o){(0,l.r2)(i,(function(){return"PostChannel:initialize"}),(function(){t[ee](r,i,o),J=i.getNotifyMgr();try{z=(0,f.Hm)((0,d.Z)(e[$e]),i.evtNamespace&&i.evtNamespace()),e._addHook((0,v.a)(r,(function(t){var r=t.cfg,o=(0,h.i8)(null,r,i);n=o.getExtCfg(e[$e],wt),F=st(n[te],n[ne]),U=!n.disableOptimizeObj&&(0,u.F2)(),X=n.ignoreMc1Ms0CookieProcessing,function(e){var t=e[De];e[De]=function(){var n=0;return X&&(n|=2),n|t.call(e)}}(i),I=n[Me],w=n.immediateEventLimit,k=n[ze],$=n.maxEventRetryAttempts,M=n.maxUnloadEventRetryAttempts,Q=n.disableAutoBatchFlushLimit,(0,c.$XS)(r.endpointUrl)?e.pause():se&&e[_e](),ft(),re=n.overrideInstrumentationKey,ie=!!n.disableTelemetry,Y&&a();var s=r.disablePageUnloadEvents||[];Y=(0,f.ee)(me,s,z),Y=(0,f.Fc)(me,s,z)||Y,Y=(0,f.oS)(ge,r.disablePageShowEvents,z)||Y}))),_[ee](r,e.core,e)}catch(t){throw e.setInitialized(!1),t}}),(function(){return{theConfig:r,core:i,extensions:o}}))},e.processTelemetry=function(t,n){(0,u.u9)(t,e[$e]),n=n||e._getTelCtx(n);var r=t;ie||oe||(re&&(r[G]=re),we(r,!0),N?He(2,2):Re()),e.processNext(r,n)},e.getOfflineSupport=function(){try{var e=_&&_[de]();if(_)return{getUrl:function(){return e?e.url:null},serialize:pe,batch:s,shouldProcess:function(e){return!ie},createPayload:function(e){return null},createOneDSPayload:function(e){if(_[he])return _[he](e,U)}}}catch(e){}return null},e._doTeardown=function(e,t){He(2,2),oe=!0,_.teardown(),a(),Ae()},e.setEventQueueLimits=function(e,t){n[Me]=I=(0,u.ei)(e)?e:gt,n[ze]=k=(0,u.ei)(t)?t:0,ft();var r=ce>e;if(!r&&D>0)for(var i=1;!r&&i<=3;i++){var o=T[i];o&&o[be]&&(0,c.Iuo)(o[be],(function(e){e&&e[K]()>=D&&(r=!0)}))}Ke(!0,r)},e.pause=function(){Ne(),se=!0,_&&_.pause()},e[_e]=function(){se=!1,_&&_[_e](),Re()},e._loadTransmitProfiles=function(e){Ne(),et(),ve=r,Re(),(0,c.zav)(e,(function(e,t){var n=t[q];if(n>=2){var r=n>2?t[2]:0;if(t[W](0,n-2),t[1]<0&&(t[0]=-1),t[1]>0&&t[0]>0){var i=t[0]/t[1];t[0]=Math.ceil(i)*t[1]}r>=0&&t[1]>=0&&r>t[1]&&(r=t[1]),t[j](r),le[e]=t}}))},e.flush=function(e,t,n){var r;if(void 0===e&&(e=!0),!se)if(n=n||1,e)t||(r=(0,b.Qo)((function(e){t=e}))),null==y?(Ne(),We(1,0,n),y=Le((function(){y=null,Qe(t,n)}),0)):ae[j](t);else{var i=Ne();ke(1,1,n),t&&t(),i&&Re()}return r},e.setMsaAuthTicket=function(e){_[Ie](A,e)},e.setAuthPluginHeader=function(e){_[Ie](L,e)},e.removeAuthPluginHeader=function(){_.removeHeader(L)},e.hasEvents=xe,e._setTransmitProfile=function(e){ve!==e&&void 0!==le[e]&&(Ne(),ve=e,Re())},(0,p.o$)(e,(function(){return _}),["addResponseHandler"]),e[Oe]=function(){C<4&&(C++,Ne(),Re())},e._clearBackOff=function(){C&&(C=0,Ne(),Re())}})),a}return(0,a.qU)(t,e),t.__ieDyn=1,t}(y.s)},2845:(e,t,n)=>{n.d(t,{Uu:()=>r});var r="AppInsightsChannelPlugin"},2475:(e,t,n)=>{n.d(t,{DD:()=>u,Lx:()=>s,NU:()=>a});var r=n(269),i=n(6182);function o(e,t,n){return!e&&(0,r.hXl)(e)?t:(0,r.Lmq)(e)?e:"true"===(0,r.oJg)(e)[i.OL]()}function a(e){return{mrg:!0,v:e}}function s(e,t,n){return{fb:n,isVal:e,v:t}}function u(e,t){return{fb:t,set:o,v:!!e}}},991:(e,t,n)=>{n.d(t,{q:()=>u});var r=n(269),i=n(6182);function o(e){return e&&(0,r.Gvm)(e)&&(e.isVal||e.fb||(0,r.KhI)(e,"v")||(0,r.KhI)(e,"mrg")||(0,r.KhI)(e,"ref")||e.set)}function a(e,t,n){var o,a=n.dfVal||r.O9V;if(t&&n.fb){var s=n.fb;(0,r.cyL)(s)||(s=[s]);for(var u=0;u<s[i.oI];u++){var c=s[u],l=t[c];if(a(l)?o=l:e&&(a(l=e.cfg[c])&&(o=l),e.set(e.cfg,(0,r.oJg)(c),l)),a(o))break}}return!a(o)&&a(n.v)&&(o=n.v),o}function s(e,t,n){var u,c=n;return n&&o(n)&&(c=a(e,t,n)),c&&(o(c)&&(c=s(e,t,c)),(0,r.cyL)(c)?(u=[])[i.oI]=c[i.oI]:(0,r.QdQ)(c)&&(u={}),u&&((0,r.zav)(c,(function(n,r){r&&o(r)&&(r=s(e,t,r)),u[n]=r})),c=u)),c}function u(e,t,n,c){var l,f,d,v,h,p,m,g,y=c;o(y)?(l=y.isVal,f=y.set,p=y[i.XW],m=y[i.JQ],v=y.mrg,!(h=y.ref)&&(0,r.b07)(h)&&(h=!!v),d=a(e,t,y)):d=c,m&&e[i.JQ](t,n);var b=!0,w=t[n];!w&&(0,r.hXl)(w)||(g=w,b=!1,l&&g!==d&&!l(g)&&(g=d,b=!0),f&&(b=(g=f(g,d,t))===d)),b?g=d?s(e,t,d):d:((0,r.QdQ)(g)||(0,r.cyL)(d))&&v&&d&&((0,r.QdQ)(d)||(0,r.cyL)(d))&&(0,r.zav)(d,(function(t,n){u(e,g,t,n)})),e.set(t,n,g),h&&e.ref(t,n),p&&e[i.XW](t,n)}},9749:(e,t,n)=>{n.d(t,{e:()=>b,a:()=>w});var r,i=n(269),o=n(4276),a=n(6492),s=n(6182),u=n(991),c=n(9147),l=["push","pop","shift","unshift","splice"],f=function(e,t,n,r){e&&e[s.ih](3,108,"".concat(n," [").concat(t,"] failed - ")+(0,i.mmD)(r))};function d(e,t){var n=(0,i.kgX)(e,t);return n&&n.get}function v(e,t,n,r){if(t){var o=d(t,n);o&&o[e.prop]?t[n]=r:function(e,t,n,r){var o={n,h:[],trk:function(t){t&&t.fn&&(-1===(0,i.rDm)(o.h,t)&&o.h[s.y5](t),e.trk(t,o))},clr:function(e){var t=(0,i.rDm)(o.h,e);-1!==t&&o.h[s.Ic](t,1)}},u=!0,l=!1;function h(){u&&(l=l||(0,c.hF)(h,e,r),r&&!r[c.nM]&&l&&(r=p(e,r,n,"Converting")),u=!1);var t=e.act;return t&&o.trk(t),r}h[e.prop]={chng:function(){e.add(o)}},(0,i.vF1)(t,o.n,{g:h,s:function(m){if(r!==m){h[e.ro]&&!e.upd&&(0,c.If)("["+n+"] is read-only:"+(0,i.mmD)(t)),u&&(l=l||(0,c.hF)(h,e,r),u=!1);var g=l&&h[e.rf];if(l)if(g){(0,i.zav)(r,(function(e){r[e]=m?m[e]:a.HP}));try{(0,i.zav)(m,(function(t,n){v(e,r,t,n)})),m=r}catch(t){f((e.hdlr||{})[s.Uw],n,"Assigning",t),l=!1}}else r&&r[c.nM]&&(0,i.zav)(r,(function(t){var n=d(r,t);if(n){var i=n[e.prop];i&&i.chng()}}));if(m!==r){var y=m&&(0,c.hF)(h,e,m);!g&&y&&(m=p(e,m,n,"Converting")),r=m,l=y}e.add(o)}}})}(e,t,n,r)}return t}function h(e,t,n,r){if(t){var i=d(t,n),o=i&&!!i[e.prop],a=r&&r[0],u=r&&r[1],l=r&&r[2];if(!o){if(l)try{(0,c.V9)(t)}catch(t){f((e.hdlr||{})[s.Uw],n,"Blocking",t)}try{v(e,t,n,t[n]),i=d(t,n)}catch(t){f((e.hdlr||{})[s.Uw],n,"State",t)}}a&&(i[e.rf]=a),u&&(i[e.ro]=u),l&&(i[e.blkVal]=!0)}return t}function p(e,t,n,r){try{(0,i.zav)(t,(function(n,r){v(e,t,n,r)})),t[c.nM]||((0,i.UxO)(t,c.nM,{get:function(){return e[s.K0]}}),function(e,t,n){(0,i.cyL)(t)&&(0,i.Iuo)(l,(function(r){var i=t[r];t[r]=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var a=i[s.y9](this,r);return p(e,t,n,"Patching"),a}}))}(e,t,n))}catch(t){f((e.hdlr||{})[s.Uw],n,r,t)}return t}var m="[[ai_",g="]]";function y(e,t,n){var a,l=(0,c.QA)(t);if(l)return l;var d,y=(0,o.Z)("dyncfg",!0),b=t&&!1!==n?t:(0,c.Dy)(t),w=((a={uid:null,cfg:b})[s.Uw]=e,a[s.zs]=function(){d[s.zs]()},a.set=function(t,n,r){try{t=v(d,t,n,r)}catch(t){f(e,n,"Setting value",t)}return t[n]},a[s.h0]=function(e,t){return t&&(0,i.zav)(t,(function(t,n){(0,u.q)(w,e,t,n)})),e},a[s.x6]=function(e){return function(e,t){var n={fn:t,rm:function(){n.fn=null,e=null,t=null}};return(0,i.vF1)(n,"toJSON",{v:function(){return"WatcherHandler"+(n.fn?"":"[X]")}}),e.use(n,t),n}(d,e)},a.ref=function(e,t){var n;return h(d,e,t,(n={},n[0]=!0,n))[t]},a[s.XW]=function(e,t){var n;return h(d,e,t,(n={},n[1]=!0,n))[t]},a[s.JQ]=function(e,t){var n;return h(d,e,t,(n={},n[2]=!0,n))[t]},a._block=function(e,t){d.use(null,(function(n){var r=d.upd;try{(0,i.b07)(t)||(d.upd=t),e(n)}finally{d.upd=r}}))},a);return(0,i.vF1)(w,"uid",{c:!1,e:!1,w:!1,v:y}),p(d=function(e){var t,n,o=(0,i.jjc)(m+"get"+e.uid+g),a=(0,i.jjc)(m+"ro"+e.uid+g),u=(0,i.jjc)(m+"rf"+e.uid+g),c=(0,i.jjc)(m+"blkVal"+e.uid+g),l=(0,i.jjc)(m+"dtl"+e.uid+g),f=null,d=null;function v(t,r){var o=n.act;try{n.act=t,t&&t[l]&&((0,i.Iuo)(t[l],(function(e){e.clr(t)})),t[l]=[]),r({cfg:e.cfg,set:e.set.bind(e),setDf:e[s.h0].bind(e),ref:e.ref.bind(e),rdOnly:e[s.XW].bind(e)})}catch(t){var a=e[s.Uw];throw a&&a[s.ih](1,107,(0,i.mmD)(t)),t}finally{n.act=o||null}}function h(){if(f){var e=f;f=null,d&&d[s._w](),d=null;var t=[];if((0,i.Iuo)(e,(function(e){if(e&&(e[l]&&((0,i.Iuo)(e[l],(function(t){t.clr(e)})),e[l]=null),e.fn))try{v(e,e.fn)}catch(e){t[s.y5](e)}})),f)try{h()}catch(e){t[s.y5](e)}t[s.oI]>0&&function(e,t){r||(r=(0,i.aqQ)("AggregationError",(function(e,t){t[s.oI]>1&&(e.errors=t[1])})));var n="Watcher error(s): ";throw(0,i.Iuo)(t,(function(e,t){n+="\n".concat(t," > ").concat((0,i.mmD)(e))})),new r(n,t||[])}(0,t)}}return(t={prop:o,ro:a,rf:u})[s.JQ]=c,t[s.K0]=e,t.add=function(e){if(e&&e.h[s.oI]>0){f||(f=[]),d||(d=(0,i.dRz)((function(){d=null,h()}),0));for(var t=0;t<e.h[s.oI];t++){var n=e.h[t];n&&-1===(0,i.rDm)(f,n)&&f[s.y5](n)}}},t[s.zs]=h,t.use=v,t.trk=function(e,t){if(e){var n=e[l]=e[l]||[];-1===(0,i.rDm)(n,t)&&n[s.y5](t)}},n=t}(w),b,"config","Creating"),w}function b(e,t,n,r){var i=y(n,e||{},r);return t&&i[s.h0](i.cfg,t),i}function w(e,t,n){var r=e[c.nM]||e;return!r.cfg||r.cfg!==e&&r.cfg[c.nM]!==r?(function(e,t){e?(e[s.on](t),e[s.ih](2,108,t)):(0,c.If)(t)}(n,a.xW+(0,i.mmD)(e)),b(e,null,n)[s.x6](t)):r[s.x6](t)}},9147:(e,t,n)=>{n.d(t,{Dy:()=>u,Hf:()=>f,If:()=>v,QA:()=>c,V9:()=>l,hF:()=>d,nM:()=>o});var r=n(269),i=n(6182),o=(0,r.eCG)("[[ai_dynCfg_1]]"),a=(0,r.eCG)("[[ai_blkDynCfg_1]]"),s=(0,r.eCG)("[[ai_frcDynCfg_1]]");function u(e){var t;return e&&((0,r.cyL)(e)?(t=[])[i.oI]=e[i.oI]:(0,r.QdQ)(e)&&(t={}),t)?((0,r.zav)(e,(function(e,n){t[e]=u(n)})),t):e}function c(e){if(e){var t=e[o]||e;if(t.cfg&&(t.cfg===e||t.cfg[o]===t))return t}return null}function l(e){if(e&&((0,r.QdQ)(e)||(0,r.cyL)(e)))try{e[a]=!0}catch(e){}return e}function f(e){if(e)try{e[s]=!0}catch(e){}return e}function d(e,t,n){var i=!1;return n&&!e[t.blkVal]&&((i=n[s])||n[a]||(i=(0,r.QdQ)(n)||(0,r.cyL)(n))),i}function v(e){(0,r.zkd)("InvalidAccess:"+e)}},4282:(e,t,n)=>{n.d(t,{H:()=>i,o:()=>o});var r=n(269),i=r.WSA,o=r.fn0},3662:(e,t,n)=>{n.d(t,{x:()=>i});var r=n(4282),i=(0,r.H)({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5});(0,r.H)({Unknown:0,NonRetryableStatus:1,CleanStorage:2,MaxInStorageTimeExceeded:3})},4875:(e,t,n)=>{n.d(t,{f:()=>r});var r=(0,n(4282).H)({NONE:0,PENDING:3,INACTIVE:1,ACTIVE:2})},2774:(e,t,n)=>{n.d(t,{_:()=>L});var r,i=n(659),o=n(8279),a=n(8205),s=n(269),u=n(9749),c=n(4875),l=n(6182),f=n(4013),d=n(7847),v=n(5034),h=n(4276),p=n(7867),m=n(3775),g=n(3673),y=n(6492),b=n(1356),w=n(8156),I=n(2317),S=n(380),P=function(e){function t(){var n,r,i=e.call(this)||this;function a(){n=0,r=[]}return i.identifier="TelemetryInitializerPlugin",i.priority=199,a(),(0,o.A)(t,i,(function(e,t){e.addTelemetryInitializer=function(e){return function(e,t,n){var r={id:t,fn:n};return(0,s.Yny)(e,r),{remove:function(){(0,s.Iuo)(e,(function(t,n){if(t.id===r.id)return e[l.Ic](n,1),-1}))}}}(r,n++,e)},e[y.qT]=function(t,n){(function(e,t,n){for(var r=!1,i=e[l.oI],o=0;o<i;++o){var a=e[o];if(a)try{if(!1===a.fn[l.y9](null,[t])){r=!0;break}}catch(e){(0,m.ZP)(n,2,64,"Telemetry initializer failed: "+(0,g.lL)(e),{exception:(0,s.mmD)(e)},!0)}}return!r})(r,t,n?n[l.e4]():e[l.e4]())&&e[l.$5](t,n)},e[l.tn]=function(){a()}})),i}return(0,i.qU)(t,e),t.__ieDyn=1,t}(n(8257).s),C=n(836),E=n(8969),_="Plugins must provide initialize method",T="SDK is still unloading...",k=(0,s.ZHX)(((r={cookieCfg:{}})[y.jy]={rdOnly:!0,ref:!0,v:[]},r[y.LZ]={rdOnly:!0,ref:!0,v:[]},r[y.Bw]={ref:!0,v:{}},r[y.Yd]=y.HP,r.loggingLevelConsole=0,r.diagnosticLogInterval=y.HP,r));function D(e,t){return new w.NS(t)}function x(e,t){var n=!1;return(0,s.Iuo)(t,(function(t){if(t===e)return n=!0,-1})),n}function R(e,t,n,r){n&&(0,s.zav)(n,(function(n,i){r&&(0,s.QdQ)(i)&&(0,s.QdQ)(t[n])&&R(e,t[n],i,r),r&&(0,s.QdQ)(i)&&(0,s.QdQ)(t[n])?R(e,t[n],i,r):e.set(t,n,i)}))}function A(e,t){var n=null,r=-1;return(0,s.Iuo)(e,(function(e,i){if(e.w===t)return n=e,r=i,-1})),{i:r,l:n}}var L=function(){function e(){var t,n,r,L,U,N,H,O,$,M,z,F,X,q,B,G,K,V,j,Z,W,Q,J,Y,ee,te,ne,re,ie,oe,ae,se;(0,o.A)(e,this,(function(e){function o(){ie=!0,(0,s.hXl)(W)?(te=c.f[l.Yq],(0,m.ZP)(r,1,112,"ikey can't be resolved from promises")):te=c.f.ACTIVE,ue()}function ue(){n&&(e.releaseQueue(),e[l.h4]())}function ce(e){return oe&&oe[l.XM]||se||(e||r&&r.queue[l.oI]>0)&&(ae||(ae=!0,Se(t[l.x6]((function(e){var t=e.cfg.diagnosticLogInterval;t&&t>0||(t=1e4);var n=!1;oe&&(n=oe[l.XM],oe[l._w]()),(oe=(0,s.AHH)(ge,t)).unref(),oe[l.XM]=n})))),oe[l.XM]=!0),oe}function le(){var e={};Y=[];var t=function(t){t&&(0,s.Iuo)(t,(function(t){if(t[l.Ju]&&t[l.s]&&!e[t.identifier]){var n=t[l.Ju]+"="+t[l.s];Y[l.y5](n),e[t.identifier]=t}}))};t(F),z&&(0,s.Iuo)(z,(function(e){t(e)})),t(M)}function fe(){n=!1,(t=(0,u.e)({},k,e[l.Uw])).cfg[l.Bl]=1,(0,s.vF1)(e,"config",{g:function(){return t.cfg},s:function(t){e.updateCfg(t,!1)}}),(0,s.vF1)(e,"pluginVersionStringArr",{g:function(){return Y||le(),Y}}),(0,s.vF1)(e,"pluginVersionString",{g:function(){return ee||(Y||le(),ee=Y.join(";")),ee||y.m5}}),(0,s.vF1)(e,"logger",{g:function(){return r||(r=new m.wq(t.cfg),t[l.Uw]=r),r},s:function(e){t[l.Uw]=e,r!==e&&((0,f.K)(r,!1),r=e)}}),e[l.Uw]=new m.wq(t.cfg),J=[];var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,s.Yny)(i,J),q=new P,L=[],(0,f.K)(U,!1),U=null,N=null,H=null,(0,f.K)(O,!1),O=null,$=null,M=[],z=null,F=null,X=!1,B=null,G=(0,h.Z)("AIBaseCore",!0),K=(0,C.P)(),Z=null,W=null,V=(0,E.w)(),Q=[],ee=null,Y=null,se=!1,oe=null,ae=!1,te=0,ne=null,re=null,ie=!1}function de(){var n=(0,I.i8)(pe(),t.cfg,e);return n[l.by](ce),n}function ve(t){var n=function(e,t,n){var r,i=[],o=[],a={};return(0,s.Iuo)(n,(function(n){((0,s.hXl)(n)||(0,s.hXl)(n[l.mE]))&&(0,s.$8)(_);var r=n[y.Vo],u=n[l.Ju];n&&r&&((0,s.hXl)(a[r])?a[r]=u:(0,m.OG)(e,"Two extensions have same priority #"+r+" - "+a[r]+", "+u)),!r||r<t?i[l.y5](n):o[l.y5](n)})),(r={})[y.eT]=i,r[y.LZ]=o,r}(e[l.Uw],d.i,M);$=null,ee=null,Y=null,F=(z||[])[0]||[],F=(0,S.Xc)((0,s.Yny)(F,n[y.LZ]));var r=(0,s.Yny)((0,S.Xc)(n[y.eT]),F);J=(0,s.N6t)(r);var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,s.Yny)(i,J);var o=de();F&&F[l.oI]>0&&(0,S.pI)(o[l.$o](F),r),(0,S.pI)(o,r),t&&be(t)}function he(e){var t=null,n=null,r=[];return(0,s.Iuo)(J,(function(t){if(t[l.Ju]===e&&t!==q)return n=t,-1;t.getChannel&&r[l.y5](t)})),!n&&r[l.oI]>0&&(0,s.Iuo)(r,(function(t){if(!(n=t.getChannel(e)))return-1})),n&&(t={plugin:n,setEnabled:function(e){(0,S.Cr)(n)[y.Hr]=!e},isEnabled:function(){var e=(0,S.Cr)(n);return!e[l.Ik]&&!e[y.Hr]},remove:function(e,t){var r;void 0===e&&(e=!0);var i=[n],o=((r={reason:1})[l.tI]=e,r);me(i,o,(function(e){e&&ve({reason:32,removed:i}),t&&t(e)}))}}),t}function pe(){if(!$){var n=(J||[]).slice();-1===(0,s.rDm)(n,q)&&n[l.y5](q),$=(0,I.PV)((0,S.Xc)(n),t.cfg,e)}return $}function me(n,r,i){if(n&&n[l.oI]>0){var o=(0,I.PV)(n,t.cfg,e),a=(0,I.tS)(o,e);a[l.by]((function(){var e=!1,t=[];(0,s.Iuo)(M,(function(r,i){x(r,n)?e=!0:t[l.y5](r)})),M=t,ee=null,Y=null;var r=[];z&&((0,s.Iuo)(z,(function(t,i){var o=[];(0,s.Iuo)(t,(function(t){x(t,n)?e=!0:o[l.y5](t)})),r[l.y5](o)})),z=r),i&&i(e),ce()})),a[l.$5](r)}else i(!1)}function ge(){if(r&&r.queue){var t=r.queue.slice(0);r.queue[l.oI]=0,(0,s.Iuo)(t,(function(t){var n,r=((n={})[l.RS]=B||"InternalMessageId: "+t[l.JR],n[l.FI]=W,n[l.fA]=(0,g._u)(new Date),n.baseType=m.WD.dataType,n.baseData={message:t[l.pM]},n);e.track(r)}))}}function ye(e,t,n,r){var i=1,o=!1,a=null;function u(){i--,o&&0===i&&(a&&a[l._w](),a=null,t&&t(o),t=null)}return r=r||5e3,F&&F[l.oI]>0&&de()[l.$o](F).iterate((function(t){if(t.flush){i++;var o=!1;t.flush(e,(function(){o=!0,u()}),n)||o||(e&&null==a?a=(0,s.dRz)((function(){a=null,u()}),r):u())}})),o=!0,u(),!0}function be(t){var n=(0,I.nU)(pe(),e);n[l.by](ce),e._updateHook&&!0===e._updateHook(n,t)||n[l.$5](t)}function we(t){var n=e[l.Uw];n?((0,m.ZP)(n,2,73,t),ce()):(0,s.$8)(t)}function Ie(t){var n=e[l.RF]();n&&n[y.Yp]([t],2)}function Se(e){V.add(e)}fe(),e._getDbgPlgTargets=function(){return[J,L]},e[l.tZ]=function(){return n},e.activeStatus=function(){return te},e._setPendingStatus=function(){te=3},e[l.mE]=function(f,d,v,h){var b;X&&(0,s.$8)(T),e[l.tZ]()&&(0,s.$8)("Core cannot be initialized more than once"),t=(0,u.e)(f,k,v||e[l.Uw],!1),f=t.cfg,Se(t[l.x6]((function(e){var t=e.cfg;if(3!==te){re=t.initInMemoMaxSize||100;var i=t[l.sl],u=t.endpointUrl;if((0,s.hXl)(i)){W=null,te=c.f[l.Yq];var d="Please provide instrumentation key";n?((0,m.ZP)(r,1,100,d),ue()):(0,s.$8)(d)}else{var v=[];if((0,s.$XS)(i)?(v[l.y5](i),W=null):W=i,(0,s.$XS)(u)?(v[l.y5](u),ne=null):ne=u,v[l.oI]){ie=!1,te=3;var h=(0,g.Gh)(t.initTimeOut)?t.initTimeOut:5e4,p=(0,a.lh)(v);(0,s.dRz)((function(){ie||o()}),h),(0,a.Dv)(p,(function(e){try{if(ie)return;if(!e.rejected){var t=e[l.pF];if(t&&t[l.oI]){var n=t[0];if(W=n&&n[l.pF],t[l.oI]>1){var r=t[1];ne=r&&r[l.pF]}}W&&(f[l.sl]=W,f.endpointUrl=ne)}o()}catch(e){ie||o()}}))}else o();var b=e.ref(e.cfg,y.Bw);(0,s.zav)(b,(function(t){e.ref(b,t)}))}}}))),j=function(e,t,n,r){return t.add(e[l.x6]((function(e){var t=e.cfg.disableDbgExt;!0===t&&r&&(n[l.h3](r),r=null),n&&!r&&!0!==t&&(r=(0,p.M)(e.cfg),n[l.vR](r))}))),r}(t,V,(U=h)&&e[l.RF](),j),Se(t[l.x6]((function(t){if(t.cfg.enablePerfMgr){var n=t.cfg[y.Yd];b===n&&b||(n||(n=D),(0,g.c2)(t.cfg,y.Yd,n),b=n,H=null),N||H||!(0,s.Tnt)(n)||(H=n(e,e[l.RF]()))}else H=null,b=null}))),e[l.Uw]=v;var w=f[y.jy];if((M=[])[l.y5].apply(M,(0,i.vz)((0,i.vz)([],d,!1),w,!1)),z=f[y.LZ],ve(null),F&&0!==F[l.oI]||(0,s.$8)("No "+y.LZ+" available"),z&&z[l.oI]>1){var I=e[l.AP]("TeeChannelController");I&&I.plugin||(0,m.ZP)(r,1,28,"TeeChannel required")}!function(e,t,n){(0,s.Iuo)(t,(function(t){var r=(0,u.a)(e,t.w,n);delete t.w,t.rm=function(){r.rm()}}))}(f,Q,r),Q=null,n=!0,te===c.f.ACTIVE&&ue()},e.getChannels=function(){var e=[];return F&&(0,s.Iuo)(F,(function(t){e[l.y5](t)})),(0,s.N6t)(e)},e.track=function(t){(0,w.r2)(e[y.kI](),(function(){return"AppInsightsCore:track"}),(function(){null===t&&(Ie(t),(0,s.$8)("Invalid telemetry item")),!t[l.RS]&&(0,s.hXl)(t[l.RS])&&(Ie(t),(0,s.$8)("telemetry name required")),t[l.FI]=t[l.FI]||W,t[l.fA]=t[l.fA]||(0,g._u)(new Date),t.ver=t.ver||"4.0",!X&&e[l.tZ]()&&te===c.f.ACTIVE?de()[l.$5](t):te!==c.f[l.Yq]&&L[l.oI]<=re&&L[l.y5](t)}),(function(){return{item:t}}),!t.sync)},e[l.DI]=de,e[l.RF]=function(){return U||(U=new b.h(t.cfg),e._notificationManager=U),U},e[l.vR]=function(t){e.getNotifyMgr()[l.vR](t)},e[l.h3]=function(e){U&&U[l.h3](e)},e.getCookieMgr=function(){return O||(O=(0,v.xN)(t.cfg,e[l.Uw])),O},e.setCookieMgr=function(e){O!==e&&((0,f.K)(O,!1),O=e)},e[y.kI]=function(){return N||H||(0,w.Z4)()},e.setPerfMgr=function(e){N=e},e.eventCnt=function(){return L[l.oI]},e.releaseQueue=function(){if(n&&L[l.oI]>0){var e=L;L=[],2===te?(0,s.Iuo)(e,(function(e){e[l.FI]=e[l.FI]||W,de()[l.$5](e)})):(0,m.ZP)(r,2,20,"core init status is not active")}},e[l.h4]=function(e){return B=e||null,se=!1,oe&&oe[l._w](),ce(!0)},e[l.Di]=function(){se=!0,oe&&oe[l._w](),ge()},(0,g.o$)(e,(function(){return q}),["addTelemetryInitializer"]),e[l.M5]=function(t,i,o){var u;void 0===t&&(t=!0),n||(0,s.$8)("SDK is not initialized"),X&&(0,s.$8)(T);var c,d=((u={reason:50})[l.tI]=t,u.flushComplete=!1,u);t&&!i&&(c=(0,a.Qo)((function(e){i=e})));var v=(0,I.tS)(pe(),e);function h(t){d.flushComplete=t,X=!0,K.run(v,d),e[l.Di](),v[l.$5](d)}return v[l.by]((function(){V.run(e[l.Uw]),(0,f.k)([O,U,r],t,(function(){fe(),i&&i(d)}))}),e),ge(),ye(t,h,6,o)||h(!1),c},e[l.AP]=he,e.addPlugin=function(e,t,n,r){if(!e)return r&&r(!1),void we(_);var i=he(e[l.Ju]);if(i&&!t)return r&&r(!1),void we("Plugin ["+e[l.Ju]+"] is already loaded!");var o={reason:16};function a(t){M[l.y5](e),o.added=[e],ve(o),r&&r(!0)}if(i){var s=[i.plugin];me(s,{reason:2,isAsync:!!n},(function(e){e?(o.removed=s,o.reason|=32,a()):r&&r(!1)}))}else a()},e.updateCfg=function(n,r){var i;if(void 0===r&&(r=!0),e[l.tZ]()){i={reason:1,cfg:t.cfg,oldCfg:(0,s.zwS)({},t.cfg),newConfig:(0,s.zwS)({},n),merge:r},n=i.newConfig;var o=t.cfg;n[y.jy]=o[y.jy],n[y.LZ]=o[y.LZ]}t._block((function(e){var t=e.cfg;R(e,t,n,r),r||(0,s.zav)(t,(function(r){(0,s.KhI)(n,r)||e.set(t,r,y.HP)})),e[l.h0](t,k)}),!0),t[l.zs](),i&&be(i)},e.evtNamespace=function(){return G},e.flush=ye,e.getTraceCtx=function(e){return Z||(Z=(0,S.u7)()),Z},e.setTraceCtx=function(e){Z=e||null},e.addUnloadHook=Se,(0,g.RF)(e,"addUnloadCb",(function(){return K}),"add"),e.onCfgChange=function(r){var i,o,a,c;return n?i=(0,u.a)(t.cfg,r,e[l.Uw]):((c=A(o=Q,a=r).l)||(c={w:a,rm:function(){var e=A(o,a);-1!==e.i&&o[l.Ic](e.i,1)}},o[l.y5](c)),i=c),function(e){return(0,s.vF1)({rm:function(){e.rm()}},"toJSON",{v:function(){return"aicore::onCfgChange<"+JSON.stringify(e)+">"}})}(i)},e.getWParam=function(){return(0,s.Wtk)()||t.cfg.enableWParam?0:-1}}))}return e.__ieDyn=1,e}()},4013:(e,t,n)=>{n.d(t,{K:()=>a,k:()=>s});var r=n(8205),i=n(269),o=n(6182);function a(e,t){if(e&&e[o.M5])return e[o.M5](t)}function s(e,t,n){var o;return n||(o=(0,r.Qo)((function(e){n=e}))),e&&(0,i.R3R)(e)>0?(0,r.Dv)(a(e[0],t),(function(){s((0,i.KVm)(e,1),t,n)})):n(),o}},8257:(e,t,n)=>{n.d(t,{s:()=>m});var r,i=n(8279),o=n(269),a=n(9749),s=n(6182),u=n(3775),c=n(3673),l=n(6492),f=n(2317),d=n(836),v=n(8969),h="getPlugin",p=((r={})[l.Bw]={isVal:c.Gh,v:{}},r),m=function(){function e(){var t,n,r,m,g,y=this;function b(e){void 0===e&&(e=null);var t=e;if(!t){var i=n||(0,f.i8)(null,{},y[l.eT]);t=r&&r[h]?i[s.$o](null,r[h]):i[s.$o](null,r)}return t}function w(e,t,i){(0,a.e)(e,p,(0,u.y0)(t)),!i&&t&&(i=t[s.DI]()[s.uR]());var o=r;r&&r[h]&&(o=r[h]()),y[l.eT]=t,n=(0,f.i8)(i,e,t,o)}function I(){t=!1,y[l.eT]=null,n=null,r=null,g=(0,v.w)(),m=(0,d.P)()}I(),(0,i.A)(e,y,(function(e){e[s.mE]=function(e,n,r,i){w(e,n,i),t=!0},e[s.Ik]=function(t,n){var i,o=e[l.eT];if(o&&(!t||o===t[l.eT]())){var a,u=!1,c=t||(0,f.tS)(null,o,r&&r[h]?r[h]():r),d=n||((i={reason:0})[s.tI]=!1,i);return e[s.tn]&&!0===e[s.tn](c,d,v)?a=!0:v(),a}function v(){u||(u=!0,m.run(c,n),g.run(c[s.e4]()),!0===a&&c[s.$5](d),I())}},e[s.HC]=function(t,n){var i=e[l.eT];if(i&&(!t||i===t[l.eT]())){var o,a=!1,u=t||(0,f.nU)(null,i,r&&r[h]?r[h]():r),c=n||{reason:0};return e._doUpdate&&!0===e._doUpdate(u,c,d)?o=!0:d(),o}function d(){a||(a=!0,w(u.getCfg(),u.core(),u[s.uR]()))}},(0,c.RF)(e,"_addUnloadCb",(function(){return m}),"add"),(0,c.RF)(e,"_addHook",(function(){return g}),"add"),(0,o.vF1)(e,"_unloadHooks",{g:function(){return g}})})),y[s.e4]=function(e){return b(e)[s.e4]()},y[s.tZ]=function(){return t},y.setInitialized=function(e){t=e},y[s.YH]=function(e){r=e},y[s.$5]=function(e,t){t?t[s.$5](e):r&&(0,o.Tnt)(r[l.qT])&&r[l.qT](e,null)},y._getTelCtx=b}return e.__ieDyn=1,e}()},7847:(e,t,n)=>{n.d(t,{i:()=>r,x:()=>i});var r=500,i="Microsoft_ApplicationInsights_BypassAjaxInstrumentation"},5034:(e,t,n)=>{n.d(t,{It:()=>M,gi:()=>L,um:()=>R,xN:()=>A});var r,i,o,a=n(269),s=n(2475),u=n(9749),c=n(6182),l=n(3775),f=n(7292),d=n(3673),v=n(6492),h="toGMTString",p="toUTCString",m="cookie",g="expires",y="isCookieUseDisabled",b="disableCookiesUsage",w="_ckMgr",I=null,S=null,P=null,C={},E={},_=((r={cookieCfg:(0,s.NU)((i={},i[v.Fk]={fb:"cookieDomain",dfVal:d.Gh},i.path={fb:"cookiePath",dfVal:d.Gh},i.enabled=v.HP,i.ignoreCookies=v.HP,i.blockedCookies=v.HP,i)),cookieDomain:v.HP,cookiePath:v.HP})[b]=v.HP,r);function T(){!o&&(o=(0,a.nRs)((function(){return(0,a.YEm)()})))}function k(e){return!e||e.isEnabled()}function D(e,t){return!!(t&&e&&(0,a.cyL)(e.ignoreCookies))&&-1!==(0,a.rDm)(e.ignoreCookies,t)}function x(e,t){var n=t[c.XM];if((0,a.hXl)(n)){var r=void 0;(0,a.b07)(e[y])||(r=!e[y]),(0,a.b07)(e[b])||(r=!e[b]),n=r}return n}function R(e,t){var n;if(e)n=e.getCookieMgr();else if(t){var r=t.cookieCfg;n=r&&r[w]?r[w]:A(t)}return n||(n=function(e,t){var n=A[w]||E[w];return n||(n=A[w]=A(e,t),E[w]=n),n}(t,(e||{})[c.Uw])),n}function A(e,t){var n,r,i,o,s,l,m,y,b;e=(0,u.e)(e||E,null,t).cfg,s=(0,u.a)(e,(function(t){t[c.h0](t.cfg,_),r=t.ref(t.cfg,"cookieCfg"),i=r[v.QW]||"/",o=r[v.Fk],l=!1!==x(e,r),m=r.getCookie||O,y=r.setCookie||$,b=r.delCookie||$}),t);var I=((n={isEnabled:function(){var n=!1!==x(e,r)&&l&&L(t),i=E[w];return n&&i&&I!==i&&(n=k(i)),n},setEnabled:function(e){l=!1!==e,r[c.XM]=e},set:function(e,t,n,s,u){var l=!1;if(k(I)&&!function(e,t){return!!(t&&e&&(0,a.cyL)(e.blockedCookies)&&-1!==(0,a.rDm)(e.blockedCookies,t))||D(e,t)}(r,e)){var m={},b=(0,a.EHq)(t||v.m5),w=(0,a.HzD)(b,";");if(-1!==w&&(b=(0,a.EHq)((0,a.ZWZ)(t,w)),m=U((0,a.P0f)(t,w+1))),(0,d.KY)(m,v.Fk,s||o,a.zzB,a.b07),!(0,a.hXl)(n)){var P=(0,f.lT)();if((0,a.b07)(m[g])){var C=(0,a.f0d)()+1e3*n;if(C>0){var E=new Date;E.setTime(C),(0,d.KY)(m,g,N(E,P?h:p)||N(E,P?h:p)||v.m5,a.zzB)}}P||(0,d.KY)(m,"max-age",v.m5+n,null,a.b07)}var _=(0,f.g$)();_&&"https:"===_[c.Qg]&&((0,d.KY)(m,"secure",null,null,a.b07),null===S&&(S=!M(((0,a.w3n)()||{})[c.tX])),S&&(0,d.KY)(m,"SameSite","None",null,a.b07)),(0,d.KY)(m,v.QW,u||i,null,a.b07),y(e,H(b,m)),l=!0}return l},get:function(e){var t=v.m5;return k(I)&&!D(r,e)&&(t=m(e)),t},del:function(e,t){var n=!1;return k(I)&&(n=I.purge(e,t)),n},purge:function(e,n){var r,i=!1;if(L(t)){var o=((r={})[v.QW]=n||"/",r[g]="Thu, 01 Jan 1970 00:00:01 GMT",r);(0,f.lT)()||(o["max-age"]="0"),b(e,H(v.m5,o)),i=!0}return i}})[c.M5]=function(e){s&&s.rm(),s=null},n);return I[w]=I,I}function L(e){if(null===I){I=!1,!o&&T();try{var t=o.v||{};I=void 0!==t[m]}catch(t){(0,l.ZP)(e,2,68,"Cannot access document.cookie - "+(0,d.lL)(t),{exception:(0,a.mmD)(t)})}}return I}function U(e){var t={};if(e&&e[c.oI]){var n=(0,a.EHq)(e)[c.sY](";");(0,a.Iuo)(n,(function(e){if(e=(0,a.EHq)(e||v.m5)){var n=(0,a.HzD)(e,"=");-1===n?t[e]=null:t[(0,a.EHq)((0,a.ZWZ)(e,n))]=(0,a.EHq)((0,a.P0f)(e,n+1))}}))}return t}function N(e,t){return(0,a.Tnt)(e[t])?e[t]():null}function H(e,t){var n=e||v.m5;return(0,a.zav)(t,(function(e,t){n+="; "+e+((0,a.hXl)(t)?v.m5:"="+t)})),n}function O(e){var t=v.m5;if(!o&&T(),o.v){var n=o.v[m]||v.m5;P!==n&&(C=U(n),P=n),t=(0,a.EHq)(C[e]||v.m5)}return t}function $(e,t){!o&&T(),o.v&&(o.v[m]=e+"="+t)}function M(e){return!(!(0,a.KgX)(e)||!(0,d.Ju)(e,"CPU iPhone OS 12")&&!(0,d.Ju)(e,"iPad; CPU OS 12")&&!((0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")&&(0,d.Ju)(e,"Version/")&&(0,d.Ju)(e,"Safari"))&&(!(0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")||!(0,a.Cv9)(e,"AppleWebKit/605.1.15 (KHTML, like Gecko)"))&&!(0,d.Ju)(e,"Chrome/5")&&!(0,d.Ju)(e,"Chrome/6")&&(!(0,d.Ju)(e,"UnrealEngine")||(0,d.Ju)(e,"Chrome"))&&!(0,d.Ju)(e,"UCBrowser/12")&&!(0,d.Ju)(e,"UCBrowser/11"))}},9882:(e,t,n)=>{n.d(t,{aq:()=>a,cL:()=>s});var r=n(269),i=n(6492),o=n(6535);function a(){var e=s();return(0,r.P0f)(e,0,8)+"-"+(0,r.P0f)(e,8,12)+"-"+(0,r.P0f)(e,12,16)+"-"+(0,r.P0f)(e,16,20)+"-"+(0,r.P0f)(e,20)}function s(){for(var e,t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],n=i.m5,a=0;a<4;a++)n+=t[15&(e=(0,o.VN)())]+t[e>>4&15]+t[e>>8&15]+t[e>>12&15]+t[e>>16&15]+t[e>>20&15]+t[e>>24&15]+t[e>>28&15];var s=t[8+(3&(0,o.VN)())|0];return(0,r.hKY)(n,0,8)+(0,r.hKY)(n,9,4)+"4"+(0,r.hKY)(n,13,3)+s+(0,r.hKY)(n,16,3)+(0,r.hKY)(n,19,12)}},4276:(e,t,n)=>{n.d(t,{T:()=>v,Z:()=>d});var r=n(269),i=n(6182),o=n(3673),a=n(6492),s=n(6535),u="3.3.4",c="."+(0,s.Si)(6),l=0;function f(e){return 1===e[i.re]||9===e[i.re]||!+e[i.re]}function d(e,t){return void 0===t&&(t=!1),(0,o.cH)(e+l+++(t?"."+u:a.m5)+c)}function v(e){var t={id:d("_aiData-"+(e||a.m5)+"."+u),accept:function(e){return f(e)},get:function(e,n,i,a){var s=e[t.id];return s?s[(0,o.cH)(n)]:(a&&(s=function(e,t){var n=t[e.id];if(!n){n={};try{f(t)&&(0,r.vF1)(t,e.id,{e:!1,v:n})}catch(e){}}return n}(t,e),s[(0,o.cH)(n)]=i),i)},kill:function(e,t){if(e&&e[t])try{delete e[t]}catch(e){}}};return t}},7867:(e,t,n)=>{n.d(t,{$:()=>l,M:()=>f});var r,i=n(269),o=n(6182),a=n(6492),s=[a.fc,a.Yp,a.dI,a.l0],u=null;function c(e,t){return function(){var n=arguments,r=l(t);if(r){var i=r.listener;i&&i[e]&&i[e][o.y9](i,n)}}}function l(e){var t,n=u;return n||!0===e.disableDbgExt||(n=u||((t=(0,i.zS2)("Microsoft"))&&(u=t.ApplicationInsights),u)),n?n.ChromeDbgExt:null}function f(e){if(!r){r={};for(var t=0;t<s[o.oI];t++)r[s[t]]=c(s[t],e)}return r}},3775:(e,t,n)=>{n.d(t,{OG:()=>I,Oc:()=>S,WD:()=>m,ZP:()=>w,wq:()=>y,y0:()=>g});var r,i=n(8279),o=n(269),a=n(9749),s=n(6182),u=n(7867),c=n(7292),l=n(6492),f="warnToConsole",d={loggingLevelConsole:0,loggingLevelTelemetry:1,maxMessageLimit:25,enableDebug:!1},v=((r={})[0]=null,r[1]="errorToConsole",r[2]=f,r[3]="debugToConsole",r);function h(e){return e?'"'+e[s.W7](/\"/g,l.m5)+'"':l.m5}function p(e,t){var n=(0,c.U5)();if(n){var r="log";n[e]&&(r=e),(0,o.Tnt)(n[r])&&n[r](t)}}var m=function(){function e(e,t,n,r){void 0===n&&(n=!1);var i=this;i[s.JR]=e,i[s.pM]=(n?"AI: ":"AI (Internal): ")+e;var o=l.m5;(0,c.Z)()&&(o=(0,c.hm)().stringify(r));var a=(t?" message:"+h(t):l.m5)+(r?" props:"+h(o):l.m5);i[s.pM]+=a}return e.dataType="MessageData",e}();function g(e,t){return(e||{})[s.Uw]||new y(t)}var y=function(){function e(t){this.identifier="DiagnosticLogger",this.queue=[];var n,r,c,l,h,g=0,y={};(0,i.A)(e,this,(function(e){function i(t,n){if(!(g>=c)){var i=!0,o="AITR_"+n[s.JR];if(y[o]?i=!1:y[o]=!0,i&&(t<=r&&(e.queue[s.y5](n),g++,b(1===t?"error":"warn",n)),g===c)){var a="Internal events throttle limit per PageView reached for this app.",u=new m(23,a,!1);e.queue[s.y5](u),1===t?e.errorToConsole(a):e[s.on](a)}}}function b(e,n){var r=(0,u.$)(t||{});r&&r[s.e4]&&r[s.e4](e,n)}h=function(t){return(0,a.a)((0,a.e)(t,d,e).cfg,(function(e){var t=e.cfg;n=t[s.Bl],r=t.loggingLevelTelemetry,c=t.maxMessageLimit,l=t.enableDebug}))}(t||{}),e.consoleLoggingLevel=function(){return n},e[s.ih]=function(t,r,a,u,c){void 0===c&&(c=!1);var d=new m(r,a,c,u);if(l)throw(0,o.mmD)(d);var h=v[t]||f;if((0,o.b07)(d[s.pM]))b("throw"+(1===t?"Critical":"Warning"),d);else{if(c){var p=+d[s.JR];!y[p]&&n>=t&&(e[h](d[s.pM]),y[p]=!0)}else n>=t&&e[h](d[s.pM]);i(t,d)}},e.debugToConsole=function(e){p("debug",e),b("warning",e)},e[s.on]=function(e){p("warn",e),b("warning",e)},e.errorToConsole=function(e){p("error",e),b("error",e)},e.resetInternalMessageCount=function(){g=0,y={}},e[s.sx]=i,e[s.M5]=function(e){h&&h.rm(),h=null}}))}return e.__ieDyn=1,e}();function b(e){return e||new y}function w(e,t,n,r,i,o){void 0===o&&(o=!1),b(e)[s.ih](t,n,r,i,o)}function I(e,t){b(e)[s.on](t)}function S(e,t,n){b(e)[s.sx](t,n)}},7292:(e,t,n)=>{n.d(t,{$Z:()=>M,Iu:()=>z,L0:()=>L,MY:()=>D,PV:()=>H,R7:()=>N,U5:()=>_,Uf:()=>U,Z:()=>T,cU:()=>C,g$:()=>E,hm:()=>k,iN:()=>x,lT:()=>A,lV:()=>R,xk:()=>O});var r=n(5664),i=n(269),o=n(6182),a=n(3673),s=n(6492),u="documentMode",c="location",l="console",f="JSON",d="crypto",v="msCrypto",h="ReactNative",p="msie",m="trident/",g="XMLHttpRequest",y=null,b=null,w=!1,I=null,S=null;function P(e,t){var n=!1;if(e){try{if(!(n=t in e)){var o=e[r.vR];o&&(n=t in o)}}catch(e){}if(!n)try{var a=new e;n=!(0,i.b07)(a[t])}catch(e){}}return n}function C(e){w=e}function E(e){if(e&&w){var t=(0,i.zS2)("__mockLocation");if(t)return t}return typeof location===r._1&&location?location:(0,i.zS2)(c)}function _(){return typeof console!==r.bA?console:(0,i.zS2)(l)}function T(){return Boolean(typeof JSON===r._1&&JSON||null!==(0,i.zS2)(f))}function k(){return T()?JSON||(0,i.zS2)(f):null}function D(){return(0,i.zS2)(d)}function x(){return(0,i.zS2)(v)}function R(){var e=(0,i.w3n)();return!(!e||!e.product)&&e.product===h}function A(){var e=(0,i.w3n)();if(e&&(e[o.tX]!==b||null===y)){var t=((b=e[o.tX])||s.m5)[o.OL]();y=(0,a.Ju)(t,p)||(0,a.Ju)(t,m)}return y}function L(e){if(void 0===e&&(e=null),!e){var t=(0,i.w3n)()||{};e=t?(t.userAgent||s.m5)[o.OL]():s.m5}var n=(e||s.m5)[o.OL]();if((0,a.Ju)(n,p)){var r=(0,i.YEm)()||{};return Math.max(parseInt(n[o.sY](p)[1]),r[u]||0)}if((0,a.Ju)(n,m)){var c=parseInt(n[o.sY](m)[1]);if(c)return c+4}return null}function U(e){return null!==S&&!1!==e||(S=(0,i.w9M)()&&Boolean((0,i.w3n)().sendBeacon)),S}function N(e){var t=!1;try{t=!!(0,i.zS2)("fetch");var n=(0,i.zS2)("Request");t&&e&&n&&(t=P(n,"keepalive"))}catch(e){}return t}function H(){return null===I&&(I=typeof XDomainRequest!==r.bA)&&O()&&(I=I&&!P((0,i.zS2)(g),"withCredentials")),I}function O(){var e=!1;try{e=!!(0,i.zS2)(g)}catch(e){}return e}function $(e,t){if(e)for(var n=0;n<e[o.oI];n++){var r=e[n];if(r[o.RS]&&r[o.RS]===t)return r}return{}}function M(e){var t=(0,i.YEm)();return t&&e?$(t.querySelectorAll("meta"),e).content:null}function z(e){var t,n=(0,i.FJj)();if(n){var r=n.getEntriesByType("navigation")||[];t=$((r[o.oI]>0?r[0]:{}).serverTiming,e).description}return t}},6149:(e,t,n)=>{n.d(t,{Ds:()=>X,El:()=>T,Fc:()=>q,Hm:()=>R,ML:()=>L,Q3:()=>U,So:()=>H,Wg:()=>z,Ym:()=>N,ee:()=>F,lQ:()=>M,mB:()=>A,oS:()=>G,sq:()=>B,vF:()=>K,zh:()=>O});var r=n(269),i=n(6182),o=n(4276),a=n(6492),s="on",u="attachEvent",c="addEventListener",l="detachEvent",f="removeEventListener",d="events",v="visibilitychange",h="pagehide",p="pageshow",m="unload",g="beforeunload",y=(0,o.Z)("aiEvtPageHide"),b=(0,o.Z)("aiEvtPageShow"),w=/\.[\.]+/g,I=/[\.]+$/,S=1,P=(0,o.T)("events"),C=/^([^.]*)(?:\.(.+)|)/;function E(e){return e&&e[i.W7]?e[i.W7](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,a.m5):e}function _(e,t){var n;if(t){var o=a.m5;(0,r.cyL)(t)?(o=a.m5,(0,r.Iuo)(t,(function(e){(e=E(e))&&("."!==e[0]&&(e="."+e),o+=e)}))):o=E(t),o&&("."!==o[0]&&(o="."+o),e=(e||a.m5)+o)}var s=C.exec(e||a.m5)||[];return(n={})[i.QM]=s[1],n.ns=(s[2]||a.m5).replace(w,".").replace(I,a.m5)[i.sY](".").sort().join("."),n}function T(e,t,n){var o=[],s=P.get(e,d,{},!1),u=_(t,n);return(0,r.zav)(s,(function(e,t){(0,r.Iuo)(t,(function(e){var t;u[i.QM]&&u[i.QM]!==e.evtName[i.QM]||u.ns&&u.ns!=u.ns||o[i.y5](((t={})[i.RS]=e.evtName[i.QM]+(e.evtName.ns?"."+e.evtName.ns:a.m5),t.handler=e[i.Yo],t))}))})),o}function k(e,t,n){void 0===n&&(n=!0);var r=P.get(e,d,{},n),i=r[t];return i||(i=r[t]=[]),i}function D(e,t,n,r){e&&t&&t[i.QM]&&(e[f]?e[f](t[i.QM],n,r):e[l]&&e[l](s+t[i.QM],n))}function x(e,t,n,r){for(var o=t[i.oI];o--;){var a=t[o];a&&(n.ns&&n.ns!==a.evtName.ns||r&&!r(a)||(D(e,a.evtName,a[i.Yo],a.capture),t[i.Ic](o,1)))}}function R(e,t){return t?_("xx",(0,r.cyL)(t)?[e].concat(t):[e,t]).ns[i.sY]("."):e}function A(e,t,n,r,o){var a;void 0===o&&(o=!1);var l=!1;if(e)try{var f=_(t,r);if(l=function(e,t,n,r){var o=!1;return e&&t&&t[i.QM]&&n&&(e[c]?(e[c](t[i.QM],n,r),o=!0):e[u]&&(e[u](s+t[i.QM],n),o=!0)),o}(e,f,n,o),l&&P.accept(e)){var d=((a={guid:S++,evtName:f})[i.Yo]=n,a.capture=o,a);k(e,f.type)[i.y5](d)}}catch(e){}return l}function L(e,t,n,o,a){if(void 0===a&&(a=!1),e)try{var s=_(t,o),u=!1;!function(e,t,n){if(t[i.QM])x(e,k(e,t[i.QM]),t,n);else{var o=P.get(e,d,{});(0,r.zav)(o,(function(r,i){x(e,i,t,n)})),0===(0,r.cGk)(o)[i.oI]&&P.kill(e,d)}}(e,s,(function(e){return!((!s.ns||n)&&e[i.Yo]!==n||(u=!0,0))})),u||D(e,s,n,a)}catch(e){}}function U(e,t,n,r){return void 0===r&&(r=!1),A(e,t,n,null,r)}function N(e,t,n,r){void 0===r&&(r=!1),L(e,t,n,null,r)}function H(e,t,n){var i=!1,o=(0,r.zkX)();o&&(i=A(o,e,t,n),i=A(o.body,e,t,n)||i);var a=(0,r.YEm)();return a&&(i=A(a,e,t,n)||i),i}function O(e,t,n){var i=(0,r.zkX)();i&&(L(i,e,t,n),L(i.body,e,t,n));var o=(0,r.YEm)();o&&L(o,e,t,n)}function $(e,t,n,o){var a=!1;return t&&e&&e[i.oI]>0&&(0,r.Iuo)(e,(function(e){e&&(n&&-1!==(0,r.rDm)(n,e)||(a=H(e,t,o)||a))})),a}function M(e,t,n,o){var a=!1;return t&&e&&(0,r.cyL)(e)&&!(a=$(e,t,n,o))&&n&&n[i.oI]>0&&(a=$(e,t,null,o)),a}function z(e,t,n){e&&(0,r.cyL)(e)&&(0,r.Iuo)(e,(function(e){e&&O(e,t,n)}))}function F(e,t,n){return M([g,m,h],e,t,n)}function X(e,t){z([g,m,h],e,t)}function q(e,t,n){var i=R(y,n),o=$([h],e,t,i);return t&&-1!==(0,r.rDm)(t,v)||(o=$([v],(function(t){var n=(0,r.YEm)();e&&n&&"hidden"===n.visibilityState&&e(t)}),t,i)||o),!o&&t&&(o=q(e,null,n)),o}function B(e,t){var n=R(y,t);z([h],e,n),z([v],null,n)}function G(e,t,n){var i=R(b,n),o=$([p],e,t,i);return!(o=$([v],(function(t){var n=(0,r.YEm)();e&&n&&"visible"===n.visibilityState&&e(t)}),t,i)||o)&&t&&(o=G(e,null,n)),o}function K(e,t){var n=R(b,t);z([p],e,n),z([v],null,n)}},3673:(e,t,n)=>{n.d(t,{CP:()=>P,Gh:()=>l,H$:()=>R,HU:()=>E,IL:()=>A,Ju:()=>d,KY:()=>p,LU:()=>L,Lo:()=>C,RF:()=>b,SZ:()=>I,_u:()=>v,c2:()=>m,cH:()=>f,hW:()=>S,jL:()=>T,lL:()=>h,o$:()=>w,qz:()=>y,r4:()=>_,w3:()=>O});var r=n(269),i=n(5664),o=n(6182),a=n(6492),s=/-([a-z])/g,u=/([^\w\d_$])/g,c=/^(\d+[\w\d_$])/;function l(e){return!(0,r.hXl)(e)}function f(e){var t=e;return t&&(0,r.KgX)(t)&&(t=(t=(t=t[o.W7](s,(function(e,t){return t.toUpperCase()})))[o.W7](u,"_"))[o.W7](c,(function(e,t){return"_"+t}))),t}function d(e,t){return!(!e||!t)&&-1!==(0,r.HzD)(e,t)}function v(e){return e&&e.toISOString()||""}function h(e){return(0,r.bJ7)(e)?e[o.RS]:a.m5}function p(e,t,n,r,i){var o=n;return e&&((o=e[t])===n||i&&!i(o)||r&&!r(n)||(o=n,e[t]=o)),o}function m(e,t,n){var i;return e?!(i=e[t])&&(0,r.hXl)(i)&&(i=(0,r.b07)(n)?{}:n,e[t]=i):i=(0,r.b07)(n)?{}:n,i}function g(e,t){var n=null,i=null;return(0,r.Tnt)(e)?n=e:i=e,function(){var e=arguments;if(n&&(i=n()),i)return i[t][o.y9](i,e)}}function y(e,t,n){if(e&&t&&(0,r.Gvm)(e)&&(0,r.Gvm)(t)){var i=function(i){if((0,r.KgX)(i)){var o=t[i];(0,r.Tnt)(o)?n&&!n(i,!0,t,e)||(e[i]=g(t,i)):n&&!n(i,!1,t,e)||((0,r.KhI)(e,i)&&delete e[i],(0,r.vF1)(e,i,{g:function(){return t[i]},s:function(e){t[i]=e}}))}};for(var o in t)i(o)}return e}function b(e,t,n,i,o){e&&t&&n&&(!1!==o||(0,r.b07)(e[t]))&&(e[t]=g(n,i))}function w(e,t,n,i){return e&&t&&(0,r.Gvm)(e)&&(0,r.cyL)(n)&&(0,r.Iuo)(n,(function(n){(0,r.KgX)(n)&&b(e,n,t,n,i)})),e}function I(e){return function(){var t=this;e&&(0,r.zav)(e,(function(e,n){t[e]=n}))}}function S(e){return e&&r.vE3&&(e=(0,i.s6)((0,r.vE3)({},e))),e}function P(e,t,n,i,a,s){var u=arguments,c=u[0]||{},l=u[o.oI],f=!1,d=1;for(l>0&&(0,r.Lmq)(c)&&(f=c,c=u[d]||{},d++),(0,r.Gvm)(c)||(c={});d<l;d++){var v=u[d],h=(0,r.cyL)(v),p=(0,r.Gvm)(v);for(var m in v)if(h&&m in v||p&&(0,r.KhI)(v,m)){var g=v[m],y=void 0;if(f&&g&&((y=(0,r.cyL)(g))||(0,r.QdQ)(g))){var b=c[m];y?(0,r.cyL)(b)||(b=[]):(0,r.QdQ)(b)||(b={}),g=P(f,b,g)}void 0!==g&&(c[m]=g)}}return c}function C(e){try{return e.responseText}catch(e){}return null}function E(e,t){return e?"XDomainRequest,Response:"+C(e)||0:t}function _(e,t){return e?"XMLHttpRequest,Status:"+e[o.cV]+",Response:"+C(e)||0:t}function T(e,t){return t&&((0,r.EtT)(t)?e=[t].concat(e):(0,r.cyL)(t)&&(e=t.concat(e))),e}Object.getPrototypeOf;var k="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",D="withCredentials",x="timeout";function R(e,t,n,r,i,o){function a(e,t,n){try{e[t]=n}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var s=new XMLHttpRequest;return r&&a(s,k,r),n&&a(s,D,n),s.open(e,t,!i),n&&a(s,D,n),!i&&o&&a(s,x,o),s}function A(e){var t={};if((0,r.KgX)(e)){var n=(0,r.EHq)(e)[o.sY](/[\r\n]+/);(0,r.Iuo)(n,(function(e){if(e){var n=e.indexOf(": ");if(-1!==n){var i=(0,r.EHq)(e.substring(0,n))[o.OL](),a=(0,r.EHq)(e.substring(n+1));t[i]=a}else t[(0,r.EHq)(e)]=1}}))}return t}function L(e,t,n){if(!e[n]&&t&&t[o.Az]){var i=t[o.Az](n);i&&(e[n]=(0,r.EHq)(i))}return e}var U="kill-duration",N="kill-duration-seconds",H="time-delta-millis";function O(e,t){var n={};return e[o.wJ]?n=A(e[o.wJ]()):t&&(n=L(n,e,H),n=L(n,e,U),n=L(n,e,N)),n}},6492:(e,t,n)=>{n.d(t,{Bw:()=>c,Ev:()=>b,Fk:()=>I,HP:()=>r,Hr:()=>u,LZ:()=>o,QW:()=>S,Vj:()=>y,Vo:()=>d,Yd:()=>s,Yp:()=>h,dI:()=>p,eT:()=>a,fc:()=>v,jy:()=>l,kI:()=>w,l0:()=>m,m5:()=>i,qT:()=>f,s4:()=>g,xW:()=>P});var r=void 0,i="",o="channels",a="core",s="createPerfMgr",u="disabled",c="extensionConfig",l="extensions",f="processTelemetry",d="priority",v="eventsSent",h="eventsDiscarded",p="eventsSendRequest",m="perfEvent",g="offlineEventsStored",y="offlineBatchSent",b="offlineBatchDrop",w="getPerfMgr",I="domain",S="path",P="Not dynamic - "},1356:(e,t,n)=>{n.d(t,{h:()=>d});var r=n(8279),i=n(8205),o=n(269),a=n(9749),s=n(6182),u=n(6492),c={perfEvtsSendAll:!1};function l(e){e.h=null;var t=e.cb;e.cb=[],(0,o.Iuo)(t,(function(e){(0,o.gBW)(e.fn,[e.arg])}))}function f(e,t,n,r){(0,o.Iuo)(e,(function(e){e&&e[t]&&(n?(n.cb[s.y5]({fn:r,arg:e}),n.h=n.h||(0,o.dRz)(l,0,n)):(0,o.gBW)(r,[e]))}))}var d=function(){function e(t){var n,l;this.listeners=[];var d=[],v={h:null,cb:[]},h=(0,a.e)(t,c);l=h[s.x6]((function(e){n=!!e.cfg.perfEvtsSendAll})),(0,r.A)(e,this,(function(e){(0,o.vF1)(e,"listeners",{g:function(){return d}}),e[s.vR]=function(e){d[s.y5](e)},e[s.h3]=function(e){for(var t=(0,o.rDm)(d,e);t>-1;)d[s.Ic](t,1),t=(0,o.rDm)(d,e)},e[u.fc]=function(e){f(d,u.fc,v,(function(t){t[u.fc](e)}))},e[u.Yp]=function(e,t){f(d,u.Yp,v,(function(n){n[u.Yp](e,t)}))},e[u.dI]=function(e,t){f(d,u.dI,t?v:null,(function(n){n[u.dI](e,t)}))},e[u.l0]=function(e){e&&(!n&&e[s.Zu]()||f(d,u.l0,null,(function(t){e[s.tI]?(0,o.dRz)((function(){return t[u.l0](e)}),0):t[u.l0](e)})))},e[u.s4]=function(e){e&&e[s.oI]&&f(d,u.s4,v,(function(t){t[u.s4](e)}))},e[u.Vj]=function(e){e&&e[s.Cd]&&f(d,u.Vj,v,(function(t){t[u.Vj](e)}))},e[u.Ev]=function(e,t){if(e>0){var n=t||0;f(d,u.Ev,v,(function(t){t[u.Ev](e,n)}))}},e[s.M5]=function(e){var t,n=function(){l&&l.rm(),l=null,d=[],v.h&&v.h[s._w](),v.h=null,v.cb=[]};if(f(d,"unload",null,(function(n){var r=n[s.M5](e);r&&(t||(t=[]),t[s.y5](r))})),t)return(0,i.Qo)((function(e){return(0,i.Dv)((0,i.Xf)(t),(function(){n(),e()}))}));n()}}))}return e.__ieDyn=1,e}()},8156:(e,t,n)=>{n.d(t,{NS:()=>d,Q6:()=>f,Z4:()=>p,r2:()=>h});var r=n(8279),i=n(269),o=n(6182),a=n(6492),s="ctx",u="ParentContextKey",c="ChildrenContextKey",l=null,f=function(){function e(t,n,r){var a,l=this;l.start=(0,i.f0d)(),l[o.RS]=t,l[o.tI]=r,l[o.Zu]=function(){return!1},(0,i.Tnt)(n)&&(0,i.vF1)(l,"payload",{g:function(){return!a&&(0,i.Tnt)(n)&&(a=n(),n=null),a}}),l[o.O_]=function(t){return t?t===e[u]||t===e[c]?l[t]:(l[s]||{})[t]:null},l[o.e_]=function(t,n){t&&(t===e[u]?(l[t]||(l[o.Zu]=function(){return!0}),l[t]=n):t===e[c]?l[t]=n:(l[s]=l[s]||{})[t]=n)},l[o.Ru]=function(){var t=0,n=l[o.O_](e[c]);if((0,i.cyL)(n))for(var r=0;r<n[o.oI];r++){var a=n[r];a&&(t+=a[o.fA])}l[o.fA]=(0,i.f0d)()-l.start,l.exTime=l[o.fA]-t,l[o.Ru]=function(){}}}return e.ParentContextKey="parent",e.ChildrenContextKey="childEvts",e}(),d=function(){function e(t){this.ctx={},(0,r.A)(e,this,(function(e){e.create=function(e,t,n){return new f(e,t,n)},e.fire=function(e){e&&(e[o.Ru](),t&&(0,i.Tnt)(t[a.l0])&&t[a.l0](e))},e[o.e_]=function(t,n){t&&((e[s]=e[s]||{})[t]=n)},e[o.O_]=function(t){return(e[s]||{})[t]}}))}return e.__ieDyn=1,e}(),v="CoreUtils.doPerf";function h(e,t,n,r,i){if(e){var s=e;if(s[a.kI]&&(s=s[a.kI]()),s){var l=void 0,d=s[o.O_](v);try{if(l=s.create(t(),r,i)){if(d&&l[o.e_]&&(l[o.e_](f[u],d),d[o.O_]&&d[o.e_])){var h=d[o.O_](f[c]);h||(h=[],d[o.e_](f[c],h)),h[o.y5](l)}return s[o.e_](v,l),n(l)}}catch(e){l&&l[o.e_]&&l[o.e_]("exception",e)}finally{l&&s.fire(l),s[o.e_](v,d)}}}return n()}function p(){return l}},2317:(e,t,n)=>{n.d(t,{PV:()=>w,W0:()=>I,i8:()=>g,nU:()=>b,tS:()=>y});var r=n(269),i=n(991),o=n(9749),a=n(6182),s=n(3775),u=n(3673),c=n(6492),l=n(8156),f=n(380),d="TelemetryPluginChain",v="_hasRun",h="_getTelCtx",p=0;function m(e,t,n,u){var l=null,f=[];t||(t=(0,o.e)({},null,n[a.Uw])),null!==u&&(l=u?function(e,t,n){for(;e;){if(e[a.AP]()===n)return e;e=e[a.uR]()}return w([n],t.config||{},t)}(e,n,u):e);var d={_next:function(){var e=l;if(l=e?e[a.uR]():null,!e){var t=f;t&&t[a.oI]>0&&((0,r.Iuo)(t,(function(e){try{e.func.call(e.self,e.args)}catch(e){(0,s.ZP)(n[a.Uw],2,73,"Unexpected Exception during onComplete - "+(0,r.mmD)(e))}})),f=[])}return e},ctx:{core:function(){return n},diagLog:function(){return(0,s.y0)(n,t.cfg)},getCfg:function(){return t.cfg},getExtCfg:function(e,n){var o=v(e,!0);return n&&(0,r.zav)(n,(function(e,n){if((0,r.hXl)(o[e])){var a=t.cfg[e];!a&&(0,r.hXl)(a)||(o[e]=a)}(0,i.q)(t,o,e,n)})),t[a.h0](o,n)},getConfig:function(e,n,i){void 0===i&&(i=!1);var o,a=v(e,!1),s=t.cfg;return!a||!a[n]&&(0,r.hXl)(a[n])?!s[n]&&(0,r.hXl)(s[n])||(o=s[n]):o=a[n],o||!(0,r.hXl)(o)?o:i},hasNext:function(){return!!l},getNext:function(){return l},setNext:function(e){l=e},iterate:function(e){for(var t;t=d._next();){var n=t[a.AP]();n&&e(n)}},onComplete:function(e,t){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e&&f[a.y5]({func:e,self:(0,r.b07)(t)?d.ctx:t,args:n})}}};function v(e,n){var r=null,i=t.cfg;if(i&&e){var o=i[c.Bw];!o&&n&&(o={}),i[c.Bw]=o,(o=t.ref(i,c.Bw))&&(!(r=o[e])&&n&&(r={}),o[e]=r,r=t.ref(o,e))}return r}return d}function g(e,t,n,i){var s=(0,o.e)(t),u=m(e,s,n,i),l=u.ctx;return l[a.$5]=function(e){var t=u._next();return t&&t[c.qT](e,l),!t},l[a.$o]=function(e,t){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,s.cfg,n,t)),g(e||l[a.uR](),s.cfg,n,t)},l}function y(e,t,n){var i=(0,o.e)(t.config),s=m(e,i,t,n),u=s.ctx;return u[a.$5]=function(e){var t=s._next();return t&&t[a.M5](u,e),!t},u[a.$o]=function(e,n){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,i.cfg,t,n)),y(e||u[a.uR](),t,n)},u}function b(e,t,n){var i=(0,o.e)(t.config),s=m(e,i,t,n).ctx;return s[a.$5]=function(e){return s.iterate((function(t){(0,r.Tnt)(t[a.HC])&&t[a.HC](s,e)}))},s[a.$o]=function(e,n){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,i.cfg,t,n)),b(e||s[a.uR](),t,n)},s}function w(e,t,n,i){var o=null,u=!i;if((0,r.cyL)(e)&&e[a.oI]>0){var m=null;(0,r.Iuo)(e,(function(e){if(u||i!==e||(u=!0),u&&e&&(0,r.Tnt)(e[c.qT])){var y=function(e,t,n){var i,o=null,u=(0,r.Tnt)(e[c.qT]),m=(0,r.Tnt)(e[a.YH]),y={getPlugin:function(){return e},getNext:function(){return o},processTelemetry:function(i,s){b(s=s||function(){var i;return e&&(0,r.Tnt)(e[h])&&(i=e[h]()),i||(i=g(y,t,n)),i}(),(function(t){if(!e||!u)return!1;var n=(0,f.Cr)(e);return!n[a.Ik]&&!n[c.Hr]&&(m&&e[a.YH](o),e[c.qT](i,t),!0)}),"processTelemetry",(function(){return{item:i}}),!i.sync)||s[a.$5](i)},unload:function(t,n){b(t,(function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[c.eT]||i[c.eT];!e||o&&o!==t.core()||i[a.Ik]||(i[c.eT]=null,i[a.Ik]=!0,i[a.tZ]=!1,e[a.Ik]&&!0===e[a.Ik](t,n)&&(r=!0))}return r}),"unload",(function(){}),n[a.tI])||t[a.$5](n)},update:function(t,n){b(t,(function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[c.eT]||i[c.eT];!e||o&&o!==t.core()||i[a.Ik]||e[a.HC]&&!0===e[a.HC](t,n)&&(r=!0)}return r}),"update",(function(){}),!1)||t[a.$5](n)},_id:i=e?e[a.Ju]+"-"+e[c.Vo]+"-"+p++:"Unknown-0-"+p++,_setNext:function(e){o=e}};function b(t,n,u,f,h){var p=!1,m=e?e[a.Ju]:d,g=t[v];return g||(g=t[v]={}),t.setNext(o),e&&(0,l.r2)(t[c.eT](),(function(){return m+":"+u}),(function(){g[i]=!0;try{var e=o?o._id:c.m5;e&&(g[e]=!1),p=n(t)}catch(e){var l=!o||g[o._id];l&&(p=!0),o&&l||(0,s.ZP)(t[a.e4](),1,73,"Plugin ["+m+"] failed during "+u+" - "+(0,r.mmD)(e)+", run flags: "+(0,r.mmD)(g))}}),f,h),p}return(0,r.N6t)(y)}(e,t,n);o||(o=y),m&&m._setNext(y),m=y}}))}return i&&!o?w([i],t,n):o}var I=function(e,t,n,i){var o=g(e,t,n,i);(0,u.o$)(this,o,(0,r.cGk)(o))}},6535:(e,t,n)=>{n.d(t,{Si:()=>g,VN:()=>m,Z1:()=>p});var r=n(269),i=n(6182),o=n(7292),a=n(6492),s=4294967296,u=4294967295,c=123456789,l=987654321,f=!1,d=c,v=l;function h(){try{var e=2147483647&(0,r.f0d)();(t=(Math.random()*s^e)+e)<0&&(t>>>=0),d=c+t&u,v=l-t&u,f=!0}catch(e){}var t}function p(e){return e>0?Math.floor(m()/u*(e+1))>>>0:0}function m(e){var t=0,n=(0,o.MY)()||(0,o.iN)();return n&&n.getRandomValues&&(t=n.getRandomValues(new Uint32Array(1))[0]&u),0===t&&(0,o.lT)()&&(f||h(),t=function(e){var t=((v=36969*(65535&v)+(v>>16)&u)<<16)+(65535&(d=18e3*(65535&d)+(d>>16)&u))>>>0&u;return t>>>=0}()&u),0===t&&(t=Math.floor(s*Math.random()|0)),e||(t>>>=0),t}function g(e){void 0===e&&(e=22);for(var t=m()>>>0,n=0,r=a.m5;r[i.oI]<e;)n++,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&t),t>>>=6,5===n&&(t=(m()<<2&4294967295|3&t)>>>0,n=0);return r}},1190:(e,t,n)=>{n.d(t,{x:()=>s});var r=n(269),i=n(6182),o=n(3775),a=n(7292);function s(e,t){try{if(e&&""!==e){var n=(0,a.hm)().parse(e);if(n&&n[i.cp]&&n[i.cp]>=n.itemsAccepted&&n.itemsReceived-n.itemsAccepted===n.errors[i.oI])return n}}catch(n){(0,o.ZP)(t,1,43,"Cannot parse the response. "+(n[i.RS]||(0,r.mmD)(n)),{response:e})}return null}},856:(e,t,n)=>{n.d(t,{v:()=>h});var r=n(8279),i=n(8205),o=n(269),a=n(6182),s=n(7847),u=n(3775),c=n(7292),l=n(3673),f="",d="&NoResponseBody=true",v="POST",h=function(){function e(){var t,n,h,p,m,g,y,b,w,I,S,P,C,E,_=0;(0,r.A)(e,this,(function(e,r){var T=!0;function k(e,t){(0,u.ZP)(h,2,26,"Failed to send telemetry.",{message:e}),x(t,400,{})}function D(e){k("No endpoint url is provided for the batch",e)}function x(e,t,n,r){try{e&&e(t,n,r)}catch(e){}}function R(e,t){var n=(0,o.w3n)(),r=e[a.Vq];if(!r)return D(t),!0;r=e[a.Vq]+(C?d:f);var i=e[a.Cd],s=p?i:new Blob([i],{type:"text/plain;charset=UTF-8"});return n.sendBeacon(r,s)}function A(e,t,n){var r=e[a.Cd];try{if(r)if(R(e,t))x(t,200,{},f);else{var i=m&&m.beaconOnRetry;i&&(0,o.Tnt)(i)?i(e,t,R):(b&&b[a.L](e,t,!0),(0,u.ZP)(h,2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}catch(e){p&&(0,u.OG)(h,"Failed to send telemetry using sendBeacon API. Ex:"+(0,o.mmD)(e)),x(t,p?0:400,{},f)}}function L(e,n,r){var s,u,c,d=e[a.c1]||{};!r&&t&&(s=(0,i.Qo)((function(e,t){u=e,c=t}))),p&&r&&e.disableXhrSync&&(r=!1);var h=e[a.Vq];if(!h)return D(n),void(u&&u(!1));var g=(0,l.H$)(v,h,T,!0,r,e[a.do]);function y(t){var r=m&&m.xhrOnComplete;if(r&&(0,o.Tnt)(r))r(t,n,e);else{var i=(0,l.Lo)(t);x(n,t[a.cV],(0,l.w3)(t,p),i)}}return p||g[a.yy]("Content-type","application/json"),(0,o.Iuo)((0,o.cGk)(d),(function(e){g[a.yy](e,d[e])})),g.onreadystatechange=function(){p||(y(g),4===g.readyState&&u&&u(!0))},g.onload=function(){p&&y(g)},g.onerror=function(e){x(n,p?g[a.cV]:400,(0,l.w3)(g,p),p?f:(0,l.r4)(g)),c&&c(e)},g.ontimeout=function(){x(n,p?g[a.cV]:500,(0,l.w3)(g,p),p?f:(0,l.r4)(g)),u&&u(!1)},g.send(e[a.Cd]),s}function U(e,n,r){var u,c,l,h,g=e[a.Vq],b=e[a.Cd],w=p?b:new Blob([b],{type:"application/json"}),I=new Headers,S=b[a.oI],P=!1,k=!1,R=e[a.c1]||{},A=((u={method:v,body:w})[s.x]=!0,u);e.headers&&(0,o.cGk)(e.headers)[a.oI]>0&&((0,o.Iuo)((0,o.cGk)(R),(function(e){I.append(e,R[e])})),A[a.c1]=I),y?A.credentials=y:T&&p&&(A.credentials="include"),r&&(A.keepalive=!0,_+=S,p?2===e._sendReason&&(P=!0,C&&(g+=d)):P=!0);var L=new Request(g,A);try{L[s.x]=!0}catch(e){}if(!r&&t&&(c=(0,i.Qo)((function(e,t){l=e,h=t}))),!g)return D(n),void(l&&l(!1));function U(e){x(n,p?0:400,{},p?f:e)}function N(e,t,r){var i=e[a.cV],s=m.fetchOnComplete;s&&(0,o.Tnt)(s)?s(e,n,r||f,t):x(n,i,{},r||f)}try{(0,i.Dv)(fetch(p?g:L,p?A:null),(function(t){if(r&&(_-=S,S=0),!k)if(k=!0,t.rejected)U(t.reason&&t.reason[a.pM]),h&&h(t.reason);else{var n=t[a.pF];try{p||n.ok?p&&!n.body?(N(n,null,f),l&&l(!0)):(0,i.Dv)(n.text(),(function(t){N(n,e,t[a.pF]),l&&l(!0)})):(U(n.statusText),l&&l(!1))}catch(e){U((0,o.mmD)(e)),h&&h(e)}}}))}catch(e){k||(U((0,o.mmD)(e)),h&&h(e))}return P&&!k&&(k=!0,x(n,200,{}),l&&l(!0)),p&&!k&&e[a.do]>0&&E&&E.set((function(){k||(k=!0,x(n,500,{}),l&&l(!0))}),e[a.do]),c}function N(e,t,n){var r=(0,o.zkX)(),i=new XDomainRequest,s=e[a.Cd];i.onload=function(){var n=(0,l.Lo)(i),r=m&&m.xdrOnComplete;r&&(0,o.Tnt)(r)?r(i,t,e):x(t,200,{},n)},i.onerror=function(){x(t,400,{},p?f:(0,l.HU)(i))},i.ontimeout=function(){x(t,500,{})},i.onprogress=function(){};var c=r&&r.location&&r.location[a.Qg]||"",d=e[a.Vq];if(d){if(!p&&0!==d.lastIndexOf(c,0)){var g="Cannot send XDomain request. The endpoint URL protocol doesn't match the hosting page protocol.";return(0,u.ZP)(h,2,40,". "+g),void k(g,t)}var y=p?d:d[a.W7](/^(https?:)/,"");i.open(v,y),e[a.do]&&(i[a.do]=e[a.do]),i.send(s),p&&n?E&&E.set((function(){i.send(s)}),0):i.send(s)}else D(t)}function H(){_=0,n=!1,t=!1,h=null,p=null,m=null,g=null,y=null,b=null,w=!1,I=!1,S=!1,P=!1,C=!1,E=null}H(),e[a.mE]=function(t,r){h=r,n&&(0,u.ZP)(h,1,28,"Sender is already initialized"),e.SetConfig(t),n=!0},e._getDbgPlgTargets=function(){return[n,p,g,t]},e.SetConfig=function(e){try{if(m=e.senderOnCompleteCallBack||{},g=!!e.disableCredentials,y=e.fetchCredentials,p=!!e.isOneDs,t=!!e.enableSendPromise,w=!!e.disableXhr,I=!!e.disableBeacon,S=!!e.disableBeaconSync,E=e.timeWrapper,C=!!e.addNoResponse,P=!!e.disableFetchKeepAlive,b={sendPOST:L},p||(T=!1),g){var n=(0,c.g$)();n&&n.protocol&&"file:"===n.protocol[a.OL]()&&(T=!1)}return!0}catch(e){}return!1},e.getSyncFetchPayload=function(){return _},e.getSenderInst=function(e,t){return e&&e[a.oI]?function(e,t){for(var n,r=0,i=null,o=0;null==i&&o<e[a.oI];)r=e[o],w||1!==r?2!==r||!(0,c.R7)(t)||t&&P?3!==r||!(0,c.Uf)()||(t?S:I)||(i=A):i=U:(0,c.PV)()?i=N:(0,c.xk)()&&(i=L),o++;return i?((n={_transport:r,_isSync:t})[a.L]=i,n):null}(e,t):null},e.getFallbackInst=function(){return b},e[a.tn]=function(e,t){H()}}))}return e.__ieDyn=1,e}()},380:(e,t,n)=>{n.d(t,{Cr:()=>c,Xc:()=>f,pI:()=>l,u7:()=>d});var r=n(269),i=n(6182),o=n(4276),a=n(6492),s=n(1864),u=(0,o.T)("plugin");function c(e){return u.get(e,"state",{},!0)}function l(e,t){for(var n,o=[],s=null,u=e[i.uR]();u;){var l=u[i.AP]();if(l){s&&s[i.YH]&&l[a.qT]&&s[i.YH](l);var f=!!(n=c(l))[i.tZ];l[i.tZ]&&(f=l[i.tZ]()),f||o[i.y5](l),s=l,u=u[i.uR]()}}(0,r.Iuo)(o,(function(r){var o=e[a.eT]();r[i.mE](e.getCfg(),o,t,e[i.uR]()),n=c(r),r[a.eT]||n[a.eT]||(n[a.eT]=o),n[i.tZ]=!0,delete n[i.Ik]}))}function f(e){return e.sort((function(e,t){var n=0;if(t){var r=t[a.qT];e[a.qT]?n=r?e[a.Vo]-t[a.Vo]:1:r&&(n=-1)}else n=e?1:-1;return n}))}function d(e){var t={};return{getName:function(){return t[i.RS]},setName:function(n){e&&e.setName(n),t[i.RS]=n},getTraceId:function(){return t[i.P5]},setTraceId:function(n){e&&e.setTraceId(n),(0,s.hX)(n)&&(t[i.P5]=n)},getSpanId:function(){return t[i.wi]},setSpanId:function(n){e&&e.setSpanId(n),(0,s.wN)(n)&&(t[i.wi]=n)},getTraceFlags:function(){return t[i.Rr]},setTraceFlags:function(n){e&&e.setTraceFlags(n),t[i.Rr]=n}}}},836:(e,t,n)=>{n.d(t,{P:()=>a});var r=n(269),i=n(6182),o=n(3775);function a(){var e=[];return{add:function(t){t&&e[i.y5](t)},run:function(t,n){(0,r.Iuo)(e,(function(e){try{e(t,n)}catch(e){(0,o.ZP)(t[i.e4](),2,73,"Unexpected error calling unload handler - "+(0,r.mmD)(e))}})),e=[]}}}},8969:(e,t,n)=>{n.d(t,{d:()=>u,w:()=>c});var r,i,o=n(269),a=n(6182),s=n(3775);function u(e,t){r=e,i=t}function c(){var e=[];return{run:function(t){var n=e;e=[],(0,o.Iuo)(n,(function(e){try{(e.rm||e.remove).call(e)}catch(e){(0,s.ZP)(t,2,73,"Unloading:"+(0,o.mmD)(e))}})),r&&n[a.oI]>r&&(i?i("doUnload",n):(0,s.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected."))},add:function(t){t&&((0,o.Yny)(e,t),r&&e[a.oI]>r&&(i?i("Add",e):(0,s.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected.")))}}}},1864:(e,t,n)=>{n.d(t,{L0:()=>P,N7:()=>S,ZI:()=>y,ef:()=>C,hX:()=>b,mJ:()=>I,wN:()=>w,wk:()=>g});var r=n(269),i=n(6182),o=n(9882),a=n(7292),s=n(6492),u=/^([\da-f]{2})-([\da-f]{32})-([\da-f]{16})-([\da-f]{2})(-[^\s]{1,64})?$/i,c="00",l="ff",f="00000000000000000000000000000000",d="0000000000000000",v=1;function h(e,t,n){return!(!e||e[i.oI]!==t||e===n||!e.match(/^[\da-f]*$/i))}function p(e,t,n){return h(e,t)?e:n}function m(e){(isNaN(e)||e<0||e>255)&&(e=1);for(var t=e.toString(16);t[i.oI]<2;)t="0"+t;return t}function g(e,t,n,a){var s;return(s={})[i.s]=h(a,2,l)?a:c,s[i.P5]=b(e)?e:(0,o.cL)(),s[i.wi]=w(t)?t:(0,r.ZWZ)((0,o.cL)(),16),s.traceFlags=n>=0&&n<=255?n:1,s}function y(e,t){var n;if(!e)return null;if((0,r.cyL)(e)&&(e=e[0]||""),!e||!(0,r.KgX)(e)||e[i.oI]>8192)return null;if(-1!==e.indexOf(",")){var o=e[i.sY](",");e=o[t>0&&o[i.oI]>t?t:0]}var a=u.exec((0,r.EHq)(e));return a&&a[1]!==l&&a[2]!==f&&a[3]!==d?((n={version:(a[1]||s.m5)[i.OL](),traceId:(a[2]||s.m5)[i.OL](),spanId:(a[3]||s.m5)[i.OL]()})[i.Rr]=parseInt(a[4],16),n):null}function b(e){return h(e,32,f)}function w(e){return h(e,16,d)}function I(e){return!!(e&&h(e[i.s],2,l)&&h(e[i.P5],32,f)&&h(e[i.wi],16,d)&&h(m(e[i.Rr]),2))}function S(e){return!!I(e)&&(e[i.Rr]&v)===v}function P(e){if(e){var t=m(e[i.Rr]);h(t,2)||(t="01");var n=e[i.s]||c;return"00"!==n&&"ff"!==n&&(n=c),"".concat(n.toLowerCase(),"-").concat(p(e.traceId,32,f).toLowerCase(),"-").concat(p(e.spanId,16,d).toLowerCase(),"-").concat(t.toLowerCase())}return""}function C(e){var t="traceparent",n=y((0,a.$Z)(t),e);return n||(n=y((0,a.Iu)(t),e)),n}},6182:(e,t,n)=>{n.d(t,{$5:()=>R,$o:()=>z,AP:()=>T,Az:()=>oe,Bl:()=>M,Cd:()=>ue,DI:()=>A,Di:()=>N,FI:()=>D,HC:()=>V,Ic:()=>p,Ik:()=>F,JQ:()=>i,JR:()=>X,Ju:()=>b,K0:()=>m,L:()=>he,M5:()=>H,OL:()=>r,O_:()=>ce,P5:()=>ye,QM:()=>ne,Qg:()=>W,RF:()=>_,RS:()=>k,Rr:()=>we,Ru:()=>fe,Uw:()=>d,Vq:()=>ve,W7:()=>ee,XM:()=>U,XW:()=>a,YH:()=>Z,Yo:()=>re,Yq:()=>C,Zu:()=>se,_w:()=>g,by:()=>O,c1:()=>pe,cV:()=>ie,cp:()=>de,do:()=>me,e4:()=>G,e_:()=>le,fA:()=>x,h0:()=>l,h3:()=>w,h4:()=>L,ih:()=>c,mE:()=>y,oI:()=>o,on:()=>u,pF:()=>E,pM:()=>q,re:()=>Y,s:()=>$,sY:()=>J,sl:()=>P,sx:()=>te,tI:()=>B,tX:()=>Q,tZ:()=>S,tn:()=>K,uR:()=>j,vR:()=>I,wJ:()=>ae,wi:()=>be,x6:()=>f,y5:()=>h,y9:()=>v,yy:()=>ge,zs:()=>s});var r="toLowerCase",i="blkVal",o="length",a="rdOnly",s="notify",u="warnToConsole",c="throwInternal",l="setDf",f="watch",d="logger",v="apply",h="push",p="splice",m="hdlr",g="cancel",y="initialize",b="identifier",w="removeNotificationListener",I="addNotificationListener",S="isInitialized",P="instrumentationKey",C="INACTIVE",E="value",_="getNotifyMgr",T="getPlugin",k="name",D="iKey",x="time",R="processNext",A="getProcessTelContext",L="pollInternalLogs",U="enabled",N="stopPollingInternalLogs",H="unload",O="onComplete",$="version",M="loggingLevelConsole",z="createNew",F="teardown",X="messageId",q="message",B="isAsync",G="diagLog",K="_doTeardown",V="update",j="getNext",Z="setNextPlugin",W="protocol",Q="userAgent",J="split",Y="nodeType",ee="replace",te="logInternalMessage",ne="type",re="handler",ie="status",oe="getResponseHeader",ae="getAllResponseHeaders",se="isChildEvt",ue="data",ce="getCtx",le="setCtx",fe="complete",de="itemsReceived",ve="urlString",he="sendPOST",pe="headers",me="timeout",ge="setRequestHeader",ye="traceId",be="spanId",we="traceFlags"},5664:(e,t,n)=>{n.d(t,{Wy:()=>u,_1:()=>i,bA:()=>o,hW:()=>r,s6:()=>s,vR:()=>a});var r="function",i="object",o="undefined",a="prototype",s=Object,u=s[a]},659:(e,t,n)=>{n.d(t,{Im:()=>a,qU:()=>u,vz:()=>c});var r=n(269),i=n(5664),o=(((0,r.mS$)()||{}).Symbol,((0,r.mS$)()||{}).Reflect,"hasOwnProperty"),a=r.vE3||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])i.Wy[o].call(t,a)&&(e[a]=t[a]);return e},s=function(e,t){return s=i.s6.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t[o](n)&&(e[n]=t[n])},s(e,t)};function u(e,t){function n(){this.constructor=e}typeof t!==i.hW&&null!==t&&(0,r.zkd)("Class extends value "+String(t)+" is not a constructor or null"),s(e,t),e[i.vR]=null===t?(0,r.sSX)(t):(n[i.vR]=t[i.vR],new n)}function c(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e}},60:(e,t,n)=>{n.r(t),n.d(t,{AppInsightsCore:()=>qn._,ApplicationInsights:()=>er,Sender:()=>zn,SeverityLevel:()=>Qn,arrForEach:()=>z.Iuo,isNullOrUndefined:()=>z.hXl,proxyFunctions:()=>xe.o$,throwError:()=>z.$8});var r=n(8279),i=n(659),o="sampleRate",a="ProcessLegacy",s="http.method",u="https://dc.services.visualstudio.com",c="/v2/track",l="not_specified",f="split",d="length",v="toLowerCase",h="ingestionendpoint",p="toString",m="push",g="removeItem",y="name",b="message",w="stringify",I="pathname",S="exceptions",P="parsedStack",C="properties",E="measurements",_="sizeInBytes",T="typeName",k="severityLevel",D="problemGroup",x="isManual",R="CreateFromInterface",A="assembly",L="fileName",U="hasFullStack",N="level",H="method",O="line",$="duration",M="receivedResponse",z=n(269),F=n(3775),X=n(7292);function q(e,t,n){var r,i=t[d],o=function(e,t){var n;return t&&(t=(0,z.EHq)((0,z.oJg)(t)))[d]>150&&(n=(0,z.P0f)(t,0,150),(0,F.ZP)(e,2,57,"name is too long.  It has been truncated to 150 characters.",{name:t},!0)),n||t}(e,t);if(o[d]!==i){for(var a=0,s=o;void 0!==n[s];)a++,s=(0,z.P0f)(o,0,147)+(void 0,r="00"+a,(0,z.hKY)(r,r[d]-3));o=s}return o}function B(e,t,n){var r;return void 0===n&&(n=1024),t&&(n=n||1024,(t=(0,z.EHq)((0,z.oJg)(t)))[d]>n&&(r=(0,z.P0f)(t,0,n),(0,F.ZP)(e,2,61,"string value is too long. It has been truncated to "+n+" characters.",{value:t},!0))),r||t}function G(e,t){return Z(e,t,2048,66)}function K(e,t){var n;return t&&t[d]>32768&&(n=(0,z.P0f)(t,0,32768),(0,F.ZP)(e,2,56,"message is too long, it has been truncated to 32768 characters.",{message:t},!0)),n||t}function V(e,t){if(t){var n={};(0,z.zav)(t,(function(t,r){if((0,z.Gvm)(r)&&(0,X.Z)())try{r=(0,X.hm)()[w](r)}catch(t){(0,F.ZP)(e,2,49,"custom property is not valid",{exception:t},!0)}r=B(e,r,8192),t=q(e,t,n),n[t]=r})),t=n}return t}function j(e,t){if(t){var n={};(0,z.zav)(t,(function(t,r){t=q(e,t,n),n[t]=r})),t=n}return t}function Z(e,t,n,r){var i;return t&&(t=(0,z.EHq)((0,z.oJg)(t)))[d]>n&&(i=(0,z.P0f)(t,0,n),(0,F.ZP)(e,2,r,"input is too long, it has been truncated to "+n+" characters.",{data:t},!0)),i||t}var W=function(){function e(e,t,n,r){this.aiDataContract={ver:1,name:1,properties:0,measurements:0};var i=this;i.ver=2,i[y]=B(e,t)||l,i[C]=V(e,n),i[E]=j(e,r)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Event",e.dataType="EventData",e}(),Q=function(){function e(e,t,n,r,i){this.aiDataContract={ver:1,message:1,severityLevel:0,properties:0};var o=this;o.ver=2,t=t||l,o[b]=K(e,t),o[C]=V(e,r),o[E]=j(e,i),n&&(o[k]=n)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Message",e.dataType="MessageData",e}();function J(e){(isNaN(e)||e<0)&&(e=0);var t=""+(e=Math.round(e))%1e3,n=""+Math.floor(e/1e3)%60,r=""+Math.floor(e/6e4)%60,i=""+Math.floor(e/36e5)%24,o=Math.floor(e/864e5);return t=1===t[d]?"00"+t:2===t[d]?"0"+t:t,n=n[d]<2?"0"+n:n,r=r[d]<2?"0"+r:r,(o>0?o+".":"")+(i=i[d]<2?"0"+i:i)+":"+r+":"+n+"."+t}var Y=function(){function e(e,t,n,r,i,o,a){this.aiDataContract={ver:1,name:0,url:0,duration:0,properties:0,measurements:0,id:0};var s=this;s.ver=2,s.id=function(e,t){return t?Z(e,t,128,69)[p]():t}(e,a),s.url=G(e,n),s[y]=B(e,t)||l,isNaN(r)||(s[$]=J(r)),s[C]=V(e,i),s[E]=j(e,o)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Pageview",e.dataType="PageviewData",e}(),ee=function(){function e(e,t,n,r,i,o,a){this.aiDataContract={ver:1,name:0,url:0,duration:0,perfTotal:0,networkConnect:0,sentRequest:0,receivedResponse:0,domProcessing:0,properties:0,measurements:0};var s=this;s.ver=2,s.url=G(e,n),s[y]=B(e,t)||l,s[C]=V(e,i),s[E]=j(e,o),a&&(s.domProcessing=a.domProcessing,s[$]=a[$],s.networkConnect=a.networkConnect,s.perfTotal=a.perfTotal,s[M]=a[M],s.sentRequest=a.sentRequest)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.PageviewPerformance",e.dataType="PageviewPerformanceData",e}(),te="error",ne="stack",re="stackDetails",ie="errorSrc",oe="message",ae="description";function se(e,t){var n=e;return n&&!(0,z.KgX)(n)&&(JSON&&JSON[w]?(n=JSON[w](e),!t||n&&"{}"!==n||(n=(0,z.Tnt)(e[p])?e[p]():""+e)):n=e+" - (Missing JSON.stringify)"),n||""}function ue(e,t){var n=e;return e&&(n&&!(0,z.KgX)(n)&&(n=e[oe]||e[ae]||n),n&&!(0,z.KgX)(n)&&(n=se(n,!0)),e.filename&&(n=n+" @"+(e.filename||"")+":"+(e.lineno||"?")+":"+(e.colno||"?"))),t&&"String"!==t&&"Object"!==t&&"Error"!==t&&-1===(0,z.HzD)(n||"",t)&&(n=t+": "+n),n||""}function ce(e){return e&&e.src&&(0,z.KgX)(e.src)&&e.obj&&(0,z.cyL)(e.obj)}function le(e){var t=e||"";(0,z.KgX)(t)||(t=(0,z.KgX)(t[ne])?t[ne]:""+t);var n=t[f]("\n");return{src:t,obj:n}}function fe(e){var t=null;if(e)try{if(e[ne])t=le(e[ne]);else if(e[te]&&e[te][ne])t=le(e[te][ne]);else if(e.exception&&e.exception[ne])t=le(e.exception[ne]);else if(ce(e))t=e;else if(ce(e[re]))t=e[re];else if((0,z.zkX)()&&(0,z.zkX)().opera&&e[oe])t=function(e){for(var t=[],n=e[f]("\n"),r=0;r<n[d];r++){var i=n[r];n[r+1]&&(i+="@"+n[r+1],r++),t[m](i)}return{src:e,obj:t}}(e[b]);else if(e.reason&&e.reason[ne])t=le(e.reason[ne]);else if((0,z.KgX)(e))t=le(e);else{var n=e[oe]||e[ae]||"";(0,z.KgX)(e[ie])&&(n&&(n+="\n"),n+=" from "+e[ie]),n&&(t=le(n))}}catch(e){t=le(e)}return t||{src:"",obj:null}}function de(e){var t="";if(e&&!(t=e.typeName||e[y]||""))try{var n=/function (.{1,200})\(/.exec(e.constructor[p]());t=n&&n[d]>1?n[1]:""}catch(e){}return t}function ve(e){if(e)try{if(!(0,z.KgX)(e)){var t=de(e),n=se(e,!1);return n&&"{}"!==n||(e[te]&&(t=de(e=e[te])),n=se(e,!0)),0!==(0,z.HzD)(n,t)&&"String"!==t?t+":"+n:n}}catch(e){}return""+(e||"")}var he=function(){function e(e,t,n,r,i,o){this.aiDataContract={ver:1,exceptions:1,severityLevel:0,properties:0,measurements:0};var a=this;a.ver=2,function(e){try{if((0,z.Gvm)(e))return"ver"in e&&"exceptions"in e&&"properties"in e}catch(e){}return!1}(t)?(a[S]=t[S]||[],a[C]=t[C],a[E]=t[E],t[k]&&(a[k]=t[k]),t.id&&(a.id=t.id,t[C].id=t.id),t[D]&&(a[D]=t[D]),(0,z.hXl)(t[x])||(a[x]=t[x])):(n||(n={}),o&&(n.id=o),a[S]=[new pe(e,t,n)],a[C]=V(e,n),a[E]=j(e,r),i&&(a[k]=i),o&&(a.id=o))}return e.CreateAutoException=function(e,t,n,r,i,o,a,s){var u,c=de(i||o||e);return(u={})[b]=ue(e,c),u.url=t,u.lineNumber=n,u.columnNumber=r,u.error=ve(i||o||e),u.evt=ve(o||e),u[T]=c,u.stackDetails=fe(a||i||o),u.errorSrc=s,u},e.CreateFromInterface=function(t,n,r,o){var a=n[S]&&(0,z.W$7)(n[S],(function(e){return pe[R](t,e)}));return new e(t,(0,i.Im)((0,i.Im)({},n),{exceptions:a}),r,o)},e.prototype.toInterface=function(){var e,t=this,n=t.exceptions,r=t.properties,i=t.measurements,o=t.severityLevel,a=t.problemGroup,s=t.id,u=t.isManual,c=n instanceof Array&&(0,z.W$7)(n,(function(e){return e.toInterface()}))||void 0;return(e={ver:"4.0"})[S]=c,e.severityLevel=o,e.properties=r,e.measurements=i,e.problemGroup=a,e.id=s,e.isManual=u,e},e.CreateSimpleException=function(e,t,n,r,i,o){var a;return{exceptions:[(a={},a[U]=!0,a.message=e,a.stack=i,a.typeName=t,a)]}},e.envelopeType="Microsoft.ApplicationInsights.{0}.Exception",e.dataType="ExceptionData",e.formatError=ve,e}(),pe=function(){function e(e,t,n){this.aiDataContract={id:0,outerId:0,typeName:1,message:1,hasFullStack:0,stack:0,parsedStack:2};var r=this;if(function(e){try{if((0,z.Gvm)(e))return"hasFullStack"in e&&"typeName"in e}catch(e){}return!1}(t))r[T]=t[T],r[b]=t[b],r[ne]=t[ne],r[P]=t[P]||[],r[U]=t[U];else{var i=t,o=i&&i.evt;(0,z.bJ7)(i)||(i=i[te]||o||i),r[T]=B(e,de(i))||l,r[b]=K(e,ue(t||i,r[T]))||l;var a=t[re]||fe(t);r[P]=function(e){var t,n=e.obj;if(n&&n[d]>0){t=[];var r=0,i=0;if((0,z.Iuo)(n,(function(e){var n=e[p]();if(me.regex.test(n)){var o=new me(n,r++);i+=o[_],t[m](o)}})),i>32768)for(var o=0,a=t[d]-1,s=0,u=o,c=a;o<a;){if((s+=t[o][_]+t[a][_])>32768){var l=c-u+1;t.splice(u,l);break}u=o,c=a,o++,a--}}return t}(a),(0,z.cyL)(r[P])&&(0,z.W$7)(r[P],(function(t){t[A]=B(e,t[A]),t[L]=B(e,t[L])})),r[ne]=function(e,t){var n;if(t){var r=""+t;r[d]>32768&&(n=(0,z.P0f)(r,0,32768),(0,F.ZP)(e,2,52,"exception is too long, it has been truncated to 32768 characters.",{exception:t},!0))}return n||t}(e,function(e){var t="";return e&&(e.obj?(0,z.Iuo)(e.obj,(function(e){t+=e+"\n"})):t=e.src||""),t}(a)),r.hasFullStack=(0,z.cyL)(r.parsedStack)&&r.parsedStack[d]>0,n&&(n[T]=n[T]||r[T])}}return e.prototype.toInterface=function(){var e,t=this,n=t[P]instanceof Array&&(0,z.W$7)(t[P],(function(e){return e.toInterface()}));return(e={id:t.id,outerId:t.outerId,typeName:t[T],message:t[b],hasFullStack:t[U],stack:t[ne]})[P]=n||void 0,e},e.CreateFromInterface=function(t,n){var r=n[P]instanceof Array&&(0,z.W$7)(n[P],(function(e){return me[R](e)}))||n[P];return new e(t,(0,i.Im)((0,i.Im)({},n),{parsedStack:r}))},e}(),me=function(){function e(t,n){this.aiDataContract={level:1,method:1,assembly:0,fileName:0,line:0};var r=this;if(r[_]=0,"string"==typeof t){var i=t;r[N]=n,r[H]="<no_method>",r[A]=(0,z.EHq)(i),r[L]="",r[O]=0;var o=i.match(e.regex);o&&o[d]>=5&&(r[H]=(0,z.EHq)(o[2])||r[H],r[L]=(0,z.EHq)(o[4]),r[O]=parseInt(o[5])||0)}else r[N]=t[N],r[H]=t[H],r[A]=t[A],r[L]=t[L],r[O]=t[O],r[_]=0;r.sizeInBytes+=r.method[d],r.sizeInBytes+=r.fileName[d],r.sizeInBytes+=r.assembly[d],r[_]+=e.baseSize,r.sizeInBytes+=r.level.toString()[d],r.sizeInBytes+=r.line.toString()[d]}return e.CreateFromInterface=function(t){return new e(t,null)},e.prototype.toInterface=function(){var e=this;return{level:e[N],method:e[H],assembly:e[A],fileName:e[L],line:e[O]}},e.regex=/^([\s]+at)?[\s]{0,50}([^\@\()]+?)[\s]{0,50}(\@|\()([^\(\n]+):([0-9]+):([0-9]+)(\)?)$/,e.baseSize=58,e}(),ge=function(){this.aiDataContract={name:1,kind:0,value:1,count:0,min:0,max:0,stdDev:0},this.kind=0},ye=function(){function e(e,t,n,r,i,o,a,s,u){this.aiDataContract={ver:1,metrics:1,properties:0};var c=this;c.ver=2;var f=new ge;f.count=r>0?r:void 0,f.max=isNaN(o)||null===o?void 0:o,f.min=isNaN(i)||null===i?void 0:i,f[y]=B(e,t)||l,f.value=n,f.stdDev=isNaN(a)||null===a?void 0:a,c.metrics=[f],c[C]=V(e,s),c[E]=j(e,u)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Metric",e.dataType="MetricData",e}(),be=(0,z.YEm)()||{},we=0,Ie=[null,null,null,null,null];function Se(e,t){var n=function(e,t){var n=null;if(e){var r=e.match(/(\w{1,150}):\/\/([^\/:]{1,256})(:\d{1,20})?/i);if(null!=r&&r[d]>2&&(0,z.KgX)(r[2])&&r[2][d]>0&&(n=r[2]||"",t&&r[d]>2)){var i=(r[1]||"")[v](),o=r[3]||"";("http"===i&&":80"===o||"https"===i&&":443"===o)&&(o=""),n+=o}}return n}(e,t)||"";if(n){var r=n.match(/(www\d{0,5}\.)?([^\/:]{1,256})(:\d{1,20})?/i);if(null!=r&&r[d]>3&&(0,z.KgX)(r[2])&&r[2][d]>0)return r[2]+(r[3]||"")}return n}var Pe=[u+c,"https://breeze.aimon.applicationinsights.io"+c,"https://dc-int.services.visualstudio.com"+c];function Ce(e){return-1!==(0,z.rDm)(Pe,e[v]())}var Ee=function(){function e(e,t,n,r,i,o,a,s,u,c,l,f){void 0===u&&(u="Ajax"),this.aiDataContract={id:1,ver:1,name:0,resultCode:0,duration:0,success:0,data:0,target:0,type:0,properties:0,measurements:0,kind:0,value:0,count:0,min:0,max:0,stdDev:0,dependencyKind:0,dependencySource:0,commandName:0,dependencyTypeName:0};var v=this;v.ver=2,v.id=t,v[$]=J(i),v.success=o,v.resultCode=a+"",v.type=B(e,u);var h=function(e,t,n,r){var i,o,a,s,u,c=r,l=r;if(t&&t[d]>0){var f=(o=t,u=(s=Ie)[a=we],be.createElement?s[a]||(u=s[a]=be.createElement("a")):u={host:Se(o,!0)},u.href=o,++a>=s[d]&&(a=0),we=a,u);if(i=f.host,!c)if(null!=f[I]){var v=0===f.pathname[d]?"/":f[I];"/"!==v.charAt(0)&&(v="/"+v),l=f[I],c=B(e,n?n+" "+v:v)}else c=B(e,t)}else i=r,c=r;return{target:i,name:c,data:l}}(e,n,s,r);v.data=G(e,r)||h.data,v.target=B(e,h.target),c&&(v.target="".concat(v.target," | ").concat(c)),v[y]=B(e,h[y]),v[C]=V(e,l),v[E]=j(e,f)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.RemoteDependency",e.dataType="RemoteDependencyData",e}(),_e=n(2845),Te=n(6149),ke=n(4276);function De(e,t){(0,Te.ML)(e,null,null,t)}var xe=n(3673),Re=n(4282),Ae=(0,Re.H)({LocalStorage:0,SessionStorage:1}),Le=((0,Re.H)({AI:0,AI_AND_W3C:1,W3C:2}),void(0,Re.H)({Normal:1,Critical:2})),Ue="";function Ne(e){try{if((0,z.hXl)((0,z.mS$)()))return null;var t=(new Date)[p](),n=(0,z.zS2)(e===Ae.LocalStorage?"localStorage":"sessionStorage"),r=Ue+t;n.setItem(r,t);var i=n.getItem(r)!==t;if(n[g](r),!i)return n}catch(e){}return null}function He(){return Oe()?Ne(Ae.SessionStorage):null}function Oe(e){return(e||void 0===Le)&&(Le=!!Ne(Ae.SessionStorage)),Le}function $e(e,t){var n=He();if(null!==n)try{return n.getItem(t)}catch(t){Le=!1,(0,F.ZP)(e,2,2,"Browser failed read of session storage. "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}return null}function Me(e,t,n){var r=He();if(null!==r)try{return r.setItem(t,n),!0}catch(t){Le=!1,(0,F.ZP)(e,2,4,"Browser failed write to session storage. "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}return!1}var ze=(0,Re.o)({requestContextHeader:[0,"Request-Context"],requestContextTargetKey:[1,"appId"],requestContextAppIdFormat:[2,"appId=cid-v1:"],requestIdHeader:[3,"Request-Id"],traceParentHeader:[4,"traceparent"],traceStateHeader:[5,"tracestate"],sdkContextHeader:[6,"Sdk-Context"],sdkContextHeaderAppIdRequest:[7,"appId"],requestContextHeaderLowerCase:[8,"request-context"]}),Fe=n(2475),Xe=n(9749),qe=n(2317),Be=n(4875),Ge=n(856),Ke=n(4013),Ve=n(1190),je=n(8257);function Ze(e){var t="ai."+e+".";return function(e){return t+e}}var We=Ze("application"),Qe=Ze("device"),Je=Ze("location"),Ye=Ze("operation"),et=Ze("session"),tt=Ze("user"),nt=Ze("cloud"),rt=Ze("internal"),it=function(e){function t(){return e.call(this)||this}return(0,i.qU)(t,e),t}((0,xe.SZ)({applicationVersion:We("ver"),applicationBuild:We("build"),applicationTypeId:We("typeId"),applicationId:We("applicationId"),applicationLayer:We("layer"),deviceId:Qe("id"),deviceIp:Qe("ip"),deviceLanguage:Qe("language"),deviceLocale:Qe("locale"),deviceModel:Qe("model"),deviceFriendlyName:Qe("friendlyName"),deviceNetwork:Qe("network"),deviceNetworkName:Qe("networkName"),deviceOEMName:Qe("oemName"),deviceOS:Qe("os"),deviceOSVersion:Qe("osVersion"),deviceRoleInstance:Qe("roleInstance"),deviceRoleName:Qe("roleName"),deviceScreenResolution:Qe("screenResolution"),deviceType:Qe("type"),deviceMachineName:Qe("machineName"),deviceVMName:Qe("vmName"),deviceBrowser:Qe("browser"),deviceBrowserVersion:Qe("browserVersion"),locationIp:Je("ip"),locationCountry:Je("country"),locationProvince:Je("province"),locationCity:Je("city"),operationId:Ye("id"),operationName:Ye("name"),operationParentId:Ye("parentId"),operationRootId:Ye("rootId"),operationSyntheticSource:Ye("syntheticSource"),operationCorrelationVector:Ye("correlationVector"),sessionId:et("id"),sessionIsFirst:et("isFirst"),sessionIsNew:et("isNew"),userAccountAcquisitionDate:tt("accountAcquisitionDate"),userAccountId:tt("accountId"),userAgent:tt("userAgent"),userId:tt("id"),userStoreRegion:tt("storeRegion"),userAuthUserId:tt("authUserId"),userAnonymousUserAcquisitionDate:tt("anonUserAcquisitionDate"),userAuthenticatedUserAcquisitionDate:tt("authUserAcquisitionDate"),cloudName:nt("name"),cloudRole:nt("role"),cloudRoleVer:nt("roleVer"),cloudRoleInstance:nt("roleInstance"),cloudEnvironment:nt("environment"),cloudLocation:nt("location"),cloudDeploymentUnit:nt("deploymentUnit"),internalNodeName:rt("nodeName"),internalSdkVersion:rt("sdkVersion"),internalAgentVersion:rt("agentVersion"),internalSnippet:rt("snippet"),internalSdkSrc:rt("sdkSrc")})),ot=new it,at=function(e,t,n){var r=this,i=this;i.ver=1,i.sampleRate=100,i.tags={},i[y]=B(e,n)||l,i.data=t,i.time=(0,xe._u)(new Date),i.aiDataContract={time:1,iKey:1,name:1,sampleRate:function(){return 100===r.sampleRate?4:1},tags:1,data:1}},st=function(e,t){this.aiDataContract={baseType:1,baseData:1},this.baseType=e,this.baseData=t},ut="duration",ct="tags",lt="deviceType",ft="data",dt="name",vt="traceID",ht="length",pt="stringify",mt="measurements",gt="dataType",yt="envelopeType",bt="toString",wt="_get",It="enqueue",St="count",Pt="eventsLimitInMem",Ct="push",Et="item",_t="emitLineDelimitedJson",Tt="clear",kt="createNew",Dt="markAsSent",xt="clearSent",Rt="bufferOverride",At="BUFFER_KEY",Lt="SENT_BUFFER_KEY",Ut="concat",Nt="MAX_BUFFER_SIZE",Ht="triggerSend",Ot="diagLog",$t="initialize",Mt="_sender",zt="endpointUrl",Ft="instrumentationKey",Xt="customHeaders",qt="maxBatchSizeInBytes",Bt="onunloadDisableBeacon",Gt="isBeaconApiDisabled",Kt="alwaysUseXhrOverride",Vt="disableXhr",jt="enableSessionStorageBuffer",Zt="_buffer",Wt="onunloadDisableFetch",Qt="disableSendBeaconSplit",Jt="enableSendPromise",Yt="getSenderInst",en="unloadTransports",tn="convertUndefined",nn="maxBatchInterval",rn="serialize",on="_onError",an="_onPartialSuccess",sn="_onSuccess",un="itemsReceived",cn="itemsAccepted",ln="oriPayload",fn="baseType",dn="sampleRate",vn="eventsSendRequest",hn="getSamplingScore",pn="baseType",mn="baseData",gn="properties",yn="true";function bn(e,t,n){return(0,xe.KY)(e,t,n,z.zzB)}function wn(e,t,n){(0,z.hXl)(e)||(0,z.zav)(e,(function(e,r){(0,z.EtT)(r)?n[e]=r:(0,z.KgX)(r)?t[e]=r:(0,X.Z)()&&(t[e]=(0,X.hm)()[pt](r))}))}function In(e,t){(0,z.hXl)(e)||(0,z.zav)(e,(function(n,r){e[n]=r||t}))}function Sn(e,t,n,r){var a=new at(e,r,t);bn(a,"sampleRate",n[o]),(n[mn]||{}).startTime&&(a.time=(0,xe._u)(n[mn].startTime)),a.iKey=n.iKey;var s=n.iKey.replace(/-/g,"");return a[dt]=a[dt].replace("{0}",s),function(e,t,n){var r=n[ct]=n[ct]||{},o=t.ext=t.ext||{},a=t[ct]=t[ct]||[],s=o.user;s&&(bn(r,ot.userAuthUserId,s.authId),bn(r,ot.userId,s.id||s.localId));var u=o.app;u&&bn(r,ot.sessionId,u.sesId);var c=o.device;c&&(bn(r,ot.deviceId,c.id||c.localId),bn(r,ot[lt],c.deviceClass),bn(r,ot.deviceIp,c.ip),bn(r,ot.deviceModel,c.model),bn(r,ot[lt],c[lt]));var l=t.ext.web;if(l){bn(r,ot.deviceLanguage,l.browserLang),bn(r,ot.deviceBrowserVersion,l.browserVer),bn(r,ot.deviceBrowser,l.browser);var f=n[ft]=n[ft]||{},d=f[mn]=f[mn]||{},v=d[gn]=d[gn]||{};bn(v,"domain",l.domain),bn(v,"isManual",l.isManual?yn:null),bn(v,"screenRes",l.screenRes),bn(v,"userConsent",l.userConsent?yn:null)}var h=o.os;h&&(bn(r,ot.deviceOS,h[dt]),bn(r,ot.deviceOSVersion,h.osVer));var p=o.trace;p&&(bn(r,ot.operationParentId,p.parentID),bn(r,ot.operationName,B(e,p[dt])),bn(r,ot.operationId,p[vt]));for(var m={},g=a[ht]-1;g>=0;g--){var y=a[g];(0,z.zav)(y,(function(e,t){m[e]=t})),a.splice(g,1)}(0,z.zav)(a,(function(e,t){m[e]=t}));var b=(0,i.Im)((0,i.Im)({},r),m);b[ot.internalSdkVersion]||(b[ot.internalSdkVersion]=B(e,"javascript:".concat(Cn.Version),64)),n[ct]=(0,xe.hW)(b)}(e,n,a),n[ct]=n[ct]||[],(0,xe.hW)(a)}function Pn(e,t){(0,z.hXl)(t[mn])&&(0,F.ZP)(e,1,46,"telemetryItem.baseData cannot be null.")}var Cn={Version:"3.3.4"};function En(e,t,n){Pn(e,t);var r={},i={};t[pn]!==W[gt]&&(r.baseTypeSource=t[pn]),t[pn]===W[gt]?(r=t[mn][gn]||{},i=t[mn][mt]||{}):t[mn]&&wn(t[mn],r,i),wn(t[ft],r,i),(0,z.hXl)(n)||In(r,n);var o=t[mn][dt],a=new W(e,o,r,i),s=new st(W[gt],a);return Sn(e,W[yt],t,s)}var _n,Tn,kn=function(){function e(t,n){var i=[],o=!1,a=n.maxRetryCnt;this[wt]=function(){return i},this._set=function(e){return i=e},(0,r.A)(e,this,(function(e){e[It]=function(r){e[St]()>=n[Pt]?o||((0,F.ZP)(t,2,105,"Maximum in-memory buffer size reached: "+e[St](),!0),o=!0):(r.cnt=r.cnt||0,!(0,z.hXl)(a)&&r.cnt>a||i[Ct](r))},e[St]=function(){return i[ht]},e.size=function(){for(var e=i[ht],t=0;t<i[ht];t++)e+=i[t].item[ht];return n[_t]||(e+=2),e},e[Tt]=function(){i=[],o=!1},e.getItems=function(){return i.slice(0)},e.batchPayloads=function(e){if(e&&e[ht]>0){var t=[];return(0,z.Iuo)(e,(function(e){t[Ct](e[Et])})),n[_t]?t.join("\n"):"["+t.join(",")+"]"}return null},e[kt]=function(e,n,r){var o=i.slice(0);e=e||t,n=n||{};var a=r?new Rn(e,n):new Dn(e,n);return(0,z.Iuo)(o,(function(e){a[It](e)})),a}}))}return e.__ieDyn=1,e}(),Dn=function(e){function t(n,i){var o=e.call(this,n,i)||this;return(0,r.A)(t,o,(function(e,t){e[Dt]=function(e){t[Tt]()},e[xt]=function(e){}})),o}return(0,i.qU)(t,e),t.__ieDyn=1,t}(kn),xn=["AI_buffer","AI_sentBuffer"],Rn=function(e){function t(n,i){var o=e.call(this,n,i)||this,a=!1,s=null==i?void 0:i.namePrefix,u=i[Rt]||{getItem:$e,setItem:Me},c=u.getItem,l=u.setItem,f=i.maxRetryCnt;return(0,r.A)(t,o,(function(e,r){var i=p(t[At]),o=p(t[Lt]),u=function(){var e=[];try{return(0,z.Iuo)(xn,(function(t){var n=b(t);if(e=e[Ut](n),s){var r=b(s+"_"+t);e=e[Ut](r)}})),e}catch(e){(0,F.ZP)(n,2,41,"Transfer events from previous buffers: "+(0,xe.lL)(e)+". previous Buffer items can not be removed",{exception:(0,z.mmD)(e)})}return[]}(),d=o[Ut](u),v=e._set(i[Ut](d));function h(e,t){var n=[],r=[];return(0,z.Iuo)(e,(function(e){r[Ct](e[Et])})),(0,z.Iuo)(t,(function(e){(0,z.Tnt)(e)||-1!==(0,z.rDm)(r,e[Et])||n[Ct](e)})),n}function p(e){return m(s?s+"_"+e:e)}function m(e){try{var t=c(n,e);if(t){var r=(0,X.hm)().parse(t);if((0,z.KgX)(r)&&(r=(0,X.hm)().parse(r)),r&&(0,z.cyL)(r))return r}}catch(t){(0,F.ZP)(n,1,42," storage key: "+e+", "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}return[]}function y(e,t){var r=e;try{r=s?s+"_"+r:r;var i=JSON[pt](t);l(n,r,i)}catch(e){l(n,r,JSON[pt]([])),(0,F.ZP)(n,2,41," storage key: "+r+", "+(0,xe.lL)(e)+". Buffer cleared",{exception:(0,z.mmD)(e)})}}function b(e){try{var t=m(e),r=[];return(0,z.Iuo)(t,(function(e){var t={item:e,cnt:0};r[Ct](t)})),function(e,t){var n=He();if(null!==n)try{return n[g](t),!0}catch(t){Le=!1,(0,F.ZP)(e,2,6,"Browser failed removal of session storage item. "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}}(n,e),r}catch(e){}return[]}v[ht]>t[Nt]&&(v[ht]=t[Nt]),y(t[Lt],[]),y(t[At],v),e[It]=function(i){e[St]()>=t[Nt]?a||((0,F.ZP)(n,2,67,"Maximum buffer size reached: "+e[St](),!0),a=!0):(i.cnt=i.cnt||0,!(0,z.hXl)(f)&&i.cnt>f||(r[It](i),y(t.BUFFER_KEY,e[wt]())))},e[Tt]=function(){r[Tt](),y(t.BUFFER_KEY,e[wt]()),y(t[Lt],[]),a=!1},e[Dt]=function(r){y(t[At],e._set(h(r,e[wt]())));var i=p(t[Lt]);i instanceof Array&&r instanceof Array&&((i=i[Ut](r))[ht]>t[Nt]&&((0,F.ZP)(n,1,67,"Sent buffer reached its maximum size: "+i[ht],!0),i[ht]=t[Nt]),y(t[Lt],i))},e[xt]=function(e){var n=p(t[Lt]);n=h(e,n),y(t[Lt],n)},e[kt]=function(r,i,o){o=!!o;var a=e[wt]().slice(0),s=p(t[Lt]).slice(0);r=r||n,i=i||{},e[Tt]();var u=o?new t(r,i):new Dn(r,i);return(0,z.Iuo)(a,(function(e){u[It](e)})),o&&u[Dt](s),u}})),o}var n;return(0,i.qU)(t,e),n=t,t.VERSION="_1",t.BUFFER_KEY="AI_buffer"+n.VERSION,t.SENT_BUFFER_KEY="AI_sentBuffer"+n.VERSION,t.MAX_BUFFER_SIZE=2e3,t}(kn),An=function(){function e(t){(0,r.A)(e,this,(function(e){function n(e,o){var a="__aiCircularRefCheck",s={};if(!e)return(0,F.ZP)(t,1,48,"cannot serialize object because it is null or undefined",{name:o},!0),s;if(e[a])return(0,F.ZP)(t,2,50,"Circular reference detected while serializing object",{name:o},!0),s;if(!e.aiDataContract){if("measurements"===o)s=i(e,"number",o);else if("properties"===o)s=i(e,"string",o);else if("tags"===o)s=i(e,"string",o);else if((0,z.cyL)(e))s=r(e,o);else{(0,F.ZP)(t,2,49,"Attempting to serialize an object which does not implement ISerializable",{name:o},!0);try{(0,X.hm)()[pt](e),s=e}catch(e){(0,F.ZP)(t,1,48,e&&(0,z.Tnt)(e[bt])?e[bt]():"Error serializing object",null,!0)}}return s}return e[a]=!0,(0,z.zav)(e.aiDataContract,(function(i,a){var u=(0,z.Tnt)(a)?1&a():1&a,c=(0,z.Tnt)(a)?4&a():4&a,l=2&a,f=void 0!==e[i],d=(0,z.Gvm)(e[i])&&null!==e[i];if(!u||f||l){if(!c){var v;void 0!==(v=d?l?r(e[i],i):n(e[i],i):e[i])&&(s[i]=v)}}else(0,F.ZP)(t,1,24,"Missing required field specification. The field is required but not present on source",{field:i,name:o})})),delete e[a],s}function r(e,r){var i;if(e)if((0,z.cyL)(e)){i=[];for(var o=0;o<e[ht];o++){var a=n(e[o],r+"["+o+"]");i[Ct](a)}}else(0,F.ZP)(t,1,54,"This field was specified as an array in the contract but the item is not an array.\r\n",{name:r},!0);return i}function i(e,n,r){var i;return e&&(i={},(0,z.zav)(e,(function(e,o){if("string"===n)void 0===o?i[e]="undefined":null===o?i[e]="null":o[bt]?i[e]=o[bt]():i[e]="invalid field: toString() is not defined.";else if("number"===n)if(void 0===o)i[e]="undefined";else if(null===o)i[e]="null";else{var a=parseFloat(o);i[e]=a}else i[e]="invalid field: "+r+" is of unknown type.",(0,F.ZP)(t,1,i[e],null,!0)}))),i}e[rn]=function(e){var r=n(e,"root");try{return(0,X.hm)()[pt](r)}catch(e){(0,F.ZP)(t,1,48,e&&(0,z.Tnt)(e[bt])?e[bt]():"Error serializing object",null,!0)}}}))}return e.__ieDyn=1,e}(),Ln=function(){function e(){}return e.prototype.getHashCodeScore=function(t){return this.getHashCode(t)/e.INT_MAX_VALUE*100},e.prototype.getHashCode=function(e){if(""===e)return 0;for(;e[ht]<8;)e=e[Ut](e);for(var t=5381,n=0;n<e[ht];++n)t=(t<<5)+t+e.charCodeAt(n),t|=0;return Math.abs(t)},e.INT_MAX_VALUE=2147483647,e}(),Un=function(){var e=new Ln,t=new it;this[hn]=function(n){return n[ct]&&n[ct][t.userId]?e.getHashCodeScore(n[ct][t.userId]):n.ext&&n.ext.user&&n.ext.user.id?e.getHashCodeScore(n.ext.user.id):n[ct]&&n[ct][t.operationId]?e.getHashCodeScore(n[ct][t.operationId]):n.ext&&n.ext.telemetryTrace&&n.ext.telemetryTrace[vt]?e.getHashCodeScore(n.ext.telemetryTrace[vt]):100*Math.random()}},Nn=function(){function e(e,t){this.INT_MAX_VALUE=2147483647;var n=t||(0,F.y0)(null);(e>100||e<0)&&(n.throwInternal(2,58,"Sampling rate is out of range (0..100). Sampling will be disabled, you may be sending too much data which may affect your AI service level.",{samplingRate:e},!0),e=100),this[dn]=e,this.samplingScoreGenerator=new Un}return e.prototype.isSampledIn=function(e){var t=this[dn];return null==t||t>=100||e.baseType===ye[gt]||this.samplingScoreGenerator[hn](e)<t},e}(),Hn=void 0;function On(e){try{return e.responseText}catch(e){}return null}var $n=(0,z.ZHX)(((_n={endpointUrl:(0,Fe.Lx)(z.zzB,u+c)})[_t]=(0,Fe.DD)(),_n[nn]=15e3,_n[qt]=102400,_n.disableTelemetry=(0,Fe.DD)(),_n[jt]=(0,Fe.DD)(!0),_n.isRetryDisabled=(0,Fe.DD)(),_n[Gt]=(0,Fe.DD)(!0),_n[Qt]=(0,Fe.DD)(!0),_n[Vt]=(0,Fe.DD)(),_n[Wt]=(0,Fe.DD)(),_n[Bt]=(0,Fe.DD)(),_n[Ft]=Hn,_n.namePrefix=Hn,_n.samplingPercentage=(0,Fe.Lx)((function(e){return!isNaN(e)&&e>0&&e<=100}),100),_n[Xt]=Hn,_n[tn]=Hn,_n[Pt]=1e4,_n[Rt]=!1,_n.httpXHROverride={isVal:function(e){return e&&e.sendPOST},v:Hn},_n[Kt]=(0,Fe.DD)(),_n.transports=Hn,_n.retryCodes=Hn,_n.maxRetryCnt={isVal:z.EtT,v:10},_n)),Mn=((Tn={})[W.dataType]=En,Tn[Q.dataType]=function(e,t,n){Pn(e,t);var r=t[mn].message,i=t[mn].severityLevel,o=t[mn][gn]||{},a=t[mn][mt]||{};wn(t[ft],o,a),(0,z.hXl)(n)||In(o,n);var s=new Q(e,r,i,o,a),u=new st(Q[gt],s);return Sn(e,Q[yt],t,u)},Tn[Y.dataType]=function(e,t,n){var r;Pn(e,t);var i=t[mn];(0,z.hXl)(i)||(0,z.hXl)(i[gn])||(0,z.hXl)(i[gn][ut])?(0,z.hXl)(t[ft])||(0,z.hXl)(t[ft][ut])||(r=t[ft][ut],delete t[ft][ut]):(r=i[gn][ut],delete i[gn][ut]);var o,a=t[mn];((t.ext||{}).trace||{})[vt]&&(o=t.ext.trace[vt]);var s=a.id||o,u=a[dt],c=a.uri,l=a[gn]||{},f=a[mt]||{};if((0,z.hXl)(a.refUri)||(l.refUri=a.refUri),(0,z.hXl)(a.pageType)||(l.pageType=a.pageType),(0,z.hXl)(a.isLoggedIn)||(l.isLoggedIn=a.isLoggedIn[bt]()),!(0,z.hXl)(a[gn])){var d=a[gn];(0,z.zav)(d,(function(e,t){l[e]=t}))}wn(t[ft],l,f),(0,z.hXl)(n)||In(l,n);var v=new Y(e,u,c,r,l,f,s),h=new st(Y[gt],v);return Sn(e,Y[yt],t,h)},Tn[ee.dataType]=function(e,t,n){Pn(e,t);var r=t[mn],i=r[dt],o=r.uri||r.url,a=r[gn]||{},s=r[mt]||{};wn(t[ft],a,s),(0,z.hXl)(n)||In(a,n);var u=new ee(e,i,o,void 0,a,s,r),c=new st(ee[gt],u);return Sn(e,ee[yt],t,c)},Tn[he.dataType]=function(e,t,n){Pn(e,t);var r=t[mn][mt]||{},i=t[mn][gn]||{};wn(t[ft],i,r),(0,z.hXl)(n)||In(i,n);var o=t[mn],a=he.CreateFromInterface(e,o,i,r),s=new st(he[gt],a);return Sn(e,he[yt],t,s)},Tn[ye.dataType]=function(e,t,n){Pn(e,t);var r=t[mn],i=r[gn]||{},o=r[mt]||{};wn(t[ft],i,o),(0,z.hXl)(n)||In(i,n);var a=new ye(e,r[dt],r.average,r.sampleCount,r.min,r.max,r.stdDev,i,o),s=new st(ye[gt],a);return Sn(e,ye[yt],t,s)},Tn[Ee.dataType]=function(e,t,n){Pn(e,t);var r=t[mn][mt]||{},i=t[mn][gn]||{};wn(t[ft],i,r),(0,z.hXl)(n)||In(i,n);var o=t[mn];if((0,z.hXl)(o))return(0,F.OG)(e,"Invalid input for dependency data"),null;var a=o[gn]&&o[gn][s]?o[gn][s]:"GET",u=new Ee(e,o.id,o.target,o[dt],o[ut],o.success,o.responseCode,a,o.type,o.correlationContext,i,r),c=new st(Ee[gt],u);return Sn(e,Ee[yt],t,c)},Tn),zn=function(e){function t(){var n,i,s,u,c,l,f,d=e.call(this)||this;d.priority=1001,d.identifier=_e.Uu;var v,h,p,g,y,b,w,I,S,P,C,E,_,T,k,D,x,R,A,L,U,N,H,O,$,M,q,B=0;return(0,r.A)(t,d,(function(e,r){function G(t,r){var i=On(t);if(!t||i+""!="200"&&""!==i){var o=(0,Ve.x)(i);o&&o[un]&&o[un]>o[cn]&&!T?e[an](r,o):e[on](r,(0,xe.HU)(t))}else n=0,e[sn](r,0)}function K(e,t,n){4===e.readyState&&re(e.status,t,e.responseURL,n,(0,xe.r4)(e),On(e)||e.response)}function V(e){try{if(e){var t=e[ln];return t&&t[ht]?t:null}}catch(e){}return null}function j(t,n){return!(C||(t?t.baseData&&!t[fn]?(n&&(0,F.ZP)(n,1,70,"Cannot send telemetry without baseData and baseType"),1):(t[fn]||(t[fn]="EventData"),e[Mt]?(r=t,e._sample.isSampledIn(r)?(t[o]=e._sample[dn],0):(n&&(0,F.ZP)(n,2,33,"Telemetry item was sampled out and not sent",{SampleRate:e._sample[dn]}),1)):(n&&(0,F.ZP)(n,1,28,"Sender was not initialized"),1)):(n&&(0,F.ZP)(n,1,7,"Cannot send empty telemetry"),1)));var r}function Z(e,n){var r=e.iKey||E,i=t.constructEnvelope(e,r,n,_);if(i){var o=!1;if(e[ct]&&e[ct][a]&&((0,z.Iuo)(e[ct][a],(function(e){try{e&&!1===e(i)&&(o=!0,(0,F.OG)(n,"Telemetry processor check returns false"))}catch(e){(0,F.ZP)(n,1,64,"One of telemetry initializers failed, telemetry item will not be sent: "+(0,xe.lL)(e),{exception:(0,z.mmD)(e)},!0)}})),delete e[ct][a]),!o)return i}else(0,F.ZP)(n,1,47,"Unable to create an AppInsights envelope")}function W(t){var n="",r=e[Ot]();try{var i=j(t,r),o=null;i&&(o=Z(t,r)),o&&(n=c[rn](o))}catch(e){}return n}function Q(e){var t="";return e&&e[ht]&&(t="["+e.join(",")+"]"),t}function J(e){var t,n=te();return(t={urlString:g})[ft]=e,t.headers=n,t}function Y(t,n,r,i){void 0===i&&(i=!0);var o=ee(n),a=t&&t.sendPOST;return a&&o?(i&&e._buffer[Dt](n),a(o,(function(t,r,i){return function(t,n,r,i){200===n&&t?e._onSuccess(t,t[ht]):i&&e[on](t,i)}(n,t,0,i)}),!r)):null}function ee(t){var n;if((0,z.cyL)(t)&&t[ht]>0){var r=e[Zt].batchPayloads(t),i=te();return(n={})[ft]=r,n.urlString=g,n.headers=i,n.disableXhrSync=U,n.disableFetchKeepAlive=!N,n[ln]=t,n}return null}function te(){try{var e=f||{};return Ce(g)&&(e[ze[6]]=ze[7]),e}catch(e){}return null}function ne(t){var n=t?t[ht]:0;return e[Zt].size()+n>b&&(h&&!h.isOnline()||e[Ht](!0,null,10),!0)}function re(t,r,i,o,a,s){var u=null;if(e._appId||(u=(0,Ve.x)(s))&&u.appId&&(e._appId=u.appId),(t<200||t>=300)&&0!==t){if((301===t||307===t||308===t)&&!ie(i))return void e[on](r,a);if(h&&!h.isOnline())return void(T||(ue(r,10),(0,F.ZP)(e[Ot](),2,40,". Offline - Response Code: ".concat(t,". Offline status: ").concat(!h.isOnline(),". Will retry to send ").concat(r.length," items."))));!T&&fe(t)?(ue(r),(0,F.ZP)(e[Ot](),2,40,". Response code "+t+". Will retry to send "+r[ht]+" items.")):e[on](r,a)}else ie(i),206===t?(u||(u=(0,Ve.x)(s)),u&&!T?e[an](r,u):e[on](r,a)):(n=0,e[sn](r,o))}function ie(e){return!(l>=10||(0,z.hXl)(e)||""===e||e===g||(g=e,++l,0))}function oe(e,t){if(!v)return Y(M&&M[Yt]([3],!0),e,t);v(e,!1)}function ae(e){try{if(e&&e[ht])return(0,z.KgX)(e[0])}catch(e){}return null}function se(t,n){var r=null;if((0,z.cyL)(t)){for(var i=t[ht],o=0;o<t[ht];o++)i+=t[o].item[ht];return M.getSyncFetchPayload()+i<=65e3?r=2:(0,X.Uf)()?r=3:(r=1,(0,F.ZP)(e[Ot](),2,40,". Failed to send telemetry with Beacon API, retried with xhrSender.")),Y(M&&M[Yt]([r],!0),t,n)}return null}function ue(t,r){if(void 0===r&&(r=1),t&&0!==t[ht]){var o=e[Zt];o[xt](t),n++;for(var a=0,s=t;a<s.length;a++){var u=s[a];u.cnt=u.cnt||0,u.cnt++,o[It](u)}!function(e){var t;if(n<=1)t=10;else{var r=(Math.pow(2,n)-1)/2,o=Math.floor(Math.random()*r*10)+1;o*=e,t=Math.max(Math.min(o,3600),10)}var a=(0,z.f0d)()+1e3*t;i=a}(r),ce()}}function ce(){if(!u&&!s){var t=i?Math.max(0,i-(0,z.f0d)()):0,n=Math.max(k,t);u=(0,z.dRz)((function(){u=null,e[Ht](!0,null,1)}),n)}}function le(){u&&u.cancel(),u=null,i=null}function fe(e){return(0,z.hXl)(q)?401===e||408===e||429===e||500===e||502===e||503===e||504===e:q[ht]&&q.indexOf(e)>-1}function de(){e[Mt]=null,e[Zt]=null,e._appId=null,e._sample=null,f={},h=null,n=0,i=null,s=!1,u=null,c=null,l=0,B=0,v=null,p=null,g=null,y=null,b=0,w=!1,P=null,C=!1,E=null,_=Hn,T=!1,D=null,R=Hn,U=!1,N=!1,$=!1,H=null,O=null,M=null,(0,z.vF1)(e,"_senderConfig",{g:function(){return(0,xe.CP)({},$n)}})}de(),e.pause=function(){le(),s=!0},e.resume=function(){s&&(s=!1,i=null,ne(),ce())},e.flush=function(t,n,r){if(void 0===t&&(t=!0),!s){le();try{return e[Ht](t,null,r||1)}catch(t){(0,F.ZP)(e[Ot](),1,22,"flush failed, telemetry will not be collected: "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}}},e.onunloadFlush=function(){if(!s)if(w||L)try{return e[Ht](!0,oe,2)}catch(t){(0,F.ZP)(e[Ot](),1,20,"failed to flush with beacon sender on page unload, telemetry will not be collected: "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}else e.flush(!1)},e.addHeader=function(e,t){f[e]=t},e[$t]=function(t,o,a,s){e.isInitialized()&&(0,F.ZP)(e[Ot](),1,28,"Sender is already initialized"),r[$t](t,o,a,s);var u=e.identifier;c=new An(o.logger),n=0,i=null,e[Mt]=null,l=0;var B=e[Ot]();p=(0,Te.Hm)((0,ke.Z)("Sender"),o.evtNamespace&&o.evtNamespace()),h=function(e){var t=(0,z.YEm)(),n=(0,z.w3n)(),r=!1,i=[],o=1;!n||(0,z.hXl)(n.onLine)||n.onLine||(o=2);var a=0,s=f(),u=(0,Te.Hm)((0,ke.Z)("OfflineListener"),e);try{if(l((0,z.zkX)())&&(r=!0),t){var c=t.body||t;c.ononline&&l(c)&&(r=!0)}}catch(e){r=!1}function l(e){var t=!1;return e&&(t=(0,Te.mB)(e,"online",v,u))&&(0,Te.mB)(e,"offline",h,u),t}function f(){return 2!==a&&2!==o}function d(){var e=f();s!==e&&(s=e,(0,z.Iuo)(i,(function(e){var t={isOnline:s,rState:o,uState:a};try{e(t)}catch(e){}})))}function v(){o=1,d()}function h(){o=2,d()}return{isOnline:function(){return s},isListening:function(){return r},unload:function(){var e=(0,z.zkX)();if(e&&r){if(De(e,u),t){var n=t.body||t;(0,z.b07)(n.ononline)||De(n,u)}r=!1}},addListener:function(e){return i[m](e),{rm:function(){var t=i.indexOf(e);return t>-1?i.splice(t,1):void 0}}},setOnlineState:function(e){a=e,d()}}}(p),e._addHook((0,Xe.a)(t,(function(t){var n,r=t.cfg;r.storagePrefix&&(n=r.storagePrefix,Ue=n||"");var i=(0,qe.i8)(null,r,o).getExtCfg(u,$n),a=i[zt];if(g&&a===g){var s=r[zt];s&&s!==a&&(i[zt]=s)}(0,z.$XS)(i[Ft])&&(i[Ft]=r[Ft]),(0,z.vF1)(e,"_senderConfig",{g:function(){return i}}),y!==i[zt]&&(g=y=i[zt]),o.activeStatus()===Be.f.PENDING?e.pause():o.activeStatus()===Be.f.ACTIVE&&e.resume(),P&&P!==i[Xt]&&(0,z.Iuo)(P,(function(e){delete f[e.header]})),b=i[qt],w=(!1===i[Bt]||!1===i[Gt])&&(0,X.Uf)(),I=!1===i[Bt]&&(0,X.Uf)(),S=!1===i[Gt]&&(0,X.Uf)(),L=i[Kt],U=!!i[Vt],q=i.retryCodes;var c=i[Rt],l=!!i[jt]&&(!!c||Oe()),h=i.namePrefix,p=l!==D||l&&R!==h||l&&x!==c;if(e[Zt]){if(p)try{e._buffer=e._buffer[kt](B,i,l)}catch(t){(0,F.ZP)(e[Ot](),1,12,"failed to transfer telemetry to different buffer storage, telemetry will be lost: "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}ne()}else e[Zt]=l?new Rn(B,i):new Dn(B,i);R=h,D=l,x=c,N=!i[Wt]&&(0,X.R7)(!0),$=!!i[Qt],e._sample=new Nn(i.samplingPercentage,B),E=i[Ft],(0,z.$XS)(E)||function(e,t){var n=t.disableInstrumentationKeyValidation;return!((0,z.hXl)(n)||!n)||new RegExp("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").test(e)}(E,r)||(0,F.ZP)(B,1,100,"Invalid Instrumentation key "+E),P=i[Xt],(0,z.KgX)(g)&&!Ce(g)&&P&&P[ht]>0?(0,z.Iuo)(P,(function(e){d.addHeader(e.header,e.value)})):P=null,A=i[Jt];var m=function(){var t;try{var n={xdrOnComplete:function(e,t,n){var r=V(n);if(r)return G(e,r)},fetchOnComplete:function(e,t,n,r){var i=V(r);if(i)return re(e.status,i,e.url,i[ht],e.statusText,n||"")},xhrOnComplete:function(e,t,n){var r=V(n);if(r)return K(e,r,r[ht])},beaconOnRetry:function(t,n,r){return function(t,n,r){var i=t&&t[ln];if($)O&&O(i,!0),(0,F.ZP)(e[Ot](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender.");else{for(var o=[],a=0;a<i[ht];a++){var s=i[a],u=[s];r(ee(u),n)?e._onSuccess(u,u[ht]):o[Ct](s)}o[ht]>0&&(O&&O(o,!0),(0,F.ZP)(e[Ot](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}(t,n,r)}};return(t={})[Jt]=A,t.isOneDs=!1,t.disableCredentials=!1,t[Vt]=U,t.disableBeacon=!S,t.disableBeaconSync=!I,t.senderOnCompleteCallBack=n,t}catch(e){}return null}();M?M.SetConfig(m):(M=new Ge.v)[$t](m,B);var j=i.httpXHROverride,Z=null,W=null,Q=(0,xe.jL)([3,1,2],i.transports);Z=M&&M[Yt](Q,!1);var J=M&&M.getFallbackInst();H=function(e,t){return Y(J,e,t)},O=function(e,t){return Y(J,e,t,!1)},Z=L?j:Z||j||J,e[Mt]=function(e,t){return Y(Z,e,t)},N&&(v=se);var te=(0,xe.jL)([3,1],i[en]);N||(te=te.filter((function(e){return 2!==e}))),W=M&&M[Yt](te,!0),W=L?j:W||j,(L||i[en]||!v)&&W&&(v=function(e,t){return Y(W,e,t)}),v||(v=H),C=i.disableTelemetry,_=i[tn]||Hn,T=i.isRetryDisabled,k=i[nn]})))},e.processTelemetry=function(t,n){var r,i=(n=e._getTelCtx(n))[Ot]();try{if(!j(t,i))return;var o=Z(t,i);if(!o)return;var a=c[rn](o),s=e[Zt];ne(a);var u=((r={})[Et]=a,r.cnt=0,r);s[It](u),ce()}catch(e){(0,F.ZP)(i,2,12,"Failed adding telemetry to the sender's buffer, some telemetry will be lost: "+(0,xe.lL)(e),{exception:(0,z.mmD)(e)})}e.processNext(t,n)},e.isCompletelyIdle=function(){return!s&&0===B&&0===e._buffer[St]()},e.getOfflineListener=function(){return h},e._xhrReadyStateChange=function(e,t,n){if(!ae(t))return K(e,t,n)},e[Ht]=function(t,n,r){var i;if(void 0===t&&(t=!0),!s)try{var o=e[Zt];if(C)o[Tt]();else{if(o[St]()>0){var a=o.getItems();!function(t,n){var r,i=(r="getNotifyMgr",e.core[r]?e.core[r]():e.core._notificationManager);if(i&&i[vn])try{i[vn](t,n)}catch(t){(0,F.ZP)(e[Ot](),1,74,"send request notification failed: "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}}(r||0,t),i=n?n.call(e,a,t):e[Mt](a,t)}new Date}le()}catch(t){var u=(0,X.L0)();(!u||u>9)&&(0,F.ZP)(e[Ot](),1,40,"Telemetry transmission failed, some telemetry will be lost: "+(0,xe.lL)(t),{exception:(0,z.mmD)(t)})}return i},e.getOfflineSupport=function(){var e;return(e={getUrl:function(){return g},createPayload:J})[rn]=W,e.batch=Q,e.shouldProcess=function(e){return!!j(e)},e},e._doTeardown=function(t,n){e.onunloadFlush(),(0,Ke.K)(h,!1),de()},e[on]=function(t,n,r){if(!ae(t))return function(t,n,r){(0,F.ZP)(e[Ot](),2,26,"Failed to send telemetry.",{message:n}),e._buffer&&e._buffer[xt](t)}(t,n)},e[an]=function(t,n){if(!ae(t))return function(t,n){for(var r=[],i=[],o=0,a=n.errors.reverse();o<a.length;o++){var s=a[o],u=t.splice(s.index,1)[0];fe(s.statusCode)?i[Ct](u):r[Ct](u)}t[ht]>0&&e[sn](t,n[cn]),r[ht]>0&&e[on](r,(0,xe.r4)(null,["partial success",n[cn],"of",n.itemsReceived].join(" "))),i[ht]>0&&(ue(i),(0,F.ZP)(e[Ot](),2,40,"Partial success. Delivered: "+t[ht]+", Failed: "+r[ht]+". Will retry to send "+i[ht]+" our of "+n[un]+" items"))}(t,n)},e[sn]=function(t,n){if(!ae(t))return function(t,n){e._buffer&&e._buffer[xt](t)}(t)},e._xdrOnLoad=function(e,t){if(!ae(t))return G(e,t)}})),d}return(0,i.qU)(t,e),t.constructEnvelope=function(e,t,n,r){var o;return o=t===e.iKey||(0,z.hXl)(t)?e:(0,i.Im)((0,i.Im)({},e),{iKey:t}),(Mn[o.baseType]||En)(n,o,r)},t}(je.s);function Fn(e){if(!e)return{};var t=e[f](";"),n=(0,z.KTd)(t,(function(e,t){var n=t[f]("=");if(2===n[d]){var r=n[0][v](),i=n[1];e[r]=i}return e}),{});if((0,z.cGk)(n)[d]>0){if(n.endpointsuffix){var r=n.location?n.location+".":"";n[h]=n[h]||"https://"+r+"dc."+n.endpointsuffix}n[h]=n[h]||u,(0,z.Cv9)(n[h],"/")&&(n[h]=n[h].slice(0,-1))}return n}var Xn,qn=n(2774),Bn=n(8205),Gn="instrumentationKey",Kn="connectionString",Vn="instrumentationkey",jn="endpointUrl",Zn="ingestionendpoint",Wn="userOverrideEndpointUrl",Qn=(0,Re.H)({Verbose:0,Information:1,Warning:2,Error:3,Critical:4}),Jn=void 0,Yn=((Xn={diagnosticLogInterval:(0,Fe.Lx)((function(e){return e&&e>0}),1e4)})[Kn]=Jn,Xn[jn]=Jn,Xn[Gn]=Jn,Xn.extensionConfig={},Xn),er=function(){function e(t){var n,i=new qn._;function o(e){e&&(e.baseData=e.baseData||{},e.baseType=e.baseType||"EventData"),i.track(e)}((0,z.hXl)(t)||(0,z.hXl)(t[Gn])&&(0,z.hXl)(t[Kn]))&&(0,z.$8)("Invalid input configuration"),(0,r.A)(e,this,(function(e){function r(){var e=(0,Xe.e)(t||{},Yn);n=e.cfg,i.addUnloadHook((0,Xe.a)(e,(function(){var e=n[Kn];if((0,z.$XS)(e)){var t=(0,Bn.Rf)((function(t,r){(0,Bn.Dv)(e,(function(e){var r=e.value,i=n[Gn];!e.rejected&&r&&(n[Kn]=r,i=Fn(r)[Vn]||i),t(i)}))})),r=(0,Bn.Rf)((function(t,r){(0,Bn.Dv)(e,(function(e){var r=e.value,i=n[jn];if(!e.rejected&&r){var o=Fn(r)[Zn];i=o?o+c:i}t(i)}))}));n[Gn]=t,n[jn]=n[Wn]||r}if((0,z.KgX)(e)){var i=Fn(e),o=i[Zn];n[jn]=n[Wn]?n[Wn]:o+c,n[Gn]=i[Vn]||n[Gn]}n[jn]=n[Wn]?n[Wn]:n[jn]}))),i.initialize(n,[new zn])}(0,z.vF1)(e,"config",{g:function(){return n}}),r(),e.initialize=r,e.track=o,(0,xe.o$)(e,i,["flush","pollInternalLogs","stopPollingInternalLogs","unload","getPlugin","addPlugin","evtNamespace","addUnloadCb","onCfgChange","getTraceCtx","updateCfg","addTelemetryInitializer"])}))}return e.__ieDyn=1,e}()},8279:(e,t,n)=>{n.d(t,{A:()=>H});var r,i=n(269),o="constructor",a="prototype",s="function",u="_dynInstFuncs",c="_isDynProxy",l="_dynClass",f="_dynCls$",d="_dynInstChk",v=d,h="_dfOpts",p="_unknown_",m="__proto__",g="_dyn"+m,y="__dynProto$Gbl",b="_dynInstProto",w="useBaseInst",I="setInstFuncs",S=Object,P=S.getPrototypeOf,C=S.getOwnPropertyNames,E=(0,i.mS$)(),_=E[y]||(E[y]={o:(r={},r[I]=!0,r[w]=!0,r),n:1e3});function T(e){return e&&(e===S[a]||e===Array[a])}function k(e){return T(e)||e===Function[a]}function D(e){var t;if(e){if(P)return P(e);var n=e[m]||e[a]||(e[o]?e[o][a]:null);t=e[g]||n,(0,i.v0u)(e,g)||(delete e[b],t=e[g]=e[b]||e[g],e[b]=n)}return t}function x(e,t){var n=[];if(C)n=C(e);else for(var r in e)"string"==typeof r&&(0,i.v0u)(e,r)&&n.push(r);if(n&&n.length>0)for(var o=0;o<n.length;o++)t(n[o])}function R(e,t,n){return t!==o&&typeof e[t]===s&&(n||(0,i.v0u)(e,t))&&t!==m&&t!==a}function A(e){(0,i.zkd)("DynamicProto: "+e)}function L(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return!0;return!1}function U(e,t,n,r,o){if(!T(e)){var a=n[u]=n[u]||(0,i.sSX)(null);if(!T(a)){var f=a[t]=a[t]||(0,i.sSX)(null);!1!==a[v]&&(a[v]=!!o),T(f)||x(n,(function(t){R(n,t,!1)&&n[t]!==r[t]&&(f[t]=n[t],delete n[t],(!(0,i.v0u)(e,t)||e[t]&&!e[t][c])&&(e[t]=function(e,t){var n=function(){var r=function(e,t,n,r){var o=null;if(e&&(0,i.v0u)(n,l)){var a=e[u]||(0,i.sSX)(null);if((o=(a[n[l]]||(0,i.sSX)(null))[t])||A("Missing ["+t+"] "+s),!o[d]&&!1!==a[v]){for(var c=!(0,i.v0u)(e,t),f=D(e),h=[];c&&f&&!k(f)&&!L(h,f);){var p=f[t];if(p){c=p===r;break}h.push(f),f=D(f)}try{c&&(e[t]=o),o[d]=1}catch(e){a[v]=!1}}}return o}(this,t,e,n)||function(e,t,n){var r=t[e];return r===n&&(r=D(t)[e]),typeof r!==s&&A("["+e+"] is not a "+s),r}(t,e,n);return r.apply(this,arguments)};return n[c]=1,n}(e,t)))}))}}}function N(e,t){return(0,i.v0u)(e,a)?e.name||t||p:((e||{})[o]||{}).name||t||p}function H(e,t,n,r){(0,i.v0u)(e,a)||A("theClass is an invalid class definition.");var o=e[a];(function(e,t){if(P){for(var n=[],r=D(t);r&&!k(r)&&!L(n,r);){if(r===e)return!0;n.push(r),r=D(r)}return!1}return!0})(o,t)||A("["+N(e)+"] not in hierarchy of ["+N(t)+"]");var s=null;(0,i.v0u)(o,l)?s=o[l]:(s=f+N(e,"_")+"$"+_.n,_.n++,o[l]=s);var d=H[h],p=!!d[w];p&&r&&void 0!==r[w]&&(p=!!r[w]);var m=function(e){var t=(0,i.sSX)(null);return x(e,(function(n){!t[n]&&R(e,n,!1)&&(t[n]=e[n])})),t}(t),g=function(e,t,n,r){function o(e,t,n){var i=t[n];if(i[c]&&r){var o=e[u]||{};!1!==o[v]&&(i=(o[t[l]]||{})[n]||i)}return function(){return i.apply(e,arguments)}}var a=(0,i.sSX)(null);x(n,(function(e){a[e]=o(t,n,e)}));for(var s=D(e),f=[];s&&!k(s)&&!L(f,s);)x(s,(function(e){!a[e]&&R(s,e,!P)&&(a[e]=o(t,s,e))})),f.push(s),s=D(s);return a}(o,t,m,p);n(t,g);var y=!!P&&!!d[I];y&&r&&(y=!!r[I]),U(o,s,t,m,!1!==y)}H[h]=_.o},8205:(e,t,n)=>{n.d(t,{Dv:()=>c,Qo:()=>L,Rf:()=>R,Xf:()=>U,lh:()=>A});var r,i,o,a=n(269),s="Promise",u="rejected";function c(e,t){return l(e,(function(e){return t?t({status:"fulfilled",rejected:!1,value:e}):e}),(function(e){return t?t({status:u,rejected:!0,reason:e}):e}))}function l(e,t,n,r){var i=e;try{if((0,a.$XS)(e))(t||n)&&(i=e.then(t,n));else try{t&&(i=t(e))}catch(e){if(!n)throw e;i=n(e)}}finally{r&&function(e,t){var n=e;t&&((0,a.$XS)(e)?n=e.finally?e.finally(t):e.then((function(e){return t(),e}),(function(e){throw t(),e})):t())}(i,r)}return i}var f,d=!1,v=["pending","resolving","resolved",u],h="dispatchEvent";function p(e){var t;return e&&e.createEvent&&(t=e.createEvent("Event")),!!t&&t.initEvent}var m,g,y,b,w="unhandledRejection",I=w.toLowerCase(),S=[],P=0,C=10;function E(e){return(0,a.Tnt)(e)?e.toString():(0,a.mmD)(e)}function _(e,t,n){var u,c,l=(0,a.KVm)(arguments,3),g=0,y=!1,b=[],_=P++,T=S.length>0?S[S.length-1]:void 0,k=!1,D=null;function x(t,n){try{return S.push(_),k=!0,D&&D.cancel(),D=null,e((function(e,r){b.push((function(){try{var i=2===g?t:n,o=(0,a.b07)(i)?u:(0,a.Tnt)(i)?i(u):i;(0,a.$XS)(o)?o.then(e,r):i?e(o):3===g?r(o):e(o)}catch(e){r(e)}})),y&&A()}),l)}finally{S.pop()}}function R(){return v[g]}function A(){if(b.length>0){var e=b.slice();b=[],k=!0,D&&D.cancel(),D=null,t(e)}}function L(e,t){return function(n){if(g===t){if(2===e&&(0,a.$XS)(n))return g=1,void n.then(L(2,1),L(3,1));g=e,y=!0,u=n,A(),k||3!==e||D||(D=(0,a.dRz)(U,C))}}}function U(){if(!k)if(k=!0,(0,a.Lln)())process.emit(w,u,c);else{var e=(0,a.zkX)()||(0,a.mS$)();!m&&(m=(0,a.GuU)((0,a.gBW)(a.zS2,[s+"RejectionEvent"]).v)),function(e,t,n,r){var i=(0,a.YEm)();!f&&(f=(0,a.GuU)(!!(0,a.gBW)(p,[i]).v));var o=f.v?i.createEvent("Event"):r?new Event(t):{};if(n&&n(o),f.v&&o.initEvent(t,!1,!0),o&&e[h])e[h](o);else{var s=e["on"+t];if(s)s(o);else{var u=(0,a.zS2)("console");u&&(u.error||u.log)(t,(0,a.mmD)(o))}}}(e,I,(function(e){return(0,a.vF1)(e,"promise",{g:function(){return c}}),e.reason=u,e}),!!m.v)}}return c={then:x,catch:function(e){return x(void 0,e)},finally:function(e){var t=e,n=e;return(0,a.Tnt)(e)&&(t=function(t){return e&&e(),t},n=function(t){throw e&&e(),t}),x(t,n)}},(0,a.UxO)(c,"state",{get:R}),d&&function(e,t,n,s){i=i||{toString:function(){return"[[PromiseResult]]"}},o=o||{toString:function(){return"[[PromiseIsHandled]]"}};var c={};c[r=r||{toString:function(){return"[[PromiseState]]"}}]={get:t},c[i]={get:function(){return(0,a.SZ2)(u)}},c[o]={get:function(){return k}},(0,a.isD)(e,c)}(c,R),(0,a.Lok)()&&(c[(0,a.Y0g)(11)]="IPromise"),c.toString=function(){return"IPromise"+(d?"["+_+((0,a.b07)(T)?"":":"+T)+"]":"")+" "+R()+(y?" - "+E(u):"")},function(){(0,a.Tnt)(n)||(0,a.zkd)(s+": executor is not a function - "+E(n));var e=L(3,0);try{n.call(c,L(2,0),e)}catch(t){e(t)}}(),c}function T(e){return function(t){var n=(0,a.KVm)(arguments,1);return e((function(e,n){try{var r=[],i=1;(0,a.DA8)(t,(function(t,o){t&&(i++,l(t,(function(t){r[o]=t,0==--i&&e(r)}),n))})),0==--i&&e(r)}catch(e){n(e)}}),n)}}function k(e){(0,a.Iuo)(e,(function(e){try{e()}catch(e){}}))}function D(e,t){return _(D,function(e){var t=(0,a.EtT)(e)?e:0;return function(e){(0,a.dRz)((function(){k(e)}),t)}}(t),e,t)}function x(e,t){!g&&(g=(0,a.GuU)((0,a.gBW)(a.zS2,[s]).v||null));var n=g.v;if(!n)return D(e);(0,a.Tnt)(e)||(0,a.zkd)(s+": executor is not a function - "+(0,a.mmD)(e));var r=0,i=new n((function(t,n){e((function(e){r=2,t(e)}),(function(e){r=3,n(e)}))}));return(0,a.UxO)(i,"state",{get:function(){return v[r]}}),i}function R(e){return _(R,k,e)}function A(e,t){return!y&&(n=R,y=(0,a.GuU)((function(e){var t=(0,a.KVm)(arguments,1);return n((function(t,n){var r=[],i=1;function o(e,n){i++,c(e,(function(e){e.rejected?r[n]={status:u,reason:e.reason}:r[n]={status:"fulfilled",value:e.value},0==--i&&t(r)}))}try{(0,a.cyL)(e)?(0,a.Iuo)(e,o):(0,a.xZI)(e)?(0,a.DA8)(e,o):(0,a.zkd)("Input is not an iterable"),0==--i&&t(r)}catch(e){n(e)}}),t)}))),y.v(e,t);var n}function L(e,t){return!b&&(b=(0,a.GuU)(x)),b.v.call(this,e,t)}var U=T(L);(0,a.Y0g)(11)},269:(e,t,n)=>{function r(e,t){return e||t}function i(e,t){return e[t]}n.d(t,{$8:()=>le,$PY:()=>F,$XS:()=>G,AHH:()=>dn,Cv9:()=>Yt,DA8:()=>bt,EHq:()=>Ft,Edw:()=>L,EtT:()=>X,FJj:()=>Qt,GuU:()=>Ke,Gvm:()=>M,HzD:()=>tn,Iuo:()=>St,JKf:()=>ut,KTd:()=>Dt,KVm:()=>Et,KgX:()=>O,KhI:()=>W,Lln:()=>ct,Lmq:()=>q,Lok:()=>dt,N6t:()=>ye,Nq2:()=>kt,O9V:()=>H,P0f:()=>Ue,QdQ:()=>Xt,R3R:()=>Wt,SZ2:()=>A,Tnt:()=>$,UUD:()=>Jt,UxO:()=>te,Vdv:()=>et,W$7:()=>Ct,WSA:()=>Ie,Wtk:()=>Je,Y0g:()=>ht,YEm:()=>Ye,Yny:()=>It,ZHX:()=>ge,ZWZ:()=>Oe,aqQ:()=>Ut,b07:()=>U,bJ7:()=>B,cGk:()=>me,cyL:()=>z,dRz:()=>ln,eCG:()=>mt,f0d:()=>Nt,fn0:()=>Pe,gBW:()=>D,hKY:()=>Ne,hXl:()=>N,isD:()=>ne,jjc:()=>pt,jsL:()=>be,kgX:()=>j,mS$:()=>We,mmD:()=>ce,nRs:()=>Ge,oJg:()=>se,rDm:()=>Pt,raO:()=>re,sSX:()=>xt,tGl:()=>nn,twz:()=>it,v0u:()=>Z,vE3:()=>pe,vF1:()=>ie,vKV:()=>fn,w3n:()=>rt,w9M:()=>nt,woc:()=>K,xZI:()=>yt,zS2:()=>Qe,zav:()=>J,zkX:()=>tt,zkd:()=>fe,zwS:()=>jt,zzB:()=>V});var o,a=void 0,s=null,u="",c="function",l="object",f="prototype",d="__proto__",v="undefined",h="constructor",p="Symbol",m="_polyfill",g="length",y="name",b="call",w="toString",I=r(Object),S=i(I,f),P=r(String),C=i(P,f),E=r(Math),_=r(Array),T=i(_,f),k=i(T,"slice");function D(e,t){try{return{v:e.apply(this,t)}}catch(e){return{e}}}function x(e){return function(t){return typeof t===e}}function R(e){var t="[object "+e+"]";return function(e){return!(!e||A(e)!==t)}}function A(e){return S[w].call(e)}function L(e,t){return typeof e===t}function U(e){return typeof e===v||e===v}function N(e){return e===s||U(e)}function H(e){return!!e||e!==a}var O=x("string"),$=x(c);function M(e){return!(!e&&N(e)||!e||typeof e!==l)}var z=i(_,"isArray"),F=R("Date"),X=x("number"),q=x("boolean"),B=R("Error");function G(e){return!!(e&&e.then&&$(e.then))}function K(e){return!e||!V(e)}function V(e){return!(!e||(t=function(){return!(e&&0+e)},n=!e,r=D(t),r.e?n:r.v));var t,n,r}var j=i(I,"getOwnPropertyDescriptor");function Z(e,t){return!!e&&S.hasOwnProperty[b](e,t)}var W=r(i(I,"hasOwn"),Q);function Q(e,t){return Z(e,t)||!!j(e,t)}function J(e,t,n){if(e&&M(e))for(var r in e)if(W(e,r)&&-1===t[b](n||e,r,e[r]))break}var Y={e:"enumerable",c:"configurable",v:"value",w:"writable",g:"get",s:"set"};function ee(e){var t={};if(t[Y.c]=!0,t[Y.e]=!0,e.l){t.get=function(){return e.l.v};var n=j(e.l,"v");n&&n.set&&(t.set=function(t){e.l.v=t})}return J(e,(function(e,n){t[Y[e]]=H(n)?n:t[Y[e]]})),t}var te=i(I,"defineProperty"),ne=i(I,"defineProperties");function re(e,t,n,r,i,o){var a={e:o,c:i};return n&&(a.g=n),r&&(a.s=r),te(e,t,ee(a))}function ie(e,t,n){return te(e,t,ee(n))}function oe(e,t,n,r,i){var o={};return J(e,(function(e,r){ae(o,e,t?r:e,i),ae(o,r,n?r:e,i)})),r?r(o):o}function ae(e,t,n,r){te(e,t,{value:n,enumerable:!0,writable:!!r})}var se=r(P),ue="[object Error]";function ce(e,t){var n=u,r=S[w][b](e);r===ue&&(e={stack:se(e.stack),message:se(e.message),name:se(e.name)});try{n=((n=JSON.stringify(e,s,t?"number"==typeof t?t:4:a))?n.replace(/"(\w+)"\s*:\s{0,1}/g,"$1: "):s)||se(e)}catch(e){n=" - "+ce(e,t)}return r+": "+n}function le(e){throw new Error(e)}function fe(e){throw new TypeError(e)}var de=i(I,"freeze");function ve(e){return e}function he(e){return e[d]||s}var pe=i(I,"assign"),me=i(I,"keys");function ge(e){return de&&J(e,(function(e,t){(z(t)||M(t))&&ge(t)})),ye(e)}var ye=r(de,ve),be=r(i(I,"seal"),ve),we=r(i(I,"getPrototypeOf"),he);function Ie(e){return oe(e,1,0,ye)}function Se(e){return oe(e,0,0,ye)}function Pe(e){return function(e){var t={};return J(e,(function(e,n){ae(t,e,n[1]),ae(t,n[0],n[1])})),ye(t)}(e)}var Ce,Ee=Se({asyncIterator:0,hasInstance:1,isConcatSpreadable:2,iterator:3,match:4,matchAll:5,replace:6,search:7,species:8,split:9,toPrimitive:10,toStringTag:11,unscopables:12}),_e="__tsUtils$gblCfg";function Te(){var e;return typeof globalThis!==v&&(e=globalThis),e||typeof self===v||(e=self),e||typeof window===v||(e=window),e||typeof global===v||(e=global),e}function ke(){if(!Ce){var e=D(Te).v||{};Ce=e[_e]=e[_e]||{}}return Ce}var De=xe;function xe(e,t,n){var r=t?t[e]:s;return function(t){var i=(t?t[e]:s)||r;if(i||n){var o=arguments;return(i||n).apply(t,i?k[b](o,1):o)}fe('"'+se(e)+'" not defined for '+ce(t))}}function Re(e){return function(t){return t[e]}}var Ae=i(E,"max"),Le=De("slice",C),Ue=De("substring",C),Ne=xe("substr",C,He);function He(e,t,n){return N(e)&&fe("Invalid "+ce(e)),n<0?u:((t=t||0)<0&&(t=Ae(t+e[g],0)),U(n)?Le(e,t):Le(e,t,t+n))}function Oe(e,t){return Ue(e,0,t)}var $e,Me,ze,Fe="_urid";function Xe(e){var t={description:se(e),toString:function(){return p+"("+e+")"}};return t[m]=!0,t}function qe(e){var t=function(){if(!$e){var e=ke();$e=e.gblSym=e.gblSym||{k:{},s:{}}}return $e}();if(!W(t.k,e)){var n=Xe(e),r=me(t.s).length;n[Fe]=function(){return r+"_"+n[w]()},t.k[e]=n,t.s[n[Fe]()]=se(e)}return t.k[e]}function Be(){ze=ke()}function Ge(e){var t={};return!ze&&Be(),t.b=ze.lzy,te(t,"v",{configurable:!0,get:function(){var n=e();return ze.lzy||te(t,"v",{value:n}),t.b=ze.lzy,n}}),t}function Ke(e){return te({toJSON:function(){return e}},"v",{value:e})}var Ve,je="window";function Ze(e,t){var n;return function(){return!ze&&Be(),n&&!ze.lzy||(n=Ke(D(e,t).v)),n.v}}function We(e){return!ze&&Be(),Ve&&!1!==e&&!ze.lzy||(Ve=Ke(D(Te).v||s)),Ve.v}function Qe(e,t){var n;if((n=Ve&&!1!==t?Ve.v:We(t))&&n[e])return n[e];if(e===je)try{return window}catch(e){}return s}function Je(){return!!Ye()}var Ye=Ze(Qe,["document"]);function et(){return!!tt()}var tt=Ze(Qe,[je]);function nt(){return!!rt()}var rt=Ze(Qe,["navigator"]);function it(){return!!ut()}var ot,at,st,ut=Ze(Qe,["history"]),ct=Ze((function(){return!!D((function(){return process&&(process.versions||{}).node})).v}));function lt(){return ot=Ke(D(Qe,[p]).v)}function ft(e){var t=(ze.lzy?0:ot)||lt();return t.v?t.v[e]:a}function dt(){return!!vt()}function vt(){return!ze&&Be(),((ze.lzy?0:ot)||lt()).v}function ht(e,t){var n=Ee[e];!ze&&Be();var r=(ze.lzy?0:ot)||lt();return r.v?r.v[n||e]:t?a:function(e){var t;!Me&&(Me={});var n=Ee[e];return n&&(t=Me[n]=Me[n]||Xe(p+"."+n)),t}(e)}function pt(e,t){!ze&&Be();var n=(ze.lzy?0:ot)||lt();return n.v?n.v(e):t?s:Xe(e)}function mt(e){return!ze&&Be(),((at=(ze.lzy?0:at)||Ke(D(ft,["for"]).v)).v||qe)(e)}function gt(e){return!!e&&$(e.next)}function yt(e){return!function(e){return e===s||!H(e)}(e)&&$(e[ht(3)])}function bt(e,t,n){if(e&&(gt(e)||(!st&&(st=Ke(ht(3))),e=e[st.v]?e[st.v]():s),gt(e))){var r=a,i=a;try{for(var o=0;!(i=e.next()).done&&-1!==t[b](n||e,i.value,o,e);)o++}catch(t){r={e:t},e.throw&&(i=s,e.throw(r))}finally{try{i&&!i.done&&e.return&&e.return(i)}finally{if(r)throw r.e}}}}function wt(e,t,n){return e.apply(t,n)}function It(e,t){return!U(t)&&e&&(z(t)?wt(e.push,e,t):gt(t)||yt(t)?bt(t,(function(t){e.push(t)})):e.push(t)),e}function St(e,t,n){if(e)for(var r=e[g]>>>0,i=0;i<r&&(!(i in e)||-1!==t[b](n||e,e[i],i,e));i++);}var Pt=De("indexOf",T),Ct=De("map",T);function Et(e,t,n){return((e?e.slice:s)||k).apply(e,k[b](arguments,1))}function _t(e,t,n){return-1!==Pt(e,t,n)}var Tt,kt=xe("includes",T,_t),Dt=De("reduce",T),xt=r(i(I,"create"),Rt);function Rt(e){if(!e)return{};var t=typeof e;function n(){}return t!==l&&t!==c&&fe("Prototype must be an Object or function: "+ce(e)),n[f]=e,new n}function At(e,t){return(I.setPrototypeOf||function(e,t){var n;!Tt&&(Tt=Ke(((n={})[d]=[],n instanceof Array))),Tt.v?e[d]=t:J(t,(function(t,n){return e[t]=n}))})(e,t)}function Lt(e,t){t&&(e[y]=t)}function Ut(e,t,n){var r=n||Error,i=r[f][y],o=Error.captureStackTrace;return function(e,t,n){function r(){this[h]=t,D(ie,[this,y,{v:e,c:!0,e:!1}])}return D(ie,[t,y,{v:e,c:!0,e:!1}]),(t=At(t,n))[f]=n===s?xt(n):(r[f]=n[f],new r),t}(e,(function(){var n=this,a=arguments;try{D(Lt,[r,e]);var s=wt(r,n,k[b](a))||n;if(s!==n){var u=we(n);u!==we(s)&&At(s,u)}return o&&o(s,n[h]),t&&t(s,a),s}finally{D(Lt,[r,i])}}),r)}function Nt(){return(Date.now||Ht)()}function Ht(){return(new Date).getTime()}function Ot(e){return function(t){return N(t)&&fe("strTrim called ["+ce(t)+"]"),t&&t.replace&&(t=t.replace(e,u)),t}}var $t,Mt,zt,Ft=xe("trim",C,Ot(/^\s+|(?=\s)\s+$/g));function Xt(e){if(!e||typeof e!==l)return!1;zt||(zt=!et()||tt());var t=!1;if(e!==zt){Mt||($t=Function[f][w],Mt=$t[b](I));try{var n=we(e);(t=!n)||(Z(n,h)&&(n=n[h]),t=!(!n||typeof n!==c||$t[b](n)!==Mt))}catch(e){}}return t}function qt(e){return e.value&&Vt(e),!0}var Bt=[function(e){var t=e.value;if(z(t)){var n=e.result=[];return n.length=t.length,e.copyTo(n,t),!0}return!1},Vt,function(e){return e.type===c},function(e){var t=e.value;return!!F(t)&&(e.result=new Date(t.getTime()),!0)}];function Gt(e,t,n,r){var i=n.handler,a=n.path?r?n.path.concat(r):n.path:[],u={handler:n.handler,src:n.src,path:a},c=typeof t,f=!1,d=t===s;d||(t&&c===l?f=Xt(t):d=function(e){return!o&&(o=["string","number","boolean",v,"symbol","bigint"]),!(e===l||-1===o.indexOf(e))}(c));var h={type:c,isPrim:d,isPlain:f,value:t,result:t,path:a,origin:n.src,copy:function(t,r){return Gt(e,t,r?u:n,r)},copyTo:function(t,n){return Kt(e,t,n,u)}};return h.isPrim?i&&i[b](n,h)?h.result:t:function(e,t,r,o){var a;return St(e,(function(e){if(e.k===t)return a=e,-1})),a||(a={k:t,v:t},e.push(a),function(e){ie(h,"result",{g:function(){return e.v},s:function(t){e.v=t}});for(var t=0,r=i;!(r||(t<Bt.length?Bt[t++]:qt))[b](n,h);)r=s}(a)),a.v}(e,t)}function Kt(e,t,n,r){if(!N(n))for(var i in n)t[i]=Gt(e,n[i],r,i);return t}function Vt(e){var t=e.value;if(t&&e.isPlain){var n=e.result={};return e.copyTo(n,t),!0}return!1}function jt(e,t,n,r,i,o,a){return function(e,t){return St(t,(function(t){!function(e,t,n){Kt([],e,t,{handler:void 0,src:t,path:[]})}(e,t)})),e}(Gt([],s=e,{handler:undefined,src:s})||{},k[b](arguments));var s}var Zt,Wt=Re(g);function Qt(){return!ze&&Be(),Zt&&!ze.lzy||(Zt=Ke(D(Qe,["performance"]).v)),Zt.v}function Jt(){var e=Qt();return e&&e.now?e.now():Nt()}dt();var Yt=xe("endsWith",C,en);function en(e,t,n){O(e)||fe("'"+ce(e)+"' is not a string");var r=O(t)?t:se(t),i=!U(n)&&n<e[g]?n:e[g];return Ue(e,i-r[g],i)===r}var tn=De("indexOf",C),nn=xe("startsWith",C,rn);function rn(e,t,n){O(e)||fe("'"+ce(e)+"' is not a string");var r=O(t)?t:se(t),i=n>0?n:0;return Ue(e,i,i+r[g])===r}var on="ref",an="unref",sn="hasRef",un="enabled";function cn(e,t,n){var r=z(t),i=r?t.length:0,o=(i>0?t[0]:r?a:t)||setTimeout,u=(i>1?t[1]:a)||clearTimeout,c=n[0];n[0]=function(){l.dn(),wt(c,a,k[b](arguments))};var l=function(e,t,n){var r,i=!0,o=e?t(s):s;function a(){return i=!1,o&&o[an]&&o[an](),r}function u(){o&&n(o),o=s}function c(){return o=t(o),i||a(),r}return(r={cancel:u,refresh:c})[sn]=function(){return o&&o[sn]?o[sn]():i},r[on]=function(){return i=!0,o&&o[on]&&o[on](),r},r[an]=a,{h:r=te(r,un,{get:function(){return!!o},set:function(e){!e&&o&&u(),e&&!o&&c()}}),dn:function(){o=s}}}(e,(function(e){if(e){if(e.refresh)return e.refresh(),e;wt(u,a,[e])}return wt(o,a,n)}),(function(e){wt(u,a,[e])}));return l.h}function ln(e,t){return cn(!0,a,k[b](arguments))}function fn(e,t,n){return cn(!0,e,k[b](arguments,1))}function dn(e,t){return cn(!1,a,k[b](arguments))}},803:(e,t,n)=>{e.exports=n(7469)},7469:(e,t,n)=>{n(9278);var r,i=n(4756),o=n(8611),a=(n(5692),n(4434)),s=(n(2613),n(9023));function u(e){var t=this;t.options=e||{},t.proxyOptions=t.options.proxy||{},t.maxSockets=t.options.maxSockets||o.Agent.defaultMaxSockets,t.requests=[],t.sockets=[],t.on("free",(function(e,n,r,i){for(var o=l(n,r,i),a=0,s=t.requests.length;a<s;++a){var u=t.requests[a];if(u.host===o.host&&u.port===o.port)return t.requests.splice(a,1),void u.request.onSocket(e)}e.destroy(),t.removeSocket(e)}))}function c(e,t){var n=this;u.prototype.createSocket.call(n,e,(function(r){var o=e.request.getHeader("host"),a=f({},n.options,{socket:r,servername:o?o.replace(/:.*$/,""):e.host}),s=i.connect(0,a);n.sockets[n.sockets.indexOf(r)]=s,t(s)}))}function l(e,t,n){return"string"==typeof e?{host:e,port:t,localAddress:n}:e}function f(e){for(var t=1,n=arguments.length;t<n;++t){var r=arguments[t];if("object"==typeof r)for(var i=Object.keys(r),o=0,a=i.length;o<a;++o){var s=i[o];void 0!==r[s]&&(e[s]=r[s])}}return e}t.httpsOverHttp=function(e){var t=new u(e);return t.request=o.request,t.createSocket=c,t.defaultPort=443,t},s.inherits(u,a.EventEmitter),u.prototype.addRequest=function(e,t,n,r){var i=this,o=f({request:e},i.options,l(t,n,r));i.sockets.length>=this.maxSockets?i.requests.push(o):i.createSocket(o,(function(t){function n(){i.emit("free",t,o)}function r(e){i.removeSocket(t),t.removeListener("free",n),t.removeListener("close",r),t.removeListener("agentRemove",r)}t.on("free",n),t.on("close",r),t.on("agentRemove",r),e.onSocket(t)}))},u.prototype.createSocket=function(e,t){var n=this,i={};n.sockets.push(i);var o=f({},n.proxyOptions,{method:"CONNECT",path:e.host+":"+e.port,agent:!1,headers:{host:e.host+":"+e.port}});e.localAddress&&(o.localAddress=e.localAddress),o.proxyAuth&&(o.headers=o.headers||{},o.headers["Proxy-Authorization"]="Basic "+new Buffer(o.proxyAuth).toString("base64")),r("making CONNECT request");var a=n.request(o);function s(o,s,u){var c;return a.removeAllListeners(),s.removeAllListeners(),200!==o.statusCode?(r("tunneling socket could not be established, statusCode=%d",o.statusCode),s.destroy(),(c=new Error("tunneling socket could not be established, statusCode="+o.statusCode)).code="ECONNRESET",e.request.emit("error",c),void n.removeSocket(i)):u.length>0?(r("got illegal response body from proxy"),s.destroy(),(c=new Error("got illegal response body from proxy")).code="ECONNRESET",e.request.emit("error",c),void n.removeSocket(i)):(r("tunneling connection has established"),n.sockets[n.sockets.indexOf(i)]=s,t(s))}a.useChunkedEncodingByDefault=!1,a.once("response",(function(e){e.upgrade=!0})),a.once("upgrade",(function(e,t,n){process.nextTick((function(){s(e,t,n)}))})),a.once("connect",s),a.once("error",(function(t){a.removeAllListeners(),r("tunneling socket could not be established, cause=%s\n",t.message,t.stack);var o=new Error("tunneling socket could not be established, cause="+t.message);o.code="ECONNRESET",e.request.emit("error",o),n.removeSocket(i)})),a.end()},u.prototype.removeSocket=function(e){var t=this.sockets.indexOf(e);if(-1!==t){this.sockets.splice(t,1);var n=this.requests.shift();n&&this.createSocket(n,(function(e){n.request.onSocket(e)}))}},r=process.env.NODE_DEBUG&&/\btunnel\b/.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments);"string"==typeof e[0]?e[0]="TUNNEL: "+e[0]:e.unshift("TUNNEL:"),console.error.apply(console,e)}:function(){}},2613:t=>{t.exports=e(import.meta.url)("assert")},4434:t=>{t.exports=e(import.meta.url)("events")},8611:t=>{t.exports=e(import.meta.url)("http")},5692:t=>{t.exports=e(import.meta.url)("https")},9278:t=>{t.exports=e(import.meta.url)("net")},4756:t=>{t.exports=e(import.meta.url)("tls")},9023:t=>{t.exports=e(import.meta.url)("util")}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return i[e](n,n.exports,a),n.exports}a.m=i,a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce(((t,n)=>(a.f[n](e,t),t)),[])),a.u=e=>e+".js",a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n={606:0},r=e=>{var t,r,{ids:i,modules:o,runtime:s}=e,u=0;for(t in o)a.o(o,t)&&(a.m[t]=o[t]);for(s&&s(a);u<i.length;u++)r=i[u],a.o(n,r)&&n[r]&&n[r][0](),n[i[u]]=0},a.f.j=(e,t)=>{var i=a.o(n,e)?n[e]:void 0;if(0!==i)if(i)t.push(i[1]);else{var o=import("./"+a.u(e)).then(r,(t=>{throw 0!==n[e]&&(n[e]=void 0),t}));o=Promise.race([o,new Promise((t=>i=n[e]=[t]))]),t.push(i[1]=o)}};var s={};a.d(s,{Y:()=>ye});const u=t;var c=a(5692);const l=e(import.meta.url)("os");var f,d=a(2845);class v{static applyReplacements(e,t){for(const n of Object.keys(e))for(const r of t)r.lookup.test(n)&&(void 0!==r.replacementString?e[n]=r.replacementString:delete e[n])}static shouldUseOneDataSystemSDK(e){return 74===e.length&&"-"===e[32]&&"-"===e[41]&&"-"===e[46]&&"-"===e[51]&&"-"===e[56]&&"-"===e[69]}static getAdditionalCommonProperties(e){return{"common.os":e.platform,"common.nodeArch":e.architecture,"common.platformversion":(e.release||"").replace(/^(\d+)(\.\d+)?(\.\d+)?(.*)/,"$1$2$3"),"common.telemetryclientversion":"PACKAGE_JSON_VERSION"}}static getInstance(){return v._instance||(v._instance=new v),v._instance}}class h{constructor(e,t,n){this.telemetrySender=e,this.vscodeAPI=t,this.userOptIn=!1,this.errorOptIn=!1,this.disposables=[],this._onDidChangeTelemetryLevel=new this.vscodeAPI.EventEmitter,this.onDidChangeTelemetryLevel=this._onDidChangeTelemetryLevel.event,this.telemetryLogger=this.vscodeAPI.env.createTelemetryLogger(this.telemetrySender,n),this.updateUserOptIn(),this.telemetryLogger.onDidChangeEnableStates((()=>{this.updateUserOptIn()}))}updateUserOptIn(){this.errorOptIn=this.telemetryLogger.isErrorsEnabled,this.userOptIn=this.telemetryLogger.isUsageEnabled,(this.telemetryLogger.isErrorsEnabled||this.telemetryLogger.isUsageEnabled)&&this.telemetrySender.instantiateSender(),this._onDidChangeTelemetryLevel.fire(this.telemetryLevel)}get telemetryLevel(){return this.errorOptIn&&this.userOptIn?"all":this.errorOptIn?"error":"off"}internalSendTelemetryEvent(e,t,n,r){r?this.telemetrySender.sendEventData(e,{properties:t,measurements:n}):this.telemetryLogger.logUsage(e,{properties:t,measurements:n})}sendTelemetryEvent(e,t,n){this.internalSendTelemetryEvent(e,t,n,!1)}sendRawTelemetryEvent(e,t,n){const r={...t};for(const e of Object.keys(r??{})){const t=r[e];"string"==typeof e&&void 0!==t&&(r[e]=new this.vscodeAPI.TelemetryTrustedValue("string"==typeof t?t:t.value))}this.sendTelemetryEvent(e,r,n)}sendDangerousTelemetryEvent(e,t,n){this.telemetrySender.instantiateSender(),this.internalSendTelemetryEvent(e,t,n,!0)}internalSendTelemetryErrorEvent(e,t,n,r){r?this.telemetrySender.sendEventData(e,{properties:t,measurements:n}):this.telemetryLogger.logError(e,{properties:t,measurements:n})}sendTelemetryErrorEvent(e,t,n){this.internalSendTelemetryErrorEvent(e,t,n,!1)}sendDangerousTelemetryErrorEvent(e,t,n){this.telemetrySender.instantiateSender(),this.internalSendTelemetryErrorEvent(e,t,n,!0)}async dispose(){return await this.telemetrySender.dispose(),this.telemetryLogger.dispose(),Promise.all(this.disposables.map((e=>e.dispose())))}}!function(e){e[e.NOT_INSTANTIATED=0]="NOT_INSTANTIATED",e[e.INSTANTIATING=1]="INSTANTIATING",e[e.INSTANTIATED=2]="INSTANTIATED"}(f||(f={}));class p{constructor(e,t){this._instantiationStatus=f.NOT_INSTANTIATED,this._eventQueue=[],this._exceptionQueue=[],this._clientFactory=t,this._key=e}sendEventData(e,t){this._telemetryClient?this._telemetryClient.logEvent(e,t):this._instantiationStatus!==f.INSTANTIATED&&this._eventQueue.push({eventName:e,data:t})}sendErrorData(e,t){if(!this._telemetryClient)return void(this._instantiationStatus!==f.INSTANTIATED&&this._exceptionQueue.push({exception:e,data:t}));const n={stack:e.stack,message:e.message,name:e.name};if(t){const e=t.properties||t;t.properties={...e,...n}}else t={properties:n};this._telemetryClient.logEvent("unhandlederror",t)}async flush(){return this._telemetryClient?.flush()}async dispose(){this._telemetryClient&&(await this._telemetryClient.dispose(),this._telemetryClient=void 0)}_flushQueues(){this._eventQueue.forEach((({eventName:e,data:t})=>this.sendEventData(e,t))),this._eventQueue=[],this._exceptionQueue.forEach((({exception:e,data:t})=>this.sendErrorData(e,t))),this._exceptionQueue=[]}instantiateSender(){this._instantiationStatus===f.NOT_INSTANTIATED&&(this._instantiationStatus=f.INSTANTIATING,this._clientFactory(this._key).then((e=>{this._telemetryClient=e,this._instantiationStatus=f.INSTANTIATED,this._flushQueues()})).catch((e=>{console.error(e),this._instantiationStatus=f.INSTANTIATED})))}}function m(){return{sendPOST:(e,t)=>{const n={method:"POST",headers:{...e.headers,"Content-Type":"application/json","Content-Length":Buffer.byteLength(e.data)}};try{const r=c.request(e.urlString,n,(e=>{e.on("data",(function(n){t(e.statusCode??200,e.headers,n.toString())})),e.on("error",(function(){t(0,{})}))}));r.write(e.data,(e=>{e&&t(0,{})})),r.end()}catch{t(0,{})}}}}class g extends h{constructor(e,t){let n=e=>(async(e,t,n,r,i)=>{let o;try{const t=await Promise.resolve().then(a.bind(a,60)),n={};if(r){const e={alwaysUseXhrOverride:!0,httpXHROverride:r};n[d.Uu]=e}let i;e.startsWith("InstrumentationKey=")||(i=e);const s=i?{instrumentationKey:i}:{connectionString:e};o=new t.ApplicationInsights({...s,disableAjaxTracking:!0,disableExceptionTracking:!0,disableFetchTracking:!0,disableCorrelationHeaders:!0,disableCookiesUsage:!0,autoTrackPageVisitTime:!1,emitLineDelimitedJson:!1,disableInstrumentationKeyValidation:!0,extensionConfig:n})}catch(e){return Promise.reject(e)}return{logEvent:(e,r)=>{const a={...r?.properties,...r?.measurements};i?.length&&v.applyReplacements(a,i),o?.track({name:e,data:a,baseType:"EventData",ext:{user:{id:t,authId:t},app:{sesId:n}},baseData:{name:e,properties:r?.properties,measurements:r?.measurements}})},flush:async()=>{o?.flush(!1)},dispose:async()=>new Promise((e=>{o?.unload(!0,(()=>{e(),o=void 0}),1e3)}))}})(e,u.env.machineId,u.env.sessionId,m(),t);v.shouldUseOneDataSystemSDK(e)&&(n=e=>(async(e,t,n)=>{let r=await(async(e,t,n)=>{const r=await Promise.resolve().then(a.bind(a,956)),i=await Promise.resolve().then(a.bind(a,8916)),o=new r.AppInsightsCore,s=new i.PostChannel,u={instrumentationKey:e,endpointUrl:"https://mobile.events.data.microsoft.com/OneCollector/1.0",loggingLevelTelemetry:0,loggingLevelConsole:0,disableCookiesUsage:!0,disableDbgExt:!0,disableInstrumentationKeyValidation:!0,channels:[[s]]};if(n){u.extensionConfig={};const e={alwaysUseXhrOverride:!0,httpXHROverride:n};u.extensionConfig[s.identifier]=e}const c=t.workspace.getConfiguration("telemetry").get("internalTesting");return o.initialize(u,[]),o.addTelemetryInitializer((e=>{e.ext=e.ext??{},e.ext.web=e.ext.web??{},e.ext.web.consentDetails='{"GPC_DataSharingOptIn":false}',c&&(e.ext.utc=e.ext.utc??{},e.ext.utc.flags=8462029)})),o})(e,t,n);return{logEvent:(e,t)=>{try{r?.track({name:e,baseData:{name:e,properties:t?.properties,measurements:t?.measurements}})}catch(e){throw new Error("Failed to log event to app insights!\n"+e.message)}},flush:async()=>{try{return new Promise(((e,t)=>{r?r.flush(!0,(e=>{e||t("Failed to flush app 1DS!")})):e()}))}catch(e){throw new Error("Failed to flush 1DS!\n"+e.message)}},dispose:async()=>new Promise((e=>{r?r.unload(!1,(()=>{e(),r=void 0}),1e3):e()}))}})(e,u,m()));const r={release:l.release(),platform:l.platform(),architecture:l.arch()},i=new p(e,n);if(e&&0===e.indexOf("AIF-"))throw new Error("AIF keys are no longer supported. Please switch to 1DS keys for 1st party extensions");super(i,u,{additionalCommonProperties:v.getAdditionalCommonProperties(r)})}}var y=a(803);const b=e(import.meta.url)("url");class w{constructor(){this.disposables=new Set}add(e){this.disposables.add(e)}dispose(){for(const e of this.disposables)e.dispose();this.disposables.clear()}}const I=(S=function(e,t){const n=`__$sequence$${t}`;return function(...t){const r=this[n]||Promise.resolve(null),i=async()=>await e.apply(this,t);return this[n]=r.then(i,i),this[n]}},(e,t,n)=>{let r=null,i=null;if("function"==typeof n.value?(r="value",i=n.value):"function"==typeof n.get&&(r="get",i=n.get),!i||!r)throw new Error("not supported");n[r]=S(i,t)});var S;function P(e){const t=/^https:\/\/github\.com\/([^/]+)\/([^/]+?)(\.git)?$/i.exec(e)||/^git@github\.com:([^/]+)\/([^/]+?)(\.git)?$/i.exec(e);return t?{owner:t[1],repo:t[2]}:void 0}function C(e){return!!e.state.remotes.find((e=>e.fetchUrl?P(e.fetchUrl):void 0))}function E(e){const t=e.state.remotes.filter((e=>e.fetchUrl&&P(e.fetchUrl)));if(0!==t.length)return(t.find((e=>"upstream"===e.name))??t.find((e=>"origin"===e.name))??t[0]).fetchUrl}function _(e){const t=E(e);return t?P(t):void 0}class T extends Error{}function k(e=process.env.HTTPS_PROXY){if(!e)return c.globalAgent;try{const{hostname:t,port:n,username:r,password:i}=new b.URL(e),o=r&&i&&`${r}:${i}`;return(0,y.httpsOverHttp)({proxy:{host:t,port:n,proxyAuth:o}})}catch(e){return u.window.showErrorMessage(`HTTPS_PROXY environment variable ignored: ${e.message}`),c.globalAgent}}const D=["repo","workflow","user:email","read:user"];async function x(){return await u.authentication.getSession("github",D,{createIfNone:!0})}let R;function A(){return R||(R=x().then((async e=>{const t=e.accessToken,n=k(),{Octokit:r}=await Promise.all([a.e(698),a.e(430)]).then(a.bind(a,8430));return new r({request:{agent:n},userAgent:"GitHub VSCode",auth:`token ${t}`})})).then(null,(async e=>{throw R=void 0,e}))),R}class L{constructor(){this._onDidChangeSessions=new u.EventEmitter,this.onDidChangeSessions=this._onDidChangeSessions.event,this._disposables=new w,this._disposables.add(this._onDidChangeSessions),this._disposables.add(u.authentication.onDidChangeSessions((e=>{"github"===e.provider.id&&(this._octokitGraphql=void 0,this._onDidChangeSessions.fire())})))}async getOctokitGraphql(){if(!this._octokitGraphql)try{const e=await u.authentication.getSession("github",D,{silent:!0});if(!e)throw new T("No GitHub authentication session available.");const t=e.accessToken,{graphql:n}=await Promise.all([a.e(698),a.e(555)]).then(a.bind(a,6555));return this._octokitGraphql=n.defaults({headers:{authorization:`token ${t}`},request:{agent:k()}}),this._octokitGraphql}catch(e){throw this._octokitGraphql=void 0,new T(e.message)}return this._octokitGraphql}dispose(){this._octokitGraphql=void 0,this._disposables.dispose()}}function U(e,t){return t.path.toLowerCase()===e.rootUri.path.toLowerCase()||t.path.toLowerCase().startsWith(e.rootUri.path.toLowerCase())&&t.path.substring(e.rootUri.path.length).startsWith("/")}function N(e,t){for(const n of e.repositories)if(U(n,t))return n}var H;function O(e){return void 0===e||u.window.activeTextEditor&&!u.window.activeTextEditor.selection.isEmpty&&u.window.activeTextEditor.selection.contains(new u.Position(e-1,0))?u.window.activeTextEditor?.selection:new u.Range(e-1,0,e-1,1)}function $(e){if(!e)return"";let t=`#L${e.start.line+1}`;return e.start.line!==e.end.line&&(t+=`-L${e.end.line+1}`),t}function M(e,t){if(void 0===e)return"";if(!t)return`#C${e+1}`;let n=`#C${e+1}:L${t.start.line+1}`;return t.start.line!==t.end.line&&(n+=`-L${t.end.line+1}`),n}function z(e){return e.split("/").map((e=>encodeURIComponent(e))).join("/")}async function F(e,t,n,r,i="permalink",o,a){r=r??"https://github.com";const s=function(e){let t;const{fileUri:n,lineNumber:r}=function(e){return e instanceof u.Uri?{fileUri:e,lineNumber:void 0}:void 0!==e&&"lineNumber"in e&&"uri"in e?{fileUri:e.uri,lineNumber:e.lineNumber}:{fileUri:void 0,lineNumber:void 0}}(e),i=n??u.window.activeTextEditor?.document.uri;if(i){if("vscode-notebook-cell"===i.scheme&&u.window.activeNotebookEditor?.notebook.uri.fsPath===i.fsPath){const e=u.window.activeNotebookEditor.notebook.getCells().find((e=>e.document.uri.fragment===i?.fragment)),t=e?.index??u.window.activeNotebookEditor.selection.start,n=O(r);return{type:H.Notebook,uri:i,cellIndex:t,range:n}}return t=O(r),{type:H.File,uri:i,range:t}}if(u.window.activeNotebookEditor)return{type:H.Notebook,uri:u.window.activeNotebookEditor.notebook.uri,cellIndex:u.window.activeNotebookEditor.selection.start,range:void 0}}(o),c=s?.uri,l=e.repositories.find((e=>C(e))),f=(c?N(e,c):l)??l;if(!f)return;let d;if(n&&c&&await G(f,c),f.state.remotes.find((e=>{if(e.fetchUrl){const t=P(e.fetchUrl);if(t&&e.name===f.state.HEAD?.upstream?.remote)return void(d=t);t&&!d&&(d=t)}})),!d)return;const v=f.state.HEAD?`/blob/${"headlink"===i&&f.state.HEAD.name?z(f.state.HEAD.name):f.state.HEAD?.commit}`:"",h=`${r}/${d.owner}/${d.repo}${v}`;if(!c)return h;const p=z(c.path.substring(f.rootUri.path.length));return`${h}${s.type===H.File?t?`${p}${a?$(s.range):""}`:"":t?`${p}${a?M(s.cellIndex,s.range):""}`:""}`}function X(e,t,n="https://github.com"){const r=P(e);if(!r)throw new Error("Invalid repository URL provided");return t=z(t),`${n}/${r.owner}/${r.repo}/tree/${t}`}function q(e,t,n="https://github.com"){const r=P(e);if(!r)throw new Error("Invalid repository URL provided");return`${n}/${r.owner}/${r.repo}/commit/${t}`}function B(){return`https://${u.env.appName.toLowerCase().includes("insiders")?"insiders.":""}vscode.dev/github`}async function G(e,t){if(await e.status(),(0===e.state.HEAD?.type||2===e.state.HEAD?.type)&&!e?.state.HEAD?.upstream){const e=u.l10n.t("Publish Branch & Copy Link");if(await u.window.showInformationMessage(u.l10n.t("The current branch is not published to the remote. Would you like to publish your branch before copying a link?"),{modal:!0},e)!==e)throw new u.CancellationError;await u.commands.executeCommand("git.publish")}if(![...e.state.workingTreeChanges,...e.state.indexChanges].find((e=>e.uri.toString()===t.toString()))||e.state.HEAD?.ahead||e.state.HEAD?.behind){if(e.state.HEAD?.ahead){const t=u.l10n.t("Push Commits & Copy Link");if(await u.window.showInformationMessage(u.l10n.t("The current branch has unpublished commits. Would you like to push your commits before copying a link?"),{modal:!0},t)!==t)throw new u.CancellationError;await e.push()}else if(e.state.HEAD?.behind){const t=u.l10n.t("Pull Changes & Copy Link");if(await u.window.showInformationMessage(u.l10n.t("The current branch is not up to date. Would you like to pull before copying a link?"),{modal:!0},t)!==t)throw new u.CancellationError;await e.pull()}}else{const e=u.l10n.t("Commit Changes"),t=u.l10n.t("Copy Anyway");if(await u.window.showWarningMessage(u.l10n.t("The current file has uncommitted changes. Please commit your changes before copying a link."),{modal:!0},e,t)!==t)throw u.commands.executeCommand("workbench.view.scm"),new u.CancellationError}await e.status()}function K(e){const t=u.workspace.getConfiguration("github").get("gitProtocol");return{name:`$(github) ${e.full_name}`,description:e.stargazers_count>0?`$(star-full) ${e.stargazers_count}`:"",detail:e.description||void 0,url:"https"===t?e.clone_url:e.ssh_url}}(function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);o>3&&a&&Object.defineProperty(t,n,a)})([I],L.prototype,"getOctokitGraphql",null),function(e){e[e.File=1]="File",e[e.Notebook=2]="Notebook"}(H||(H={}));class V{constructor(){this.name="GitHub",this.icon="github",this.supportsQuery=!0,this.userReposCache=[]}async getRemoteSources(e){const t=await A();if(e){const n=P(e);if(n)return[K((await t.repos.get(n)).data)]}const n=await Promise.all([this.getQueryRemoteSources(t,e),this.getUserRemoteSources(t,e)]),r=new Map;for(const e of n)for(const t of e)r.set(t.name,t);return[...r.values()]}async getUserRemoteSources(e,t){if(!t){const t=(await e.users.getAuthenticated({})).data.login,n=await e.repos.listForAuthenticatedUser({username:t,sort:"updated",per_page:100});this.userReposCache=n.data.map(K)}return this.userReposCache}async getQueryRemoteSources(e,t){if(!t)return[];const n=function(e){const t=/^([^/]+)\/([^/]+)$/i.exec(e);return t?{owner:t[1],repo:t[2]}:void 0}(t);return n&&(t=`user:${n.owner}+${n.repo}`),t+=" fork:true",(await e.search.repos({q:t,sort:"stars"})).data.items.map(K)}async getBranches(e){const t=P(e);if(!t)return[];const n=await A(),r=[];let i=1;for(;;){const e=await n.repos.listBranches({...t,per_page:100,page:i});if(0===e.data.length)break;r.push(...e.data.map((e=>e.name))),i++}const o=(await n.repos.get(t)).data.default_branch;return r.sort(((e,t)=>e===o?-1:t===o?1:0))}async getRemoteSourceActions(e){return P(e)?[{label:u.l10n.t("Open on GitHub"),icon:"github",run(t){const n=X(e,t);u.env.openExternal(u.Uri.parse(n))}},{label:u.l10n.t("Checkout on vscode.dev"),icon:"globe",run(t){const n=X(e,t,B());u.env.openExternal(u.Uri.parse(n))}}]:[]}}var j=a(9023);const Z=e(import.meta.url)("path");function W(){return"codespaces"===u.env.remoteName}const Q=[{dir:".",files:["pull_request_template.md","PULL_REQUEST_TEMPLATE.md"]},{dir:"docs",files:["pull_request_template.md","PULL_REQUEST_TEMPLATE.md"]},{dir:".github",files:["PULL_REQUEST_TEMPLATE.md","PULL_REQUEST_TEMPLATE.md"]}],J=["PULL_REQUEST_TEMPLATE","docs/PULL_REQUEST_TEMPLATE",".github/PULL_REQUEST_TEMPLATE"];class Y{constructor(){this.items=new Map}set(e,t){this.items.set(e.path,t)}delete(e){this.items.delete(e.path)}provideTextDocumentContent(e){return this.items.get(e.path)}}class ee{constructor(e){this.telemetryReporter=e,this.disposables=[],this.commandErrors=new Y,this.disposables.push(u.workspace.registerTextDocumentContentProvider("github-output",this.commandErrors))}async handlePushError(e,t,n,r){if("PermissionDenied"!==r.gitErrorCode&&"PushRejected"!==r.gitErrorCode)return!1;const i=t.pushUrl||(W()?t.fetchUrl:void 0);if(!i)return!1;const o=/^(?:https:\/\/github\.com\/|git@github\.com:)([^\/]+)\/([^\/.]+)/i.exec(i);if(!o)return!1;if(/^:/.test(n))return!1;const[,a,s]=o;return"PermissionDenied"===r.gitErrorCode?(await this.handlePermissionDeniedError(e,t,n,a,s),this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"PermissionDenied"}),!0):/GH009: Secrets detected!/i.test(r.stderr)?(await this.handlePushProtectionError(a,s,r.stderr),this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"PushRejected.PushProtection"}),!0):(this.telemetryReporter.sendTelemetryEvent("pushErrorHandler",{handler:"None"}),!1)}async handlePermissionDeniedError(e,t,n,r,i){const o=u.l10n.t("Create Fork"),a=u.l10n.t("No"),s=u.l10n.t('You don\'t have permissions to push to "{0}/{1}" on GitHub. Would you like to create a fork and push to it instead?',r,i);if(await u.window.showWarningMessage(s,{modal:!0},o,a)!==o)return;const c=/^([^:]*):([^:]*)$/.exec(n),l=c?c[1]:n;let f=c?c[2]:n;const[d,v]=await u.window.withProgress({location:u.ProgressLocation.Notification,cancellable:!1,title:u.l10n.t("Create GitHub fork")},(async n=>{n.report({message:u.l10n.t('Forking "{0}/{1}"...',r,i),increment:33});const o=await A();let a;try{if(W()){const e=await u.commands.executeCommand("github.codespaces.forkRepository");if(!e)throw new Error("Unable to fork respository");if(a=e.repository,e.ref){let t=e.ref;t.startsWith("refs/heads/")&&(t=t.substr(11)),f=t}}else a=(await o.repos.createFork({owner:r,repo:i})).data}catch(e){throw console.error(e),e}n.report({message:u.l10n.t("Pushing changes..."),increment:33}),await e.renameRemote(t.name,"upstream");const s="https"===u.workspace.getConfiguration("github").get("gitProtocol")?a.clone_url:a.ssh_url;await e.addRemote("origin",s);try{await e.fetch("origin",f),await e.setBranchUpstream(l,`origin/${f}`)}catch{}return await e.push("origin",l,!0),[o,a]}));(async()=>{const t=u.l10n.t("Open on GitHub"),n=u.l10n.t("Create PR"),o=await u.window.showInformationMessage(u.l10n.t('The fork "{0}" was successfully created on GitHub.',v.full_name),t,n);if(o===t)await u.commands.executeCommand("vscode.open",u.Uri.parse(v.html_url));else if(o===n){const t=await u.window.withProgress({location:u.ProgressLocation.Notification,cancellable:!1,title:u.l10n.t("Creating GitHub Pull Request...")},(async t=>{let n=`Update ${f}`;const o=e.state.HEAD?.name;let a;if(o){const t=await e.getCommit(o);n=t.message.split("\n")[0],a=t.message.slice(n.length+1).trim()}const s=await async function(e){return(await Promise.allSettled([...Q.map((t=>async function(e,t){return(await u.workspace.fs.readDirectory(e)).filter((([e,n])=>Boolean(n&u.FileType.File)&&-1!==t.indexOf(e))).map((([t])=>u.Uri.joinPath(e,t)))}(u.Uri.joinPath(e,t.dir),t.files))),...J.map((t=>async function(e){return(await u.workspace.fs.readDirectory(e)).filter((([e,t])=>Boolean(t&u.FileType.File)&&".md"===Z.extname(e))).map((([t])=>u.Uri.joinPath(e,t)))}(u.Uri.joinPath(e,t))))])).flatMap((e=>"fulfilled"===e.status&&e.value||[]))}(e.rootUri);if(s.length>0){s.sort(((e,t)=>e.path.localeCompare(t.path)));const t=await async function(e,t){const n=[{label:u.l10n.t("No template"),picked:!0,template:void 0},...t.map((t=>({label:Z.relative(e.path,t.path),template:t})))],r={placeHolder:u.l10n.t("Select the Pull Request template"),ignoreFocusOut:!0},i=await u.window.showQuickPick(n,r);return i?.template}(e.rootUri,s);t&&(a=new j.TextDecoder("utf-8").decode(await u.workspace.fs.readFile(t)))}const{data:c}=await d.pulls.create({owner:r,repo:i,title:n,body:a,head:`${v.owner.login}:${f}`,base:v.default_branch});return await e.setConfig(`branch.${l}.remote`,"upstream"),await e.setConfig(`branch.${l}.merge`,`refs/heads/${f}`),await e.setConfig(`branch.${l}.github-pr-owner-number`,`${r}#${i}#${c.number}`),c})),n=u.l10n.t("Open PR");await u.window.showInformationMessage(u.l10n.t('The PR "{0}/{1}#{2}" was successfully created on GitHub.',r,i,t.number),n)===n&&await u.commands.executeCommand("vscode.open",u.Uri.parse(t.html_url))}})()}async handlePushProtectionError(e,t,n){const r=(new Date).getTime(),i=u.Uri.parse(`github-output:/github-error-${r}`);this.commandErrors.set(i,n);try{const e=await u.workspace.openTextDocument(i);await u.window.showTextDocument(e)}finally{this.commandErrors.set(i,n)}const o=u.l10n.t("Learn More"),a=u.l10n.t('Your push to "{0}/{1}" was rejected by GitHub because push protection is enabled and one or more secrets were detected.',e,t);await u.window.showWarningMessage(a,{modal:!0},o)===o&&u.commands.executeCommand("vscode.open","https://aka.ms/vscode-github-push-protection")}dispose(){this.disposables.forEach((e=>e.dispose()))}}function te(e){return Promise.race([new Promise((t=>e.onDidAccept((()=>e.selectedItems.length>0&&t(e.selectedItems[0]))))),new Promise((t=>e.onDidHide((()=>t(void 0)))))])}async function ne(e,t){if(!u.workspace.workspaceFolders?.length)return;let n;if(t)n=t.rootUri;else if(1===e.repositories.length)t=e.repositories[0],n=t.rootUri;else if(1===u.workspace.workspaceFolders.length)n=u.workspace.workspaceFolders[0].uri;else{const e=u.workspace.workspaceFolders.map((e=>({label:e.name,folder:e}))),t=u.l10n.t("Pick a folder to publish to GitHub"),r=await u.window.showQuickPick(e,{placeHolder:t});if(!r)return;n=r.folder.uri}let r,i,o,a,s=u.window.createQuickPick();s.ignoreFocusOut=!0,s.placeholder="Repository Name",s.value=(0,Z.basename)(n.fsPath),s.show(),s.busy=!0;try{i=await A();const e=await i.users.getAuthenticated({});r=e.data.login}catch(e){return void s.dispose()}s.busy=!1;const c=async()=>{const e=s.value.trim().replace(/[^a-z0-9_.]/gi,"-");s.items=e?[{label:"$(repo) Publish to GitHub private repository",description:`$(github) ${r}/${e}`,alwaysShow:!0,repo:e,isPrivate:!0},{label:"$(repo) Publish to GitHub public repository",description:`$(github) ${r}/${e}`,alwaysShow:!0,repo:e,isPrivate:!1}]:[]};for(c();;){const e=s.onDidChangeValue(c),t=await te(s);if(e.dispose(),o=t?.repo,a=t?.isPrivate??!0,o)try{s.busy=!0;const e=`${r}/${o}`;if((await i.repos.get({owner:r,repo:o})).data.full_name.toLowerCase()!==e.toLowerCase())break;s.items=[{label:"$(error) GitHub repository already exists",description:`$(github) ${e}`,alwaysShow:!0}]}catch{break}finally{s.busy=!1}}if(s.dispose(),!o)return;if(!t){const e=u.Uri.joinPath(n,".gitignore");let t=!1;try{await u.workspace.fs.stat(e)}catch(e){t=!0}if(t){s=u.window.createQuickPick(),s.placeholder=u.l10n.t("Select which files should be included in the repository."),s.canSelectMany=!0,s.show();try{s.busy=!0;const t=(await u.workspace.fs.readDirectory(n)).map((([e])=>e)).filter((e=>".git"!==e));s.items=t.map((e=>({label:e}))),s.selectedItems=s.items,s.busy=!1;const r=await Promise.race([new Promise((e=>s.onDidAccept((()=>e(s.selectedItems))))),new Promise((e=>s.onDidHide((()=>e(void 0)))))]);if(!r||0===r.length)return;const i=new Set(t);if(r.forEach((e=>i.delete(e.label))),i.size>0){const t=[...i].map((e=>`/${e}`)).join("\n"),n=new j.TextEncoder;await u.workspace.fs.writeFile(e,n.encode(t))}}finally{s.dispose()}}}const l=await u.window.withProgress({location:u.ProgressLocation.Notification,cancellable:!1,title:"Publish to GitHub"},(async r=>{let s;if(r.report({message:a?u.l10n.t("Publishing to a private GitHub repository"):u.l10n.t("Publishing to a public GitHub repository"),increment:25}),s=W()?await u.commands.executeCommand("github.codespaces.publish",{name:o,isPrivate:a}):(await i.repos.createForAuthenticatedUser({name:o,private:a})).data,s){if(r.report({message:u.l10n.t("Creating first commit"),increment:25}),!t){if(!(t=await e.init(n,{defaultBranch:s.default_branch})||void 0))return;await t.commit("first commit",{all:!0,postCommitCommand:null})}r.report({message:u.l10n.t("Uploading files"),increment:25});const i=await t.getBranch("HEAD"),o="https"===u.workspace.getConfiguration("github").get("gitProtocol")?s.clone_url:s.ssh_url;await t.addRemote("origin",o),await t.push("origin",i.name,!0)}return s}));if(!l)return;const f=u.l10n.t("Open on GitHub");u.window.showInformationMessage(u.l10n.t('Successfully published the "{0}" repository to GitHub.',`${r}/${o}`),f).then((e=>{e===f&&u.commands.executeCommand("vscode.open",u.Uri.parse(l.html_url))}))}async function re(e,t,n,r=!0){try{const i=await F(e,t,!0,B(),"headlink",n,r);if(i)return u.env.clipboard.writeText(i)}catch(e){e instanceof u.CancellationError||u.window.showErrorMessage(e.message)}}async function ie(e,t){const n=await e.getBranches({contains:t,remote:!0}),r=new Set(n.filter((e=>1===e.type&&e.remote)).map((e=>e.remote))),i=e.state.remotes.filter((e=>r.has(e.name)&&e.fetchUrl&&P(e.fetchUrl)));if(0===i.length)return void u.window.showInformationMessage(u.l10n.t("No GitHub remotes found that contain this commit."));const o=q((i.find((e=>"upstream"===e.name))??i.find((e=>"origin"===e.name))??i[0]).fetchUrl,t);u.env.openExternal(u.Uri.parse(o))}const oe={dispose(){}};class ae{async getCredentials(e){if(!/github\.com/i.test(e.authority))return;const t=await x();return{username:t.account.id,password:t.accessToken}}}class se{set enabled(e){this._enabled!==e&&(this._enabled=e,e?this.providerDisposable=this.gitAPI.registerCredentialsProvider(new ae):this.providerDisposable.dispose())}constructor(e){this.gitAPI=e,this.providerDisposable=oe,this._enabled=!1,this.disposable=u.workspace.onDidChangeConfiguration((e=>{e.affectsConfiguration("github")&&this.refresh()})),this.refresh()}refresh(){const e=u.workspace.getConfiguration("github",null).get("gitAuthentication",!0);this.enabled=!!e}dispose(){this.enabled=!1,this.disposable.dispose()}}class ue{constructor(e){this.gitAPI=e,this.name="GitHub",this.icon="github"}publishRepository(e){return ne(this.gitAPI,e)}}class ce{set enabled(e){if(this._enabled!==e){if(e)for(const e of this.gitAPI.repositories)this.providerDisposables.add(this.gitAPI.registerBranchProtectionProvider(e.rootUri,new le(e,this.globalState,this.octokitService,this.logger,this.telemetryReporter)));else this.providerDisposables.dispose();this._enabled=e}}constructor(e,t,n,r,i){this.gitAPI=e,this.globalState=t,this.octokitService=n,this.logger=r,this.telemetryReporter=i,this.disposables=new w,this.providerDisposables=new w,this._enabled=!1,this.disposables.add(this.gitAPI.onDidOpenRepository((t=>{this._enabled&&this.providerDisposables.add(e.registerBranchProtectionProvider(t.rootUri,new le(t,this.globalState,this.octokitService,this.logger,this.telemetryReporter)))}))),this.disposables.add(u.workspace.onDidChangeConfiguration((e=>{e.affectsConfiguration("github.branchProtection")&&this.updateEnablement()}))),this.updateEnablement()}updateEnablement(){const e=u.workspace.getConfiguration("github",null);this.enabled=!0===e.get("branchProtection",!0)}dispose(){this.enabled=!1,this.disposables.dispose()}}class le{constructor(e,t,n,r,i){this.repository=e,this.globalState=t,this.octokitService=n,this.logger=r,this.telemetryReporter=i,this._onDidChangeBranchProtection=new u.EventEmitter,this.onDidChangeBranchProtection=this._onDidChangeBranchProtection.event,this.globalStateKey=`branchProtection:${this.repository.rootUri.toString()}`,this.disposables=new w,this.disposables.add(this._onDidChangeBranchProtection),this.branchProtection=this.globalState.get(this.globalStateKey,[]),e.status().then((()=>{this.disposables.add(this.octokitService.onDidChangeSessions((()=>{this.updateRepositoryBranchProtection()}))),this.updateRepositoryBranchProtection()}))}provideBranchProtection(){return this.branchProtection}async getRepositoryDetails(e,t){const n=await this.octokitService.getOctokitGraphql(),{repository:r}=await n("\n\tquery repositoryPermissions($owner: String!, $repo: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tdefaultBranchRef {\n\t\t\t\tname\n\t\t\t},\n\t\t\tviewerPermission\n\t\t}\n\t}\n",{owner:e,repo:t});return r}async getRepositoryRulesets(e,t){const n=[];let r;const i=await this.octokitService.getOctokitGraphql();for(;;){const{repository:o}=await i("\n\tquery repositoryRulesets($owner: String!, $repo: String!, $cursor: String, $limit: Int = 100) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\trulesets(includeParents: true, first: $limit, after: $cursor) {\n\t\t\t\tnodes {\n\t\t\t\t\tname\n\t\t\t\t\tenforcement\n\t\t\t\t\trules(type: PULL_REQUEST) {\n\t\t\t\t\t\ttotalCount\n\t\t\t\t\t}\n\t\t\t\t\tconditions {\n\t\t\t\t\t\trefName {\n\t\t\t\t\t\t\tinclude\n\t\t\t\t\t\t\texclude\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\ttarget\n\t\t\t\t},\n\t\t\t\tpageInfo {\n\t\t\t\t\tendCursor,\n\t\t\t\t\thasNextPage\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",{owner:e,repo:t,cursor:r});if(n.push(...(o.rulesets?.nodes??[]).filter((e=>e&&"BRANCH"===e.target&&"ACTIVE"===e.enforcement&&(e.rules?.totalCount??0)>0))),!o.rulesets?.pageInfo.hasNextPage)break;r=o.rulesets.pageInfo.endCursor}return n}async updateRepositoryBranchProtection(){const e=[];try{for(const t of this.repository.state.remotes){const n=P(t.pushUrl??t.fetchUrl??"");if(!n)continue;this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Fetching repository details for "${n.owner}/${n.repo}".`);const r=await this.getRepositoryDetails(n.owner,n.repo);if("ADMIN"!==r.viewerPermission&&"MAINTAIN"!==r.viewerPermission&&"WRITE"!==r.viewerPermission){this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Skipping branch protection for "${n.owner}/${n.repo}" due to missing repository write permission.`);continue}const i=[],o=await this.getRepositoryRulesets(n.owner,n.repo);for(const e of o)i.push({include:(e.conditions.refName?.include??[]).map((e=>this.parseRulesetRefName(r,e))),exclude:(e.conditions.refName?.exclude??[]).map((e=>this.parseRulesetRefName(r,e)))});e.push({remote:t.name,rules:i})}this.branchProtection=e,this._onDidChangeBranchProtection.fire(this.repository.rootUri),await this.globalState.update(this.globalStateKey,e),this.logger.trace(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Branch protection for "${this.repository.rootUri.toString()}": ${JSON.stringify(e)}.`),this.telemetryReporter.sendTelemetryEvent("branchProtection",void 0,{rulesetCount:this.branchProtection.length})}catch(t){this.logger.warn(`[GitHubBranchProtectionProvider][updateRepositoryBranchProtection] Failed to update repository branch protection: ${t.message}`),t instanceof T&&0!==this.branchProtection.length&&(this.branchProtection=e,this._onDidChangeBranchProtection.fire(this.repository.rootUri),await this.globalState.update(this.globalStateKey,void 0))}}parseRulesetRefName(e,t){if(t.startsWith("refs/heads/"))return t.substring(11);switch(t){case"~ALL":return"**/*";case"~DEFAULT_BRANCH":return e.defaultBranchRef.name;default:return t}}dispose(){this.disposables.dispose()}}const fe=["ssh","https","file"];class de{constructor(e){this.gitApi=e,this.disposables=[],this.disposables.push(...fe.map((e=>u.workspace.registerCanonicalUriProvider(e,this))))}dispose(){this.disposables.forEach((e=>e.dispose()))}provideCanonicalUri(e,t,n){if("https"===t.targetScheme){if("file"===e.scheme){const t=this.gitApi.getRepository(e),n=t?.state.remotes.find((e=>e.name===t.state.HEAD?.remote))?.pushUrl?.replace(/^(git@[^\/:]+)(:)/i,"ssh://$1/");if(n)return ve(e)}return ve(e)}}}function ve(e){if("ssh"===e.scheme&&"**************"===e.authority){const[t,n]=(e.path.endsWith(".git")?e.path.slice(0,-4):e.path).split("/").filter((e=>e.length>0));return u.Uri.parse(`https://github.com/${t}/${n}`)}if("https"===e.scheme&&"github.com"===e.authority)return e}class he{set hasGitHubRepositories(e){u.commands.executeCommand("setContext","github.hasGitHubRepo",e),this._hasGitHubRepositories=e,this.ensureShareProviderRegistration()}constructor(e){this.gitAPI=e,this.id="copyVscodeDevLink",this.label=u.l10n.t("Copy vscode.dev Link"),this.priority=10,this._hasGitHubRepositories=!1,this.disposables=[],this.initializeGitHubRepoContext()}dispose(){this.disposables.forEach((e=>e.dispose()))}initializeGitHubRepoContext(){this.gitAPI.repositories.find((e=>C(e)))?(this.hasGitHubRepositories=!0,u.commands.executeCommand("setContext","github.hasGitHubRepo",!0)):this.disposables.push(this.gitAPI.onDidOpenRepository((async e=>{await e.status(),C(e)&&(u.commands.executeCommand("setContext","github.hasGitHubRepo",!0),this.hasGitHubRepositories=!0)}))),this.disposables.push(this.gitAPI.onDidCloseRepository((()=>{this.gitAPI.repositories.find((e=>C(e)))||(this.hasGitHubRepositories=!1)})))}ensureShareProviderRegistration(){if("codespaces"!==u.env.appHost&&!this.shareProviderRegistration&&this._hasGitHubRepositories){const e=u.window.registerShareProvider({scheme:"file"},this);this.shareProviderRegistration=e,this.disposables.push(e)}else this.shareProviderRegistration&&!this._hasGitHubRepositories&&(this.shareProviderRegistration.dispose(),this.shareProviderRegistration=void 0)}async provideShare(e,t){const n=N(this.gitAPI,e.resourceUri);if(!n)return;let r;if(await G(n,e.resourceUri),n.state.remotes.find((e=>{if(e.fetchUrl){const t=P(e.fetchUrl);if(t&&e.name===n.state.HEAD?.upstream?.remote)return void(r=t);t&&!r&&(r=t)}})),!r)return;const i=n?.state.HEAD?.name?z(n.state.HEAD?.name):n?.state.HEAD?.commit,o=z(e.resourceUri.path.substring(n?.rootUri.path.length)),a=function(e){if("vscode-notebook-cell"===e.resourceUri.scheme){const t=u.window.visibleNotebookEditors.find((t=>t.notebook.uri.fsPath===e.resourceUri.fsPath)),n=t?.notebook.getCells().find((t=>t.document.uri.fragment===e.resourceUri?.fragment));return M(n?.index??t?.selection.start,e.selection)}return $(e.selection)}(e);return u.Uri.parse(`${this.getVscodeDevHost()}/${r.owner}/${r.repo}/blob/${i}${o}${a}`)}getVscodeDevHost(){return`https://${u.env.appName.toLowerCase().includes("insiders")?"insiders.":""}vscode.dev/github`}}const pe=/(([A-Za-z0-9_.\-]+)\/([A-Za-z0-9_.\-]+))?(#|GH-)([1-9][0-9]*)($|\b)/g;function me(e,t){const n=(e.authorEmail??"").localeCompare(t.authorEmail??"");return 0!==n?n:(e.authorName??"").localeCompare(t.authorName??"")}class ge{constructor(e,t,n){this._gitAPI=e,this._octokitService=t,this._logger=n,this._isUserAuthenticated=!0,this._store=new Map,this._disposables=new w,this._disposables.add(this._gitAPI.onDidCloseRepository((e=>this._onDidCloseRepository(e)))),this._disposables.add(this._octokitService.onDidChangeSessions((()=>{this._isUserAuthenticated=!0,this._store.clear()}))),this._disposables.add(u.workspace.onDidChangeConfiguration((e=>{e.affectsConfiguration("github.showAvatar")&&this._store.clear()})))}async provideAvatar(e,t){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution for ${t.commits.length} commit(s) in ${e.rootUri.fsPath}.`);const n=!0===u.workspace.getConfiguration("github",e.rootUri).get("showAvatar",!0);if(!this._isUserAuthenticated||!n)return void this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution is disabled. (${!1===n?"setting":"auth"})`);const r=_(e);if(r)try{const n={cached:0,email:0,github:0,incomplete:0};await this._loadAssignableUsers(r);const i=this._store.get(this._getRepositoryKey(r));if(!i)return;const o=function(e,t){const n=[];let r;for(const i of e.slice(0).sort(t))r&&0===t(r[0],i)?r.push(i):(r=[i],n.push(r));return n}(t.commits,me),a=new Map;return await Promise.all(o.map((async e=>{if(0===e.length)return;const o=i.users.find((t=>t.email===e[0].authorEmail||t.name===e[0].authorName))?.avatarUrl;if(o)return n.cached+=e.length,void e.forEach((({hash:e})=>a.set(e,`${o}&s=${t.size}`)));if(e.some((({hash:e})=>i.commits.has(e))))return void e.forEach((({hash:e})=>a.set(e,void 0)));const s=function(e){const t=e?.match(/^([0-9]+)\+[^@]+@users\.noreply\.github\.com$/);return t?.[1]}(e[0].authorEmail);if(s){n.email+=e.length;const r=`https://avatars.githubusercontent.com/u/${s}?s=${t.size}`;return void e.forEach((({hash:e})=>a.set(e,r)))}const u=await this._getCommitAuthor(r,e[0].hash);if(u)i.users.push(u),n.github+=e.length,e.forEach((({hash:e})=>a.set(e,`${u.avatarUrl}&s=${t.size}`)));else{n.incomplete+=e.length;for(const{hash:t}of e)i.commits.add(t),a.set(t,void 0)}}))),this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Avatar resolution for ${t.commits.length} commit(s) in ${e.rootUri.fsPath} complete: ${JSON.stringify(n)}.`),a}catch(e){return void(e instanceof T&&(this._isUserAuthenticated=!1))}else this._logger.trace("[GitHubSourceControlHistoryItemDetailsProvider][provideAvatar] Repository does not have a GitHub remote.")}async provideHoverCommands(e){const t=E(e);if(t)return[{title:u.l10n.t("{0} Open on GitHub","$(github)"),tooltip:u.l10n.t("Open on GitHub"),command:"github.openOnGitHub",arguments:[t]}]}async provideMessageLinks(e,t){const n=_(e);if(n)return t.replace(pe,((e,t,r,i,o,a)=>!a||Number.isNaN(parseInt(a))?e:`[${r&&i?`${r}/${i}#${a}`:`#${a}`}](https://github.com/${r=r??n.owner}/${i=i??n.repo}/issues/${a})`))}_onDidCloseRepository(e){for(const t of e.state.remotes){if(!t.fetchUrl)continue;const e=P(t.fetchUrl);e&&this._store.delete(this._getRepositoryKey(e))}}async _loadAssignableUsers(e){if(!this._store.has(this._getRepositoryKey(e))){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Querying assignable user(s) for ${e.owner}/${e.repo}.`);try{const t=await this._octokitService.getOctokitGraphql(),{repository:n}=await t("\n\tquery assignableUsers($owner: String!, $repo: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tassignableUsers(first: 100) {\n\t\t\t\tnodes {\n\t\t\t\t\tid\n\t\t\t\t\tlogin\n\t\t\t\t\tname\n\t\t\t\t\temail\n\t\t\t\t\tavatarUrl\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",e),r=[];for(const e of n.assignableUsers.nodes??[])e&&r.push({id:e.id,login:e.login,name:e.name,email:e.email,avatarUrl:e.avatarUrl});this._store.set(this._getRepositoryKey(e),{users:r,commits:new Set}),this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Successfully queried assignable user(s) for ${e.owner}/${e.repo}: ${r.length} user(s).`)}catch(t){throw this._logger.warn(`[GitHubSourceControlHistoryItemDetailsProvider][_loadAssignableUsers] Failed to load assignable user(s) for ${e.owner}/${e.repo}: ${t}`),t}}}async _getCommitAuthor(e,t){this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Querying commit author for ${e.owner}/${e.repo}/${t}.`);try{const n=await this._octokitService.getOctokitGraphql(),{repository:r}=await n("\n\tquery commitAuthor($owner: String!, $repo: String!, $commit: String!) {\n\t\trepository(owner: $owner, name: $repo) {\n\t\t\tobject(expression: $commit) {\n\t\t\t\t... on Commit {\n\t\t\t\t\tauthor {\n\t\t\t\t\t\tname\n\t\t\t\t\t\temail\n\t\t\t\t\t\tavatarUrl\n\t\t\t\t\t\tuser {\n\t\t\t\t\t\t\tid\n\t\t\t\t\t\t\tlogin\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n",{...e,commit:t}),i=r.object.author;if(!(i?.user?.id&&i.user?.login&&i?.name&&i?.email&&i?.avatarUrl))return void this._logger.info(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Incomplete commit author for ${e.owner}/${e.repo}/${t}: ${JSON.stringify(r.object)}`);const o={id:i.user.id,login:i.user.login,name:i.name,email:i.email,avatarUrl:i.avatarUrl};return this._logger.trace(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Successfully queried commit author for ${e.owner}/${e.repo}/${t}: ${o.login}.`),o}catch(n){throw this._logger.warn(`[GitHubSourceControlHistoryItemDetailsProvider][_getCommitAuthor] Failed to get commit author for ${e.owner}/${e.repo}/${t}: ${n}`),n}}_getRepositoryKey(e){return`${e.owner}/${e.repo}`}dispose(){this._disposables.dispose()}}function ye(e){const t=[];e.subscriptions.push(new u.Disposable((()=>u.Disposable.from(...t).dispose())));const n=u.window.createOutputChannel("GitHub",{log:!0});t.push(n);const r=e=>{n.appendLine(u.l10n.t("Log level: {0}",u.LogLevel[e]))};t.push(n.onDidChangeLogLevel(r)),r(n.logLevel);const{aiKey:i}=e.extension.packageJSON,o=new g(i);t.push(o);const a=new L;t.push(a),t.push(function(){const e=new w,t=t=>{t?(()=>{try{const t=n.getAPI(1);e.add(t.registerRemoteSourceProvider(new V))}catch(e){console.error("Could not initialize GitHub extension"),console.warn(e)}})():e.dispose()},n=u.extensions.getExtension("vscode.git-base").exports;return e.add(n.onDidChangeEnablement(t)),t(n.enabled),e}()),t.push(function(e,t,n,r){const i=new w;let o=u.extensions.getExtension("vscode.git");const a=()=>{o.activate().then((o=>{const a=a=>{if(a){const a=o.getAPI(1);i.add(function(e){const t=new w;return t.add(u.commands.registerCommand("github.publish",(async()=>{try{ne(e)}catch(e){u.window.showErrorMessage(e.message)}}))),t.add(u.commands.registerCommand("github.copyVscodeDevLink",(async t=>re(e,!0,t)))),t.add(u.commands.registerCommand("github.copyVscodeDevLinkFile",(async t=>re(e,!1,t)))),t.add(u.commands.registerCommand("github.copyVscodeDevLinkWithoutRange",(async t=>re(e,!0,t,!1)))),t.add(u.commands.registerCommand("github.openOnGitHub",(async(e,t)=>{const n=q(e,t);u.env.openExternal(u.Uri.parse(n))}))),t.add(u.commands.registerCommand("github.graph.openOnGitHub",(async(t,n)=>{if(!t||!n)return;const r=e.repositories.find((e=>e.rootUri.fsPath===t.rootUri?.fsPath));r&&await ie(r,n.id)}))),t.add(u.commands.registerCommand("github.timeline.openOnGitHub",(async(t,n)=>{if(!t.id||!n)return;const r=e.getRepository(n);r&&await ie(r,t.id)}))),t.add(u.commands.registerCommand("github.openOnVscodeDev",(async()=>async function(e){try{const t=await F(e,!0,!1,B(),"headlink");return t?u.Uri.parse(t):void 0}catch(e){return void(e instanceof u.CancellationError||u.window.showErrorMessage(e.message))}}(e)))),t}(a)),i.add(new se(a)),i.add(new ce(a,e.globalState,t,r,n)),i.add(a.registerPushErrorHandler(new ee(n))),i.add(a.registerRemoteSourcePublisher(new ue(a))),i.add(a.registerSourceControlHistoryItemDetailsProvider(new ge(a,t,r))),i.add(new de(a)),i.add(new he(a)),function(e,t){if(e.repositories.find((e=>C(e))))u.commands.executeCommand("setContext","github.hasGitHubRepo",!0);else{const n=e.onDidOpenRepository((async e=>{await e.status(),C(e)&&(u.commands.executeCommand("setContext","github.hasGitHubRepo",!0),n.dispose())}));t.add(n)}}(a,i),u.commands.executeCommand("setContext","git-base.gitEnabled",!0)}else i.dispose()};i.add(o.onDidChangeEnablement(a)),a(o.enabled)}))};if(o)a();else{const e=u.extensions.onDidChange((()=>{!o&&u.extensions.getExtension("vscode.git")&&(o=u.extensions.getExtension("vscode.git"),a(),e.dispose())}));i.add(e)}return i}(e,a,o,n))}!function(e,t,n,r){var i,o=arguments.length,a=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,n,a):i(t,n))||a);o>3&&a&&Object.defineProperty(t,n,a)}([I],ge.prototype,"_loadAssignableUsers",null);var be=s.Y;export{be as activate};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/extensions/github/dist/extension.js.map