{"information_for_contributors": ["This file has been converted from https://github.com/jeff-hykin/better-shell-syntax/blob/master/autogenerated/shell.tmLanguage.json", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/jeff-hykin/better-shell-syntax/commit/35020b0bd79a90d3b262b4c13a8bb0b33adc1f45", "name": "<PERSON> Script", "scopeName": "source.shell", "patterns": [{"include": "#initial_context"}], "repository": {"alias_statement": {"begin": "(?:(?:[ \\t]*+)(alias)(?:[ \\t]*+)((?:(?:((?<!\\w)-\\w+\\b)(?:[ \\t]*+))*))(?:(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(\\=)|(\\+\\=))|(\\-\\=))))", "end": "(?:(?= |\\t|$)|(?:(?:(?:(;)|(&&))|(\\|\\|))|(&)))", "beginCaptures": {"1": {"name": "storage.type.alias.shell"}, "2": {"patterns": [{"match": "(?<!\\w)-\\w+\\b", "name": "string.unquoted.argument.shell constant.other.option.shell"}]}, "3": {"name": "string.unquoted.argument.shell constant.other.option.shell"}, "4": {"name": "variable.other.assignment.shell"}, "5": {"name": "punctuation.definition.array.access.shell"}, "6": {"name": "variable.other.assignment.shell"}, "7": {"name": "constant.numeric.shell constant.numeric.integer.shell"}, "8": {"name": "punctuation.definition.array.access.shell"}, "9": {"name": "keyword.operator.assignment.shell"}, "10": {"name": "keyword.operator.assignment.compound.shell"}, "11": {"name": "keyword.operator.assignment.compound.shell"}}, "endCaptures": {"1": {"name": "punctuation.terminator.statement.semicolon.shell"}, "2": {"name": "punctuation.separator.statement.and.shell"}, "3": {"name": "punctuation.separator.statement.or.shell"}, "4": {"name": "punctuation.separator.statement.background.shell"}}, "name": "meta.expression.assignment.alias.shell", "patterns": [{"include": "#normal_context"}]}, "argument": {"begin": "(?:[ \\t]++)(?!(?:&|\\||\\(|\\[|#|\\n|$|;))", "end": "(?= |\\t|;|\\||&|$|\\n|\\)|\\`)", "beginCaptures": {}, "endCaptures": {}, "name": "meta.argument.shell", "patterns": [{"include": "#argument_context"}, {"include": "#line_continuation"}]}, "argument_context": {"patterns": [{"match": "(?:[ \\t]*+)((?:[^ \t\n>&;<>\\(\\)\\$`\\\\\"'<\\|]+)(?!>))", "captures": {"1": {"name": "string.unquoted.argument.shell", "patterns": [{"match": "\\*", "name": "variable.language.special.wildcard.shell"}, {"include": "#variable"}, {"include": "#numeric_literal"}, {"match": "(?<!\\w)(\\b(?:true|false)\\b)(?!\\w)", "captures": {"1": {"name": "constant.language.$1.shell"}}}]}}}, {"include": "#normal_context"}]}, "arithmetic_double": {"patterns": [{"begin": "\\(\\(", "end": "\\)(?:\\s*)\\)", "beginCaptures": {"0": {"name": "punctuation.section.arithmetic.double.shell"}}, "endCaptures": {"0": {"name": "punctuation.section.arithmetic.double.shell"}}, "name": "meta.arithmetic.shell", "patterns": [{"include": "#math"}, {"include": "#string"}]}]}, "arithmetic_no_dollar": {"patterns": [{"begin": "\\(", "end": "\\)", "beginCaptures": {"0": {"name": "punctuation.section.arithmetic.single.shell"}}, "endCaptures": {"0": {"name": "punctuation.section.arithmetic.single.shell"}}, "name": "meta.arithmetic.shell", "patterns": [{"include": "#math"}, {"include": "#string"}]}]}, "array_access_inline": {"match": "(?:(\\[)([^\\[\\]]+)(\\]))", "captures": {"1": {"name": "punctuation.section.array.shell"}, "2": {"patterns": [{"include": "#special_expansion"}, {"include": "#string"}, {"include": "#variable"}]}, "3": {"name": "punctuation.section.array.shell"}}}, "array_value": {"begin": "(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(\\=)|(\\+\\=))|(\\-\\=))(?:[ \\t]*+)(\\()", "end": "\\)", "beginCaptures": {"1": {"name": "variable.other.assignment.shell"}, "2": {"name": "punctuation.definition.array.access.shell"}, "3": {"name": "variable.other.assignment.shell"}, "4": {"name": "constant.numeric.shell constant.numeric.integer.shell"}, "5": {"name": "punctuation.definition.array.access.shell"}, "6": {"name": "keyword.operator.assignment.shell"}, "7": {"name": "keyword.operator.assignment.compound.shell"}, "8": {"name": "keyword.operator.assignment.compound.shell"}, "9": {"name": "punctuation.definition.array.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.array.shell"}}, "patterns": [{"include": "#comment"}, {"match": "(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(\\=))", "captures": {"1": {"name": "variable.other.assignment.array.shell entity.other.attribute-name.shell"}, "2": {"name": "keyword.operator.assignment.shell punctuation.definition.assignment.shell"}}}, {"match": "(?:(\\[)(.+?)(\\])(\\=))", "captures": {"1": {"name": "punctuation.definition.bracket.named-array.shell"}, "2": {"name": "string.unquoted.shell entity.other.attribute-name.bracket.shell"}, "3": {"name": "punctuation.definition.bracket.named-array.shell"}, "4": {"name": "punctuation.definition.assignment.shell"}}}, {"include": "#normal_context"}, {"include": "#simple_unquoted"}]}, "assignment_statement": {"patterns": [{"include": "#array_value"}, {"include": "#modified_assignment_statement"}, {"include": "#normal_assignment_statement"}]}, "basic_command_name": {"match": "(?:(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$))(?:((?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$))|((?!\"|'|\\\\\\n?$)(?:[^!'\"<> \\t\\n\\r]+?)))(?:(?= |\\t)|(?:(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\))))", "captures": {"1": {"name": "storage.modifier.$1.shell"}, "2": {"name": "entity.name.function.call.shell entity.name.command.shell", "patterns": [{"match": "(?<!\\w)(?:continue|return|break)(?!\\w)", "name": "keyword.control.$0.shell"}, {"match": "(?<!\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\.)(?!\\/))(?!\\w)(?!-)", "name": "support.function.builtin.shell"}, {"include": "#variable"}]}}, "name": "meta.statement.command.name.basic.shell"}, "block_comment": {"begin": "(?:(?:\\s*+)(\\/\\*))", "end": "\\*\\/", "beginCaptures": {"1": {"name": "punctuation.definition.comment.begin.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.comment.end.shell"}}, "name": "comment.block.shell"}, "boolean": {"match": "\\b(?:true|false)\\b", "name": "constant.language.$0.shell"}, "case_statement": {"begin": "(?:(\\bcase\\b)(?:[ \\t]*+)(.+?)(?:[ \\t]*+)(\\bin\\b))", "end": "\\besac\\b", "beginCaptures": {"1": {"name": "keyword.control.case.shell"}, "2": {"patterns": [{"include": "#initial_context"}]}, "3": {"name": "keyword.control.in.shell"}}, "endCaptures": {"0": {"name": "keyword.control.esac.shell"}}, "name": "meta.case.shell", "patterns": [{"include": "#comment"}, {"match": "(?:[ \\t]*+)(\\* *\\))", "captures": {"1": {"name": "keyword.operator.pattern.case.default.shell"}}}, {"begin": "(?<!\\))(?!(?:[ \\t]*+)(?:esac\\b|$))", "end": "(?:(?=\\besac\\b)|(\\)))", "beginCaptures": {}, "endCaptures": {"1": {"name": "keyword.operator.pattern.case.shell"}}, "name": "meta.case.entry.pattern.shell", "patterns": [{"include": "#case_statement_context"}]}, {"begin": "(?<=\\))", "end": "(?:(;;)|(?=\\besac\\b))", "beginCaptures": {}, "endCaptures": {"1": {"name": "punctuation.terminator.statement.case.shell"}}, "name": "meta.case.entry.body.shell", "patterns": [{"include": "#typical_statements"}, {"include": "#initial_context"}]}]}, "case_statement_context": {"patterns": [{"match": "\\*", "name": "variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"}, {"match": "\\+", "name": "variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"}, {"match": "\\?", "name": "variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell"}, {"match": "@", "name": "variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell"}, {"match": "\\|", "name": "keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell"}, {"match": "\\\\.", "name": "constant.character.escape.shell"}, {"match": "(?<=\\tin| in| |\\t|;;)\\(", "name": "keyword.operator.pattern.case.shell"}, {"begin": "(?<=\\S)(\\()", "end": "\\)", "beginCaptures": {"1": {"name": "punctuation.definition.group.shell punctuation.definition.regex.group.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.group.shell punctuation.definition.regex.group.shell"}}, "name": "meta.parenthese.shell", "patterns": [{"include": "#case_statement_context"}]}, {"begin": "\\[", "end": "\\]", "beginCaptures": {"0": {"name": "punctuation.definition.character-class.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.character-class.shell"}}, "name": "string.regexp.character-class.shell", "patterns": [{"match": "\\\\.", "name": "constant.character.escape.shell"}]}, {"include": "#string"}, {"match": "[^) \\t\\n\\[\\?\\*\\|\\@]", "name": "string.unquoted.pattern.shell string.regexp.unquoted.shell"}]}, "command_name_range": {"begin": "\\G", "end": "(?:(?= |\\t|;|\\||&|$|\\n|\\)|\\`)|(?=<))", "beginCaptures": {}, "endCaptures": {}, "name": "meta.statement.command.name.shell", "patterns": [{"match": "(?<!\\w)(?:continue|return|break)(?!\\w)", "name": "entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell"}, {"match": "(?<!\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\.)(?!\\/))(?!\\w)(?!-)", "name": "entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell"}, {"include": "#variable"}, {"match": "(?:(?<!\\w)(?<=\\G|'|\"|\\}|\\))([^ \\n\\t\\r\"'=;&\\|`\\)\\{<>]+))", "captures": {"1": {"name": "entity.name.function.call.shell entity.name.command.shell"}}}, {"begin": "(?:(?:\\G|(?<! |\\t|;|\\||&|\\n|\\{|#))(?:(\\$?)((?:(\")|(')))))", "end": "(?<!\\G)(?<=(?:\\2))", "beginCaptures": {"1": {"name": "meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell"}, "2": {}, "3": {"name": "meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell"}, "4": {"name": "meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell"}}, "endCaptures": {}, "patterns": [{"include": "#continuation_of_single_quoted_command_name"}, {"include": "#continuation_of_double_quoted_command_name"}]}, {"include": "#line_continuation"}, {"include": "#simple_unquoted"}]}, "command_statement": {"begin": "(?:(?:[ \\t]*+)(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\n?$)))", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {}, "endCaptures": {}, "name": "meta.statement.command.shell", "patterns": [{"include": "#command_name_range"}, {"include": "#line_continuation"}, {"include": "#option"}, {"include": "#argument"}, {"include": "#string"}, {"include": "#heredoc"}]}, "comment": {"match": "(?:(?:^|(?:[ \\t]++))(?:((?:(#!)(?:.*)))|((?:(#)(?:.*)))))", "captures": {"1": {"name": "comment.line.number-sign.shell meta.shebang.shell"}, "2": {"name": "punctuation.definition.comment.shebang.shell"}, "3": {"name": "comment.line.number-sign.shell"}, "4": {"name": "punctuation.definition.comment.shell"}}}, "comments": {"patterns": [{"include": "#block_comment"}, {"include": "#line_comment"}]}, "compound-command": {"patterns": [{"begin": "\\[", "beginCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "end": "\\]", "endCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "name": "meta.scope.logical-expression.shell", "patterns": [{"include": "#logical-expression"}, {"include": "#initial_context"}]}, {"begin": "(?<=\\s|^){(?=\\s|$)", "beginCaptures": {"0": {"name": "punctuation.definition.group.shell"}}, "end": "(?<=^|;)\\s*(})", "endCaptures": {"1": {"name": "punctuation.definition.group.shell"}}, "name": "meta.scope.group.shell", "patterns": [{"include": "#initial_context"}]}]}, "continuation_of_double_quoted_command_name": {"begin": "(?:\\G(?<=\"))", "end": "\"", "beginCaptures": {}, "endCaptures": {"0": {"name": "string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell"}}, "contentName": "meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command", "patterns": [{"match": "\\\\[\\$\\n`\"\\\\]", "name": "constant.character.escape.shell"}, {"include": "#variable"}, {"include": "#interpolation"}]}, "continuation_of_single_quoted_command_name": {"begin": "(?:\\G(?<='))", "end": "'", "beginCaptures": {}, "endCaptures": {"0": {"name": "string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell"}}, "contentName": "meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command"}, "custom_command_names": {"patterns": []}, "custom_commands": {"patterns": []}, "double_quote_context": {"patterns": [{"match": "\\\\[\\$`\"\\\\\\n]", "name": "constant.character.escape.shell"}, {"include": "#variable"}, {"include": "#interpolation"}]}, "double_quote_escape_char": {"match": "\\\\[\\$`\"\\\\\\n]", "name": "constant.character.escape.shell"}, "floating_keyword": {"patterns": [{"match": "(?<=^|;|&| |\\t)(?:then|elif|else|done|end|do|if|fi)(?= |\\t|;|&|$)", "name": "keyword.control.$0.shell"}]}, "for_statement": {"patterns": [{"begin": "(?:(\\bfor\\b)(?:(?:[ \\t]*+)((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:[ \\t]*+)(\\bin\\b)))", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {"1": {"name": "keyword.control.for.shell"}, "2": {"name": "variable.other.for.shell"}, "3": {"name": "keyword.control.in.shell"}}, "endCaptures": {}, "name": "meta.for.in.shell", "patterns": [{"include": "#string"}, {"include": "#simple_unquoted"}, {"include": "#normal_context"}]}, {"begin": "(\\bfor\\b)", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {"1": {"name": "keyword.control.for.shell"}}, "endCaptures": {}, "name": "meta.for.shell", "patterns": [{"include": "#arithmetic_double"}, {"include": "#normal_context"}]}]}, "function_definition": {"begin": "(?:[ \\t]*+)(?:(?:(\\bfunction\\b)(?:[ \\t]*+)([^ \\t\\n\\r\\(\\)=\"']+)(?:(?:(\\()(?:[ \\t]*+)(\\)))?))|(?:([^ \\t\\n\\r\\(\\)=\"']+)(?:[ \\t]*+)(\\()(?:[ \\t]*+)(\\))))", "end": "(?<=\\}|\\))", "beginCaptures": {"1": {"name": "storage.type.function.shell"}, "2": {"name": "entity.name.function.shell"}, "3": {"name": "punctuation.definition.arguments.shell"}, "4": {"name": "punctuation.definition.arguments.shell"}, "5": {"name": "entity.name.function.shell"}, "6": {"name": "punctuation.definition.arguments.shell"}, "7": {"name": "punctuation.definition.arguments.shell"}}, "endCaptures": {}, "name": "meta.function.shell", "patterns": [{"match": "(?:\\G(?:\\t| |\\n))"}, {"begin": "\\{", "end": "\\}", "beginCaptures": {"0": {"name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"}}, "name": "meta.function.body.shell", "patterns": [{"include": "#initial_context"}]}, {"begin": "\\(", "end": "\\)", "beginCaptures": {"0": {"name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.group.shell punctuation.section.function.definition.shell"}}, "name": "meta.function.body.shell", "patterns": [{"include": "#initial_context"}]}, {"include": "#initial_context"}], "applyEndPatternLast": 1}, "heredoc": {"patterns": [{"begin": "(?:((?<!<)(?:<<-))(?:[ \\t]*+)(\"|')(?:[ \\t]*+)([^\"']+?)(?=\\s|;|&|<|\"|')((?:\\2))(.*))", "end": "(?:(?:^\\t*)(?:\\3)(?=\\s|;|&|$))", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "2": {"name": "punctuation.definition.string.heredoc.quote.shell"}, "3": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}, "4": {"name": "punctuation.definition.string.heredoc.quote.shell"}, "5": {"patterns": [{"include": "#redirect_fix"}, {"include": "#typical_statements"}]}}, "endCaptures": {"0": {"name": "punctuation.definition.string.heredoc.$0.shell"}}, "contentName": "string.quoted.heredoc.indent.$3", "patterns": []}, {"begin": "(?:((?<!<)(?:<<)(?!<))(?:[ \\t]*+)(\"|')(?:[ \\t]*+)([^\"']+?)(?=\\s|;|&|<|\"|')((?:\\2))(.*))", "end": "(?:^(?:\\3)(?=\\s|;|&|$))", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "2": {"name": "punctuation.definition.string.heredoc.quote.shell"}, "3": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}, "4": {"name": "punctuation.definition.string.heredoc.quote.shell"}, "5": {"patterns": [{"include": "#redirect_fix"}, {"include": "#typical_statements"}]}}, "endCaptures": {"0": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}}, "contentName": "string.quoted.heredoc.no-indent.$3", "patterns": []}, {"begin": "(?:((?<!<)(?:<<-))(?:[ \\t]*+)([^\"' \\t]+)(?=\\s|;|&|<|\"|')(.*))", "end": "(?:(?:^\\t*)(?:\\2)(?=\\s|;|&|$))", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "2": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}, "3": {"patterns": [{"include": "#redirect_fix"}, {"include": "#typical_statements"}]}}, "endCaptures": {"0": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}}, "contentName": "string.unquoted.heredoc.indent.$2", "patterns": [{"include": "#double_quote_escape_char"}, {"include": "#variable"}, {"include": "#interpolation"}]}, {"begin": "(?:((?<!<)(?:<<)(?!<))(?:[ \\t]*+)([^\"' \\t]+)(?=\\s|;|&|<|\"|')(.*))", "end": "(?:^(?:\\2)(?=\\s|;|&|$))", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "2": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}, "3": {"patterns": [{"include": "#redirect_fix"}, {"include": "#typical_statements"}]}}, "endCaptures": {"0": {"name": "punctuation.definition.string.heredoc.delimiter.shell"}}, "contentName": "string.unquoted.heredoc.no-indent.$2", "patterns": [{"include": "#double_quote_escape_char"}, {"include": "#variable"}, {"include": "#interpolation"}]}]}, "herestring": {"patterns": [{"begin": "(<<<)\\s*(('))", "beginCaptures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.quoted.single.shell"}, "3": {"name": "punctuation.definition.string.begin.shell"}}, "end": "(')", "endCaptures": {"0": {"name": "string.quoted.single.shell"}, "1": {"name": "punctuation.definition.string.end.shell"}}, "name": "meta.herestring.shell", "contentName": "string.quoted.single.shell"}, {"begin": "(<<<)\\s*((\"))", "beginCaptures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.quoted.double.shell"}, "3": {"name": "punctuation.definition.string.begin.shell"}}, "end": "(\")", "endCaptures": {"0": {"name": "string.quoted.double.shell"}, "1": {"name": "punctuation.definition.string.end.shell"}}, "name": "meta.herestring.shell", "contentName": "string.quoted.double.shell", "patterns": [{"include": "#double_quote_context"}]}, {"captures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.unquoted.herestring.shell", "patterns": [{"include": "#initial_context"}]}}, "match": "(<<<)\\s*(([^\\s)\\\\]|\\\\.)+)", "name": "meta.herestring.shell"}]}, "initial_context": {"patterns": [{"include": "#comment"}, {"include": "#pipeline"}, {"include": "#normal_statement_seperator"}, {"include": "#logical_expression_double"}, {"include": "#logical_expression_single"}, {"include": "#assignment_statement"}, {"include": "#case_statement"}, {"include": "#for_statement"}, {"include": "#loop"}, {"include": "#function_definition"}, {"include": "#line_continuation"}, {"include": "#arithmetic_double"}, {"include": "#misc_ranges"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#heredoc"}, {"include": "#herestring"}, {"include": "#redirection"}, {"include": "#pathname"}, {"include": "#floating_keyword"}, {"include": "#alias_statement"}, {"include": "#normal_statement"}, {"include": "#string"}, {"include": "#support"}]}, "inline_comment": {"match": "(\\/\\*)((?:(?:[^\\*]|(?:(?:\\*++)[^\\/]))*+)((?:(?:\\*++)\\/)))", "captures": {"1": {"name": "comment.block.shell punctuation.definition.comment.begin.shell"}, "2": {"name": "comment.block.shell"}, "3": {"patterns": [{"match": "\\*\\/", "name": "comment.block.shell punctuation.definition.comment.end.shell"}, {"match": "\\*", "name": "comment.block.shell"}]}}}, "interpolation": {"patterns": [{"include": "#arithmetic_dollar"}, {"include": "#subshell_dollar"}, {"begin": "`", "beginCaptures": {"0": {"name": "punctuation.definition.evaluation.backticks.shell"}}, "end": "`", "endCaptures": {"0": {"name": "punctuation.definition.evaluation.backticks.shell"}}, "name": "string.interpolated.backtick.shell", "patterns": [{"match": "\\\\[`\\\\$]", "name": "constant.character.escape.shell"}, {"begin": "(?<=\\W)(?=#)(?!#{)", "beginCaptures": {"1": {"name": "punctuation.whitespace.comment.leading.shell"}}, "end": "(?!\\G)", "patterns": [{"begin": "#", "beginCaptures": {"0": {"name": "punctuation.definition.comment.shell"}}, "end": "(?=`)", "name": "comment.line.number-sign.shell"}]}, {"include": "#initial_context"}]}]}, "keyword": {"patterns": [{"match": "(?<=^|;|&|\\s)(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=\\s|;|&|$)", "name": "keyword.control.shell"}, {"match": "(?<=^|;|&|\\s)(?:export|declare|typeset|local|readonly)(?=\\s|;|&|$)", "name": "storage.modifier.shell"}]}, "line_comment": {"begin": "(?:\\s*+)(\\/\\/)", "end": "(?<=\\n)(?<!\\\\\\n)", "beginCaptures": {"1": {"name": "punctuation.definition.comment.shell"}}, "endCaptures": {}, "name": "comment.line.double-slash.shell", "patterns": [{"include": "#line_continuation_character"}]}, "line_continuation": {"match": "\\\\(?=\\n)", "name": "constant.character.escape.line-continuation.shell"}, "logical-expression": {"patterns": [{"include": "#arithmetic_no_dollar"}, {"comment": "do we want a special rule for ( expr )?", "match": "=[=~]?|!=?|<|>|&&|\\|\\|", "name": "keyword.operator.logical.shell"}, {"match": "(?<!\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])\\b", "name": "keyword.operator.logical.shell"}]}, "logical_expression_context": {"patterns": [{"include": "#regex_comparison"}, {"include": "#arithmetic_no_dollar"}, {"include": "#logical-expression"}, {"include": "#logical_expression_single"}, {"include": "#logical_expression_double"}, {"include": "#comment"}, {"include": "#boolean"}, {"include": "#redirect_number"}, {"include": "#numeric_literal"}, {"include": "#pipeline"}, {"include": "#normal_statement_seperator"}, {"include": "#string"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#heredoc"}, {"include": "#herestring"}, {"include": "#pathname"}, {"include": "#floating_keyword"}, {"include": "#support"}]}, "logical_expression_double": {"begin": "\\[\\[", "end": "\\]\\]", "beginCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "name": "meta.scope.logical-expression.shell", "patterns": [{"include": "#logical_expression_context"}]}, "logical_expression_single": {"begin": "\\[", "end": "\\]", "beginCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.logical-expression.shell"}}, "name": "meta.scope.logical-expression.shell", "patterns": [{"include": "#logical_expression_context"}]}, "loop": {"patterns": [{"begin": "(?<=^|;|&|\\s)(for)\\s+(.+?)\\s+(in)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}, "2": {"name": "variable.other.loop.shell", "patterns": [{"include": "#string"}]}, "3": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)done(?=\\s|;|&|$|\\))", "endCaptures": {"0": {"name": "keyword.control.shell"}}, "name": "meta.scope.for-in-loop.shell", "patterns": [{"include": "#initial_context"}]}, {"begin": "(?<=^|;|&|\\s)(while|until)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)done(?=\\s|;|&|$|\\))", "endCaptures": {"0": {"name": "keyword.control.shell"}}, "name": "meta.scope.while-loop.shell", "patterns": [{"include": "#initial_context"}]}, {"begin": "(?<=^|;|&|\\s)(select)\\s+((?:[^\\s\\\\]|\\\\.)+)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}, "2": {"name": "variable.other.loop.shell"}}, "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$|\\))", "endCaptures": {"1": {"name": "keyword.control.shell"}}, "name": "meta.scope.select-block.shell", "patterns": [{"include": "#initial_context"}]}, {"begin": "(?<=^|;|&|\\s)if(?=\\s|;|&|$)", "beginCaptures": {"0": {"name": "keyword.control.if.shell"}}, "end": "(?<=^|;|&|\\s)fi(?=\\s|;|&|$)", "endCaptures": {"0": {"name": "keyword.control.fi.shell"}}, "name": "meta.scope.if-block.shell", "patterns": [{"include": "#initial_context"}]}]}, "math": {"patterns": [{"include": "#variable"}, {"match": "\\+{1,2}|-{1,2}|!|~|\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\|{1,2}|&{1,2}|\\?|\\:|,|=|[*/%+\\-&^|]=|<<=|>>=", "name": "keyword.operator.arithmetic.shell"}, {"match": "0[xX][0-9A-Fa-f]+", "name": "constant.numeric.hex.shell"}, {"match": ";", "name": "punctuation.separator.semicolon.range"}, {"match": "0\\d+", "name": "constant.numeric.octal.shell"}, {"match": "\\d{1,2}#[0-9a-zA-Z@_]+", "name": "constant.numeric.other.shell"}, {"match": "\\d+", "name": "constant.numeric.integer.shell"}, {"match": "(?<!\\w)(?:[a-zA-Z_0-9]+)(?!\\w)", "name": "variable.other.normal.shell"}]}, "math_operators": {"patterns": [{"match": "\\+{1,2}|-{1,2}|!|~|\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\|{1,2}|&{1,2}|\\?|\\:|,|=|[*/%+\\-&^|]=|<<=|>>=", "name": "keyword.operator.arithmetic.shell"}, {"match": "0[xX][0-9A-Fa-f]+", "name": "constant.numeric.hex.shell"}, {"match": "0\\d+", "name": "constant.numeric.octal.shell"}, {"match": "\\d{1,2}#[0-9a-zA-Z@_]+", "name": "constant.numeric.other.shell"}, {"match": "\\d+", "name": "constant.numeric.integer.shell"}]}, "misc_ranges": {"patterns": [{"include": "#logical_expression_single"}, {"include": "#logical_expression_double"}, {"include": "#subshell_dollar"}, {"begin": "(?<![^ \\t])({)(?!\\w|\\$)", "end": "}", "beginCaptures": {"1": {"name": "punctuation.definition.group.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.group.shell"}}, "name": "meta.scope.group.shell", "patterns": [{"include": "#initial_context"}]}]}, "modified_assignment_statement": {"begin": "(?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$)", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {"0": {"name": "storage.modifier.$0.shell"}}, "endCaptures": {}, "name": "meta.statement.shell meta.expression.assignment.modified.shell", "patterns": [{"match": "(?<!\\w)-\\w+\\b", "name": "string.unquoted.argument.shell constant.other.option.shell"}, {"include": "#array_value"}, {"match": "(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?)(?:(?:(?:(\\=)|(\\+\\=))|(\\-\\=))?)(?:(?:(?<==| |\\t|^|\\{|\\(|\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\d+))|(\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\d+(?:\\.\\d+)))|(-?\\d+(?:\\.\\d+)+))|(-?\\d+))(?= |\\t|$|\\}|\\)|;))?))", "captures": {"1": {"name": "variable.other.assignment.shell"}, "2": {"name": "punctuation.definition.array.access.shell"}, "3": {"name": "variable.other.assignment.shell"}, "4": {"name": "constant.numeric.shell constant.numeric.integer.shell"}, "5": {"name": "punctuation.definition.array.access.shell"}, "6": {"name": "keyword.operator.assignment.shell"}, "7": {"name": "keyword.operator.assignment.compound.shell"}, "8": {"name": "keyword.operator.assignment.compound.shell"}, "9": {"name": "constant.numeric.shell constant.numeric.hex.shell"}, "10": {"name": "constant.numeric.shell constant.numeric.octal.shell"}, "11": {"name": "constant.numeric.shell constant.numeric.other.shell"}, "12": {"name": "constant.numeric.shell constant.numeric.decimal.shell"}, "13": {"name": "constant.numeric.shell constant.numeric.version.shell"}, "14": {"name": "constant.numeric.shell constant.numeric.integer.shell"}}}, {"include": "#normal_context"}]}, "modifiers": {"match": "(?<=^|;|&|[ \\t])(?:readonly|declare|typeset|export|local)(?=[ \\t]|;|&|$)", "name": "storage.modifier.$0.shell"}, "normal_assignment_statement": {"begin": "(?:[ \\t]*+)(?:((?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))(?:(?:(\\[)((?:(?:(?:(?:\\$?)(?:(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w))|@)|\\*)|(-?\\d+)))(\\]))?))(?:(?:(\\=)|(\\+\\=))|(\\-\\=))", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {"1": {"name": "variable.other.assignment.shell"}, "2": {"name": "punctuation.definition.array.access.shell"}, "3": {"name": "variable.other.assignment.shell"}, "4": {"name": "constant.numeric.shell constant.numeric.integer.shell"}, "5": {"name": "punctuation.definition.array.access.shell"}, "6": {"name": "keyword.operator.assignment.shell"}, "7": {"name": "keyword.operator.assignment.compound.shell"}, "8": {"name": "keyword.operator.assignment.compound.shell"}}, "endCaptures": {}, "name": "meta.expression.assignment.shell", "patterns": [{"include": "#comment"}, {"include": "#string"}, {"include": "#normal_assignment_statement"}, {"begin": "(?<= |\\t)(?! |\\t|\\w+=)", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {}, "endCaptures": {}, "name": "meta.statement.command.env.shell", "patterns": [{"include": "#command_name_range"}, {"include": "#line_continuation"}, {"include": "#option"}, {"include": "#argument"}, {"include": "#string"}]}, {"include": "#simple_unquoted"}, {"include": "#normal_context"}]}, "normal_context": {"patterns": [{"include": "#comment"}, {"include": "#pipeline"}, {"include": "#normal_statement_seperator"}, {"include": "#misc_ranges"}, {"include": "#boolean"}, {"include": "#redirect_number"}, {"include": "#numeric_literal"}, {"include": "#string"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#heredoc"}, {"include": "#herestring"}, {"include": "#redirection"}, {"include": "#pathname"}, {"include": "#floating_keyword"}, {"include": "#support"}, {"include": "#parenthese"}]}, "normal_statement": {"begin": "(?:(?!^[ \\t]*+$)(?:(?<=^until | until |\\tuntil |^while | while |\\twhile |^elif | elif |\\telif |^else | else |\\telse |^then | then |\\tthen |^do | do |\\tdo |^if | if |\\tif )|(?<=(?:^|;|\\||&|!|\\(|\\{|\\`)))(?:[ \\t]*+)(?!nocorrect\\W|nocorrect\\$|function\\W|function\\$|foreach\\W|foreach\\$|repeat\\W|repeat\\$|logout\\W|logout\\$|coproc\\W|coproc\\$|select\\W|select\\$|while\\W|while\\$|pushd\\W|pushd\\$|until\\W|until\\$|case\\W|case\\$|done\\W|done\\$|elif\\W|elif\\$|else\\W|else\\$|esac\\W|esac\\$|popd\\W|popd\\$|then\\W|then\\$|time\\W|time\\$|for\\W|for\\$|end\\W|end\\$|fi\\W|fi\\$|do\\W|do\\$|in\\W|in\\$|if\\W|if\\$))", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {}, "endCaptures": {}, "name": "meta.statement.shell", "patterns": [{"include": "#typical_statements"}]}, "normal_statement_seperator": {"match": "(?:(?:(?:(;)|(&&))|(\\|\\|))|(&))", "captures": {"1": {"name": "punctuation.terminator.statement.semicolon.shell"}, "2": {"name": "punctuation.separator.statement.and.shell"}, "3": {"name": "punctuation.separator.statement.or.shell"}, "4": {"name": "punctuation.separator.statement.background.shell"}}}, "numeric_literal": {"match": "(?<==| |\\t|^|\\{|\\(|\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\d+))|(\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\d+(?:\\.\\d+)))|(-?\\d+(?:\\.\\d+)+))|(-?\\d+))(?= |\\t|$|\\}|\\)|;)", "captures": {"1": {"name": "constant.numeric.shell constant.numeric.hex.shell"}, "2": {"name": "constant.numeric.shell constant.numeric.octal.shell"}, "3": {"name": "constant.numeric.shell constant.numeric.other.shell"}, "4": {"name": "constant.numeric.shell constant.numeric.decimal.shell"}, "5": {"name": "constant.numeric.shell constant.numeric.version.shell"}, "6": {"name": "constant.numeric.shell constant.numeric.integer.shell"}}}, "option": {"begin": "(?:(?:[ \\t]++)(-)((?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))))", "end": "(?:(?=[ \\t])|(?:(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)))", "beginCaptures": {"1": {"name": "string.unquoted.argument.shell constant.other.option.dash.shell"}, "2": {"name": "string.unquoted.argument.shell constant.other.option.shell"}}, "endCaptures": {}, "contentName": "string.unquoted.argument constant.other.option", "patterns": [{"include": "#option_context"}]}, "option_context": {"patterns": [{"include": "#misc_ranges"}, {"include": "#string"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#heredoc"}, {"include": "#herestring"}, {"include": "#redirection"}, {"include": "#pathname"}, {"include": "#floating_keyword"}, {"include": "#support"}]}, "parenthese": {"patterns": [{"begin": "\\(", "end": "\\)", "beginCaptures": {"0": {"name": "punctuation.section.parenthese.shell"}}, "endCaptures": {"0": {"name": "punctuation.section.parenthese.shell"}}, "name": "meta.parenthese.group.shell", "patterns": [{"include": "#initial_context"}]}]}, "pathname": {"patterns": [{"match": "(?<=\\s|:|=|^)~", "name": "keyword.operator.tilde.shell"}, {"match": "\\*|\\?", "name": "keyword.operator.glob.shell"}, {"begin": "([?*+@!])(\\()", "beginCaptures": {"1": {"name": "keyword.operator.extglob.shell"}, "2": {"name": "punctuation.definition.extglob.shell"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.extglob.shell"}}, "name": "meta.structure.extglob.shell", "patterns": [{"include": "#initial_context"}]}]}, "pipeline": {"patterns": [{"match": "(?<=^|;|&|\\s)(time)(?=\\s|;|&|$)", "name": "keyword.other.shell"}, {"match": "[|!]", "name": "keyword.operator.pipe.shell"}]}, "redirect_fix": {"match": "(?:(>>?)(?:[ \\t]*+)([^ \t\n>&;<>\\(\\)\\$`\\\\\"'<\\|]+))", "captures": {"1": {"name": "keyword.operator.redirect.shell"}, "2": {"name": "string.unquoted.argument.shell"}}}, "redirect_number": {"match": "(?<=[ \\t])(?:(?:(1)|(2)|(\\d+))(?=>))", "captures": {"1": {"name": "keyword.operator.redirect.stdout.shell"}, "2": {"name": "keyword.operator.redirect.stderr.shell"}, "3": {"name": "keyword.operator.redirect.$3.shell"}}}, "redirection": {"patterns": [{"begin": "[><]\\(", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.interpolated.process-substitution.shell", "patterns": [{"include": "#initial_context"}]}, {"match": "(?<![<>])(&>|\\d*>&\\d*|\\d*(>>|>|<)|\\d*<&|\\d*<>)(?![<>])", "name": "keyword.operator.redirect.shell"}]}, "regex_comparison": {"match": "\\=~", "name": "keyword.operator.logical.regex.shell"}, "regexp": {"patterns": [{"match": "(?:.+)"}]}, "simple_options": {"match": "(?:(?:[ \\t]++)\\-(?:\\w+))*", "captures": {"0": {"patterns": [{"match": "(?:[ \\t]++)(\\-)(\\w+)", "captures": {"1": {"name": "string.unquoted.argument.shell constant.other.option.dash.shell"}, "2": {"name": "string.unquoted.argument.shell constant.other.option.shell"}}}]}}}, "simple_unquoted": {"match": "[^ \\t\\n>&;<>\\(\\)\\$`\\\\\"'<\\|]", "name": "string.unquoted.shell"}, "special_expansion": {"match": "!|:[-=?]?|\\*|@|##|#|%%|%|\\/", "name": "keyword.operator.expansion.shell"}, "start_of_command": {"match": "(?:(?:[ \\t]*+)(?:(?!(?:!|&|\\||\\(|\\)|\\{|\\[|<|>|#|\\n|$|;|[ \\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\n?$)))"}, "string": {"patterns": [{"match": "\\\\.", "name": "constant.character.escape.shell"}, {"begin": "'", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.single.shell"}, {"begin": "\\$?\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.double.shell", "patterns": [{"match": "\\\\[\\$\\n`\"\\\\]", "name": "constant.character.escape.shell"}, {"include": "#variable"}, {"include": "#interpolation"}]}, {"begin": "\\$'", "end": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.single.dollar.shell", "patterns": [{"match": "\\\\(?:a|b|e|f|n|r|t|v|\\\\|')", "name": "constant.character.escape.ansi-c.shell"}, {"match": "\\\\[0-9]{3}\"", "name": "constant.character.escape.octal.shell"}, {"match": "\\\\x[0-9a-fA-F]{2}\"", "name": "constant.character.escape.hex.shell"}, {"match": "\\\\c.\"", "name": "constant.character.escape.control-char.shell"}]}]}, "subshell_dollar": {"patterns": [{"begin": "(?:\\$\\()", "end": "\\)", "beginCaptures": {"0": {"name": "punctuation.definition.subshell.single.shell"}}, "endCaptures": {"0": {"name": "punctuation.definition.subshell.single.shell"}}, "name": "meta.scope.subshell", "patterns": [{"include": "#parenthese"}, {"include": "#initial_context"}]}]}, "support": {"patterns": [{"match": "(?<=^|;|&|\\s)(?::|\\.)(?=\\s|;|&|$)", "name": "support.function.builtin.shell"}]}, "typical_statements": {"patterns": [{"include": "#assignment_statement"}, {"include": "#case_statement"}, {"include": "#for_statement"}, {"include": "#while_statement"}, {"include": "#function_definition"}, {"include": "#command_statement"}, {"include": "#line_continuation"}, {"include": "#arithmetic_double"}, {"include": "#normal_context"}]}, "variable": {"patterns": [{"match": "(?:(\\$)(\\@(?!\\w)))", "captures": {"1": {"name": "punctuation.definition.variable.shell variable.parameter.positional.all.shell"}, "2": {"name": "variable.parameter.positional.all.shell"}}}, {"match": "(?:(\\$)([0-9](?!\\w)))", "captures": {"1": {"name": "punctuation.definition.variable.shell variable.parameter.positional.shell"}, "2": {"name": "variable.parameter.positional.shell"}}}, {"match": "(?:(\\$)([-*#?$!0_](?!\\w)))", "captures": {"1": {"name": "punctuation.definition.variable.shell variable.language.special.shell"}, "2": {"name": "variable.language.special.shell"}}}, {"begin": "(?:(\\$)(\\{)(?:[ \\t]*+)(?=\\d))", "end": "\\}", "beginCaptures": {"1": {"name": "punctuation.definition.variable.shell variable.parameter.positional.shell"}, "2": {"name": "punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell"}}, "endCaptures": {"0": {"name": "punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell"}}, "contentName": "meta.parameter-expansion", "patterns": [{"include": "#special_expansion"}, {"include": "#array_access_inline"}, {"match": "[0-9]+", "name": "variable.parameter.positional.shell"}, {"match": "(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w)", "name": "variable.other.normal.shell"}, {"include": "#variable"}, {"include": "#string"}]}, {"begin": "(?:(\\$)(\\{))", "end": "\\}", "beginCaptures": {"1": {"name": "punctuation.definition.variable.shell"}, "2": {"name": "punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell"}}, "endCaptures": {"0": {"name": "punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell"}}, "contentName": "meta.parameter-expansion", "patterns": [{"include": "#special_expansion"}, {"include": "#array_access_inline"}, {"match": "(?<!\\w)(?:[a-zA-Z_0-9-]+)(?!\\w)", "name": "variable.other.normal.shell"}, {"include": "#variable"}, {"include": "#string"}]}, {"match": "(?:(\\$)((?:\\w+)(?!\\w)))", "captures": {"1": {"name": "punctuation.definition.variable.shell variable.other.normal.shell"}, "2": {"name": "variable.other.normal.shell"}}}]}, "while_statement": {"patterns": [{"begin": "(\\bwhile\\b)", "end": "(?=;|\\||&|\\n|\\)|\\`|\\{|\\}|[ \\t]*#|\\])(?<!\\\\)", "beginCaptures": {"1": {"name": "keyword.control.while.shell"}}, "endCaptures": {}, "name": "meta.while.shell", "patterns": [{"include": "#line_continuation"}, {"include": "#math_operators"}, {"include": "#option"}, {"include": "#simple_unquoted"}, {"include": "#normal_context"}, {"include": "#string"}]}]}}}