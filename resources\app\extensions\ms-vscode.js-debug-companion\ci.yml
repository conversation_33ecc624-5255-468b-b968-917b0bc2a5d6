trigger:
  branches:
    include:
      - main
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      endpoint: Monaco

parameters:
  - name: publishExtension
    displayName: 🚀 Publish Extension
    type: boolean
    default: false

extends:
  template: azure-pipelines/extension/stable.yml@templates
  parameters:
    publishExtension: ${{ parameters.publishExtension }}
    ghCreateRelease: true
    ghReleaseAddChangeLog: true
    buildSteps:
      - script: npm install
        displayName: Install dependencies

      - script: npm run vscode:prepublish
        displayName: Compile
    tsa:
      config:
        areaPath: 'Visual Studio Code Debugging Extensions'
        serviceTreeID: '053e3ba6-924d-456c-ace0-67812c5ccc52'
      enabled: true
