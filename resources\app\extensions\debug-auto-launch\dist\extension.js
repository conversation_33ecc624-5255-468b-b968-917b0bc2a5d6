(()=>{"use strict";var t={256:function(t,e,a){var n,o=this&&this.__createBinding||(Object.create?function(t,e,a,n){void 0===n&&(n=a);var o=Object.getOwnPropertyDescriptor(e,a);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[a]}}),Object.defineProperty(t,n,o)}:function(t,e,a,n){void 0===n&&(n=a),t[n]=e[a]}),s=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[e.length]=a);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var a=n(t),i=0;i<a.length;i++)"default"!==a[i]&&o(e,t,a[i]);return s(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.activate=function(t){P=Promise.resolve({context:t,state:null}),t.subscriptions.push(u.commands.registerCommand(y,k.bind(null,t))),t.subscriptions.push(u.workspace.onDidChangeConfiguration((t=>{(t.affectsConfiguration(`${A}.${x}`)||[...j].some((e=>t.affectsConfiguration(e))))&&T()}))),I(_())},e.deactivate=async function(){await B()};const r=a(896),c=a(278),l=a(928),u=i(a(398)),d={disabled:u.l10n.t("Auto Attach: Disabled"),always:u.l10n.t("Auto Attach: Always"),smart:u.l10n.t("Auto Attach: Smart"),onlyWithFlag:u.l10n.t("Auto Attach: With Flag")},g={disabled:u.l10n.t("Disabled"),always:u.l10n.t("Always"),smart:u.l10n.t("Smart"),onlyWithFlag:u.l10n.t("Only With Flag")},f={disabled:u.l10n.t("Auto attach is disabled and not shown in status bar"),always:u.l10n.t("Auto attach to every Node.js process launched in the terminal"),smart:u.l10n.t("Auto attach when running scripts that aren't in a node_modules folder"),onlyWithFlag:u.l10n.t("Only auto attach when the `--inspect` flag is given")},h=u.l10n.t("Toggle auto attach in this workspace"),p=u.l10n.t("Toggle auto attach on this machine"),w=u.l10n.t("Temporarily disable auto attach in this session"),b=u.l10n.t("Re-enable auto attach"),m=u.l10n.t("Auto Attach: Disabled"),y="extension.node-debug.toggleAutoAttach",v="jsDebugIpcState",A="debug.javascript",x="autoAttachFilter",j=new Set(["autoAttachSmartPattern",x].map((t=>`${A}.${t}`)));let P,C,D,S=!1;function T(){I("disabled"),I(_())}async function k(t,e){const a=u.workspace.getConfiguration(A);var n;const o=(e=e||((n=a.inspect(x))?n.workspaceFolderValue?u.ConfigurationTarget.WorkspaceFolder:n.workspaceValue?u.ConfigurationTarget.Workspace:(n.globalValue,u.ConfigurationTarget.Global):u.ConfigurationTarget.Global))===u.ConfigurationTarget.Global,s=u.window.createQuickPick(),i=_(),r=["always","smart","onlyWithFlag","disabled"].map((t=>({state:t,label:g[t],description:f[t],alwaysShow:!0})));"disabled"!==i&&r.unshift({setTempDisabled:!S,label:S?b:w,alwaysShow:!0}),s.items=r,s.activeItems=S?[r[0]]:s.items.filter((t=>"state"in t&&t.state===i)),s.title=o?p:h,s.buttons=[{iconPath:new u.ThemeIcon(o?"folder":"globe"),tooltip:o?h:p}],s.show();let c=await new Promise((t=>{s.onDidAccept((()=>t(s.selectedItems[0]))),s.onDidHide((()=>t(void 0))),s.onDidTriggerButton((()=>{t({scope:o?u.ConfigurationTarget.Workspace:u.ConfigurationTarget.Global})}))}));if(s.dispose(),c){if("scope"in c)return await k(t,c.scope);"state"in c&&(c.state!==i?a.update(x,c.state,e):S&&(c={setTempDisabled:!1})),"setTempDisabled"in c&&(M(t,i,!0),S=c.setTempDisabled,c.setTempDisabled?await B():await O(t),M(t,i,!1))}}function _(){return u.workspace.getConfiguration(A).get(x)??"disabled"}async function O(t){const e=await async function(t){const e=t.workspaceState.get(v),a=u.extensions.getExtension("ms-vscode.js-debug-nightly")?.extensionPath||u.extensions.getExtension("ms-vscode.js-debug")?.extensionPath,n=function(){const t={},e=u.workspace.getConfiguration(A);for(const a of j)t[a]=e.get(a);return JSON.stringify(t)}();if(e?.jsDebugPath===a&&e?.settingsValue===n)return e.ipcAddress;const o=await u.commands.executeCommand("extension.js-debug.setAutoAttachVariables",e?.ipcAddress);if(!o)return;const s=o.ipcAddress;return await t.workspaceState.update(v,{ipcAddress:s,jsDebugPath:a,settingsValue:n}),s}(t);if(e)return D=F(e).catch((async t=>{if(console.error("[debug-auto-launch] Error creating auto attach server: ",t),"win32"!==process.platform)try{await r.promises.access((0,l.dirname)(e))}catch{return console.error("[debug-auto-launch] Refreshing variables from error"),void T()}})),await D}const F=async t=>{try{return await W(t)}catch(e){return await r.promises.unlink(t).catch((()=>{})),await W(t)}},W=t=>new Promise(((e,a)=>{const n=(0,c.createServer)((t=>{const e=[];t.on("data",(async a=>{if(0===a[a.length-1]){e.push(a.slice(0,-1));try{await u.commands.executeCommand("extension.js-debug.autoAttachToProcess",JSON.parse(Buffer.concat(e).toString())),t.write(Buffer.from([0]))}catch(e){t.write(Buffer.from([1])),console.error(e)}}else e.push(a)}))})).on("error",a).listen(t,(()=>e(n)))}));async function B(){const t=await D;t&&await new Promise((e=>t.close(e)))}const V={async disabled(t){await async function(t){(D||await t.workspaceState.get(v))&&(await t.workspaceState.update(v,void 0),await u.commands.executeCommand("extension.js-debug.clearAutoAttachVariables"),await B())}(t)},async onlyWithFlag(t){await O(t)},async smart(t){await O(t)},async always(t){await O(t)}};function M(t,e,a=!1){if("disabled"===e&&!a)return void C?.hide();C||(C=u.window.createStatusBarItem("status.debug.autoAttach",u.StatusBarAlignment.Left),C.name=u.l10n.t("Debug Auto Attach"),C.command=y,C.tooltip=u.l10n.t("Automatically attach to node.js processes in debug mode"),t.subscriptions.push(C));let n=a?"$(loading) ":"";n+=S?m:d[e],C.text=n,C.show()}function I(t){P=P.then((async({context:e,state:a})=>t===a?{context:e,state:a}:(null!==a&&M(e,a,!0),await V[t](e),S=!1,M(e,t,!1),{context:e,state:t})))}},398:t=>{t.exports=require("vscode")},896:t=>{t.exports=require("fs")},278:t=>{t.exports=require("net")},928:t=>{t.exports=require("path")}},e={},a=function a(n){var o=e[n];if(void 0!==o)return o.exports;var s=e[n]={exports:{}};return t[n].call(s.exports,s,s.exports,a),s.exports}(256),n=exports;for(var o in a)n[o]=a[o];a.__esModule&&Object.defineProperty(n,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/extensions/debug-auto-launch/dist/extension.js.map