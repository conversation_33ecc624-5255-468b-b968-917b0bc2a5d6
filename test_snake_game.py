#!/usr/bin/env python3
"""
贪吃蛇游戏测试文件
测试游戏的各种功能和边界情况
"""

import unittest
import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟pygame模块以便在没有显示器的环境中测试
class MockPygame:
    def __init__(self):
        self.QUIT = 1
        self.KEYDOWN = 2
        self.K_ESCAPE = 27
        self.K_SPACE = 32
        self.K_UP = 273
        self.K_DOWN = 274
        self.K_LEFT = 275
        self.K_RIGHT = 276
        self.K_w = 119
        self.K_s = 115
        self.K_a = 97
        self.K_d = 100
        self.K_m = 109
        self.K_r = 114

    def init(self):
        pass

    class display:
        @staticmethod
        def set_mode(size):
            return Mock()

        @staticmethod
        def set_caption(title):
            pass

        @staticmethod
        def flip():
            pass

    class time:
        @staticmethod
        def Clock():
            clock = Mock()
            clock.tick = Mock()
            return clock

    class font:
        @staticmethod
        def Font(name, size):
            font = Mock()
            font.render = Mock(return_value=Mock())
            return font

    class mixer:
        @staticmethod
        def init():
            pass

        @staticmethod
        def Sound(filename):
            sound = Mock()
            sound.play = Mock()
            return sound

    @staticmethod
    def Surface(size):
        surface = Mock()
        surface.fill = Mock()
        surface.blit = Mock()
        surface.set_alpha = Mock()
        return surface

    class draw:
        @staticmethod
        def rect(surface, color, rect, width=0):
            pass

        @staticmethod
        def ellipse(surface, color, rect, width=0):
            pass

        @staticmethod
        def circle(surface, color, pos, radius):
            pass

        @staticmethod
        def line(surface, color, start, end):
            pass

        @staticmethod
        def polygon(surface, color, points):
            pass

    class Rect:
        def __init__(self, x, y, width, height):
            self.x = x
            self.y = y
            self.width = width
            self.height = height

        def get_rect(self, **kwargs):
            return self

        def inflate(self, x, y):
            return self

    @staticmethod
    def quit():
        pass

# 模拟pygame
sys.modules['pygame'] = MockPygame()
sys.modules['pygame.mixer'] = MockPygame.mixer()
sys.modules['pygame.sndarray'] = Mock()
sys.modules['pygame.array'] = Mock()

# 现在导入游戏模块
from snake_game import GameConfig, Colors, Direction, HighScoreManager, SoundManager, SnakeGame

class TestGameConfig(unittest.TestCase):
    """测试游戏配置类"""

    def test_default_config(self):
        """测试默认配置"""
        config = GameConfig()
        self.assertEqual(config.WINDOW_WIDTH, 800)
        self.assertEqual(config.WINDOW_HEIGHT, 600)
        self.assertEqual(config.GRID_SIZE, 20)
        self.assertEqual(config.GRID_WIDTH, 40)
        self.assertEqual(config.GRID_HEIGHT, 30)

class TestHighScoreManager(unittest.TestCase):
    """测试高分管理器"""

    def setUp(self):
        self.test_file = "test_high_scores.json"
        self.manager = HighScoreManager(self.test_file)

    def tearDown(self):
        if os.path.exists(self.test_file):
            os.remove(self.test_file)

    def test_initial_high_scores(self):
        """测试初始高分"""
        self.assertEqual(len(self.manager.high_scores), 10)
        self.assertEqual(self.manager.get_high_score(), 0)

    def test_add_score(self):
        """测试添加分数"""
        # 添加新纪录
        self.assertTrue(self.manager.add_score(100))
        self.assertEqual(self.manager.get_high_score(), 100)

        # 添加更高分数
        self.assertTrue(self.manager.add_score(200))
        self.assertEqual(self.manager.get_high_score(), 200)

        # 填满高分榜
        for i in range(8):
            self.manager.add_score(150 + i)

        # 现在添加低分数应该返回False
        self.assertFalse(self.manager.add_score(50))

class TestSoundManager(unittest.TestCase):
    """测试音效管理器"""

    def test_sound_toggle(self):
        """测试音效开关"""
        manager = SoundManager()
        self.assertTrue(manager.sound_enabled)

        manager.toggle_sound()
        self.assertFalse(manager.sound_enabled)

class TestSnakeGame(unittest.TestCase):
    """测试贪吃蛇游戏"""

    def setUp(self):
        self.config = GameConfig()
        self.game = SnakeGame(self.config)

    def test_initial_state(self):
        """测试初始状态"""
        self.assertEqual(len(self.game.snake), 1)
        self.assertEqual(self.game.direction, Direction.RIGHT)
        self.assertEqual(self.game.score, 0)
        self.assertFalse(self.game.game_over)
        self.assertFalse(self.game.paused)
    
    def test_generate_food(self):
        """测试食物生成在有效范围内"""
        food = self.game.generate_food()
        self.assertIsInstance(food, tuple)
        self.assertEqual(len(food), 2)
        x, y = food
        self.assertGreaterEqual(x, 0)
        self.assertLess(x, self.config.GRID_WIDTH)
        self.assertGreaterEqual(y, 0)
        self.assertLess(y, self.config.GRID_HEIGHT)
    
    def test_generate_food_not_on_snake(self):
        """Test that food is not generated on the snake body."""
        # Make snake longer to increase chance of collision
        self.game.snake = [(10, 10), (9, 10), (8, 10), (7, 10)]
        
        # Generate food multiple times to ensure it's never on snake
        for _ in range(10):
            food = self.game.generate_food()
            self.assertNotIn(food, self.game.snake)
    
    def test_generate_special_food(self):
        """Test special food generation."""
        # Test multiple times due to randomness
        special_foods_generated = 0
        for _ in range(100):
            special_food = self.game.generate_special_food()
            if special_food is not None:
                special_foods_generated += 1
                self.assertIsInstance(special_food, tuple)
                self.assertEqual(len(special_food), 2)
                x, y = special_food
                self.assertGreaterEqual(x, 0)
                self.assertLess(x, self.config.GRID_WIDTH)
                self.assertGreaterEqual(y, 0)
                self.assertLess(y, self.config.GRID_HEIGHT)
                self.assertNotIn(special_food, self.game.snake)
                self.assertNotEqual(special_food, self.game.food)
        
        # Should generate some special foods (30% chance each time)
        self.assertGreater(special_foods_generated, 0)
    
    def test_reset_game(self):
        """Test that game reset works correctly."""
        # Modify game state
        self.game.snake = [(5, 5), (4, 5), (3, 5)]
        self.game.score = 100
        self.game.game_over = True
        self.game.paused = True
        self.game.speed = 15
        self.game.special_food = (10, 10)
        self.game.food_pulse = 5.0
        
        # Reset game
        self.game.reset_game()
        
        # Check that everything is reset
        self.assertEqual(len(self.game.snake), 1)
        self.assertEqual(self.game.direction, Direction.RIGHT)
        self.assertEqual(self.game.score, 0)
        self.assertFalse(self.game.game_over)
        self.assertFalse(self.game.paused)
        self.assertEqual(self.game.speed, 8)
        self.assertIsNone(self.game.special_food)
        self.assertEqual(self.game.special_food_timer, 0)
        self.assertEqual(self.game.food_pulse, 0)
        self.assertEqual(len(self.game.snake_colors), 1)
    
    def test_snake_movement(self):
        """Test basic snake movement logic."""
        initial_head = self.game.snake[0]
        initial_length = len(self.game.snake)
        
        # Move snake (simulate one update cycle without food collision)
        self.game.direction = Direction.RIGHT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        # Simulate movement without eating food
        self.game.snake.insert(0, new_head)
        self.game.snake.pop()  # Remove tail since no food eaten
        
        # Check that snake moved correctly
        self.assertEqual(len(self.game.snake), initial_length)
        self.assertEqual(self.game.snake[0], new_head)
        self.assertNotEqual(self.game.snake[0], initial_head)
    
    def test_boundary_collision_detection(self):
        """Test that boundary collisions are detected correctly."""
        # Test left boundary
        self.game.snake = [(0, 10)]
        self.game.direction = Direction.LEFT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertLess(new_head[0], 0)  # Should be out of bounds
        
        # Test right boundary
        self.game.snake = [(self.config.GRID_WIDTH - 1, 10)]
        self.game.direction = Direction.RIGHT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertGreaterEqual(new_head[0], self.config.GRID_WIDTH)  # Should be out of bounds

        # Test top boundary
        self.game.snake = [(10, 0)]
        self.game.direction = Direction.UP
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertLess(new_head[1], 0)  # Should be out of bounds

        # Test bottom boundary
        self.game.snake = [(10, self.config.GRID_HEIGHT - 1)]
        self.game.direction = Direction.DOWN
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertGreaterEqual(new_head[1], self.config.GRID_HEIGHT)  # Should be out of bounds
    
    def test_self_collision_detection(self):
        """Test that self-collision is detected correctly."""
        # Create a snake that will collide with itself
        self.game.snake = [(5, 5), (4, 5), (3, 5), (3, 6), (4, 6), (5, 6)]
        self.game.direction = Direction.DOWN
        
        # Next move would be (5, 6) which is already in the snake
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        self.assertIn(new_head, self.game.snake)  # Should detect collision
    
    def test_food_consumption(self):
        """Test that eating food increases score and snake length."""
        initial_score = self.game.score
        initial_length = len(self.game.snake)
        initial_colors = len(self.game.snake_colors)
        
        # Place food at snake's next position
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        self.game.food = (head_x + dx, head_y + dy)
        
        # Simulate eating food
        new_head = self.game.food
        if new_head == self.game.food:
            self.game.score += 10
            self.game.snake_colors.append((255, 0, 0))  # Add red color
            # Don't pop tail when eating food
        
        # Check results
        self.assertEqual(self.game.score, initial_score + 10)
        self.assertEqual(len(self.game.snake_colors), initial_colors + 1)
    
    def test_special_food_consumption(self):
        """Test that eating special food gives correct bonus."""
        initial_score = self.game.score
        initial_colors = len(self.game.snake_colors)
        
        # Place special food at snake's next position
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        self.game.special_food = (head_x + dx, head_y + dy)
        
        # Simulate eating special food
        new_head = self.game.special_food
        if new_head == self.game.special_food:
            self.game.score += 50
            # Add multiple colors for special food
            for _ in range(3):
                self.game.snake_colors.append((255, 255, 0))  # Add yellow
            self.game.special_food = None
        
        # Check results
        self.assertEqual(self.game.score, initial_score + 50)
        self.assertEqual(len(self.game.snake_colors), initial_colors + 3)
        self.assertIsNone(self.game.special_food)

if __name__ == '__main__':
    unittest.main()
