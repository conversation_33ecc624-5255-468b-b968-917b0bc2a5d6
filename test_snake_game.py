import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add the current directory to the path so we can import snake_game
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock pygame before importing snake_game
sys.modules['pygame'] = MagicMock()
sys.modules['pygame.display'] = MagicMock()
sys.modules['pygame.font'] = MagicMock()
sys.modules['pygame.time'] = MagicMock()

from snake_game import SnakeGame, Direction, GRID_WIDTH, GRID_HEIGHT

class TestSnakeGame(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        with patch('pygame.display.set_mode'), \
             patch('pygame.display.set_caption'), \
             patch('pygame.time.Clock'), \
             patch('pygame.font.Font'):
            self.game = SnakeGame()
    
    def test_initial_game_state(self):
        """Test that the game initializes with correct default values."""
        self.assertEqual(len(self.game.snake), 1)
        self.assertEqual(self.game.direction, Direction.RIGHT)
        self.assertEqual(self.game.score, 0)
        self.assertFalse(self.game.game_over)
        self.assertFalse(self.game.paused)
        self.assertEqual(self.game.speed, 8)
        self.assertIsNone(self.game.special_food)
        self.assertEqual(self.game.special_food_timer, 0)
        self.assertEqual(self.game.food_pulse, 0)
        self.assertEqual(len(self.game.snake_colors), 1)
    
    def test_generate_food(self):
        """Test that food is generated within valid bounds."""
        food = self.game.generate_food()
        self.assertIsInstance(food, tuple)
        self.assertEqual(len(food), 2)
        x, y = food
        self.assertGreaterEqual(x, 0)
        self.assertLess(x, GRID_WIDTH)
        self.assertGreaterEqual(y, 0)
        self.assertLess(y, GRID_HEIGHT)
    
    def test_generate_food_not_on_snake(self):
        """Test that food is not generated on the snake body."""
        # Make snake longer to increase chance of collision
        self.game.snake = [(10, 10), (9, 10), (8, 10), (7, 10)]
        
        # Generate food multiple times to ensure it's never on snake
        for _ in range(10):
            food = self.game.generate_food()
            self.assertNotIn(food, self.game.snake)
    
    def test_generate_special_food(self):
        """Test special food generation."""
        # Test multiple times due to randomness
        special_foods_generated = 0
        for _ in range(100):
            special_food = self.game.generate_special_food()
            if special_food is not None:
                special_foods_generated += 1
                self.assertIsInstance(special_food, tuple)
                self.assertEqual(len(special_food), 2)
                x, y = special_food
                self.assertGreaterEqual(x, 0)
                self.assertLess(x, GRID_WIDTH)
                self.assertGreaterEqual(y, 0)
                self.assertLess(y, GRID_HEIGHT)
                self.assertNotIn(special_food, self.game.snake)
                self.assertNotEqual(special_food, self.game.food)
        
        # Should generate some special foods (30% chance each time)
        self.assertGreater(special_foods_generated, 0)
    
    def test_reset_game(self):
        """Test that game reset works correctly."""
        # Modify game state
        self.game.snake = [(5, 5), (4, 5), (3, 5)]
        self.game.score = 100
        self.game.game_over = True
        self.game.paused = True
        self.game.speed = 15
        self.game.special_food = (10, 10)
        self.game.food_pulse = 5.0
        
        # Reset game
        self.game.reset_game()
        
        # Check that everything is reset
        self.assertEqual(len(self.game.snake), 1)
        self.assertEqual(self.game.direction, Direction.RIGHT)
        self.assertEqual(self.game.score, 0)
        self.assertFalse(self.game.game_over)
        self.assertFalse(self.game.paused)
        self.assertEqual(self.game.speed, 8)
        self.assertIsNone(self.game.special_food)
        self.assertEqual(self.game.special_food_timer, 0)
        self.assertEqual(self.game.food_pulse, 0)
        self.assertEqual(len(self.game.snake_colors), 1)
    
    def test_snake_movement(self):
        """Test basic snake movement logic."""
        initial_head = self.game.snake[0]
        initial_length = len(self.game.snake)
        
        # Move snake (simulate one update cycle without food collision)
        self.game.direction = Direction.RIGHT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        # Simulate movement without eating food
        self.game.snake.insert(0, new_head)
        self.game.snake.pop()  # Remove tail since no food eaten
        
        # Check that snake moved correctly
        self.assertEqual(len(self.game.snake), initial_length)
        self.assertEqual(self.game.snake[0], new_head)
        self.assertNotEqual(self.game.snake[0], initial_head)
    
    def test_boundary_collision_detection(self):
        """Test that boundary collisions are detected correctly."""
        # Test left boundary
        self.game.snake = [(0, 10)]
        self.game.direction = Direction.LEFT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertLess(new_head[0], 0)  # Should be out of bounds
        
        # Test right boundary
        self.game.snake = [(GRID_WIDTH - 1, 10)]
        self.game.direction = Direction.RIGHT
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertGreaterEqual(new_head[0], GRID_WIDTH)  # Should be out of bounds
        
        # Test top boundary
        self.game.snake = [(10, 0)]
        self.game.direction = Direction.UP
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertLess(new_head[1], 0)  # Should be out of bounds
        
        # Test bottom boundary
        self.game.snake = [(10, GRID_HEIGHT - 1)]
        self.game.direction = Direction.DOWN
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        self.assertGreaterEqual(new_head[1], GRID_HEIGHT)  # Should be out of bounds
    
    def test_self_collision_detection(self):
        """Test that self-collision is detected correctly."""
        # Create a snake that will collide with itself
        self.game.snake = [(5, 5), (4, 5), (3, 5), (3, 6), (4, 6), (5, 6)]
        self.game.direction = Direction.DOWN
        
        # Next move would be (5, 6) which is already in the snake
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        self.assertIn(new_head, self.game.snake)  # Should detect collision
    
    def test_food_consumption(self):
        """Test that eating food increases score and snake length."""
        initial_score = self.game.score
        initial_length = len(self.game.snake)
        initial_colors = len(self.game.snake_colors)
        
        # Place food at snake's next position
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        self.game.food = (head_x + dx, head_y + dy)
        
        # Simulate eating food
        new_head = self.game.food
        if new_head == self.game.food:
            self.game.score += 10
            self.game.snake_colors.append((255, 0, 0))  # Add red color
            # Don't pop tail when eating food
        
        # Check results
        self.assertEqual(self.game.score, initial_score + 10)
        self.assertEqual(len(self.game.snake_colors), initial_colors + 1)
    
    def test_special_food_consumption(self):
        """Test that eating special food gives correct bonus."""
        initial_score = self.game.score
        initial_colors = len(self.game.snake_colors)
        
        # Place special food at snake's next position
        head_x, head_y = self.game.snake[0]
        dx, dy = self.game.direction.value
        self.game.special_food = (head_x + dx, head_y + dy)
        
        # Simulate eating special food
        new_head = self.game.special_food
        if new_head == self.game.special_food:
            self.game.score += 50
            # Add multiple colors for special food
            for _ in range(3):
                self.game.snake_colors.append((255, 255, 0))  # Add yellow
            self.game.special_food = None
        
        # Check results
        self.assertEqual(self.game.score, initial_score + 50)
        self.assertEqual(len(self.game.snake_colors), initial_colors + 3)
        self.assertIsNone(self.game.special_food)

if __name__ == '__main__':
    unittest.main()
