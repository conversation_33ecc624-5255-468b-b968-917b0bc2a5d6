import pygame
import random
import sys
from enum import Enum
import math

# 初始化pygame
pygame.init()

# 游戏常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GRID_SIZE = 20
GRID_WIDTH = WINDOW_WIDTH // GRID_SIZE
GRID_HEIGHT = WINDOW_HEIGHT // GRID_SIZE

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
PINK = (255, 192, 203)
CYAN = (0, 255, 255)
DARK_GREEN = (0, 128, 0)
LIGHT_BLUE = (173, 216, 230)

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class SnakeGame:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("🐍 彩色贪吃蛇游戏 🐍")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # 游戏状态
        self.reset_game()
        
    def reset_game(self):
        """重置游戏状态"""
        self.snake = [(GRID_WIDTH // 2, GRID_HEIGHT // 2)]
        self.direction = Direction.RIGHT
        self.food = self.generate_food()
        self.score = 0
        self.game_over = False
        self.paused = False
        self.speed = 8
        
        # 特殊食物
        self.special_food = None
        self.special_food_timer = 0
        
        # 动画效果
        self.food_pulse = 0
        self.snake_colors = [GREEN]
        
    def generate_food(self):
        """生成食物位置"""
        while True:
            food = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))
            if food not in self.snake:
                return food
                
    def generate_special_food(self):
        """生成特殊食物"""
        if random.random() < 0.3:  # 30%概率生成特殊食物
            while True:
                special_food = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))
                if special_food not in self.snake and special_food != self.food:
                    return special_food
        return None
        
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
                
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_SPACE:
                    if self.game_over:
                        self.reset_game()
                    else:
                        self.paused = not self.paused
                elif not self.paused and not self.game_over:
                    if event.key == pygame.K_UP and self.direction != Direction.DOWN:
                        self.direction = Direction.UP
                    elif event.key == pygame.K_DOWN and self.direction != Direction.UP:
                        self.direction = Direction.DOWN
                    elif event.key == pygame.K_LEFT and self.direction != Direction.RIGHT:
                        self.direction = Direction.LEFT
                    elif event.key == pygame.K_RIGHT and self.direction != Direction.LEFT:
                        self.direction = Direction.RIGHT
        return True
        
    def update(self):
        """更新游戏逻辑"""
        if self.paused or self.game_over:
            return
            
        # 移动蛇头
        head_x, head_y = self.snake[0]
        dx, dy = self.direction.value
        new_head = (head_x + dx, head_y + dy)
        
        # 检查边界碰撞
        if (new_head[0] < 0 or new_head[0] >= GRID_WIDTH or 
            new_head[1] < 0 or new_head[1] >= GRID_HEIGHT):
            self.game_over = True
            return
            
        # 检查自身碰撞
        if new_head in self.snake:
            self.game_over = True
            return
            
        self.snake.insert(0, new_head)
        
        # 检查食物碰撞
        ate_food = False
        if new_head == self.food:
            self.score += 10
            self.food = self.generate_food()
            ate_food = True
            # 增加蛇身颜色
            colors = [RED, BLUE, YELLOW, PURPLE, ORANGE, PINK, CYAN]
            self.snake_colors.append(random.choice(colors))
            # 提升速度
            if self.speed < 15:
                self.speed += 0.5
                
        # 检查特殊食物碰撞
        if self.special_food and new_head == self.special_food:
            self.score += 50
            self.special_food = None
            ate_food = True
            # 添加多个颜色段
            for _ in range(3):
                self.snake_colors.append(random.choice([YELLOW, ORANGE, PINK]))
                
        if not ate_food:
            self.snake.pop()
            if len(self.snake_colors) > len(self.snake):
                self.snake_colors.pop()
                
        # 特殊食物逻辑
        self.special_food_timer += 1
        if self.special_food_timer > 300:  # 5秒后生成特殊食物
            if not self.special_food:
                self.special_food = self.generate_special_food()
            self.special_food_timer = 0
            
        # 动画效果
        self.food_pulse += 0.2

    def draw_grid(self):
        """绘制网格背景"""
        for x in range(0, WINDOW_WIDTH, GRID_SIZE):
            pygame.draw.line(self.screen, (40, 40, 40), (x, 0), (x, WINDOW_HEIGHT))
        for y in range(0, WINDOW_HEIGHT, GRID_SIZE):
            pygame.draw.line(self.screen, (40, 40, 40), (0, y), (WINDOW_WIDTH, y))

    def draw_snake(self):
        """绘制蛇身"""
        for i, (x, y) in enumerate(self.snake):
            rect = pygame.Rect(x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE)

            # 使用不同颜色绘制蛇身
            if i < len(self.snake_colors):
                color = self.snake_colors[i]
            else:
                color = GREEN

            # 蛇头特殊效果
            if i == 0:
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, WHITE, rect, 2)
                # 绘制眼睛
                eye_size = 3
                if self.direction == Direction.UP:
                    eye1 = (x * GRID_SIZE + 5, y * GRID_SIZE + 5)
                    eye2 = (x * GRID_SIZE + 15, y * GRID_SIZE + 5)
                elif self.direction == Direction.DOWN:
                    eye1 = (x * GRID_SIZE + 5, y * GRID_SIZE + 15)
                    eye2 = (x * GRID_SIZE + 15, y * GRID_SIZE + 15)
                elif self.direction == Direction.LEFT:
                    eye1 = (x * GRID_SIZE + 5, y * GRID_SIZE + 5)
                    eye2 = (x * GRID_SIZE + 5, y * GRID_SIZE + 15)
                else:  # RIGHT
                    eye1 = (x * GRID_SIZE + 15, y * GRID_SIZE + 5)
                    eye2 = (x * GRID_SIZE + 15, y * GRID_SIZE + 15)

                pygame.draw.circle(self.screen, BLACK, eye1, eye_size)
                pygame.draw.circle(self.screen, BLACK, eye2, eye_size)
            else:
                # 蛇身渐变效果
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, DARK_GREEN, rect, 1)

    def draw_food(self):
        """绘制普通食物"""
        x, y = self.food

        # 脉冲效果
        pulse_size = int(2 + math.sin(self.food_pulse) * 2)
        food_rect = pygame.Rect(x * GRID_SIZE + pulse_size, y * GRID_SIZE + pulse_size,
                               GRID_SIZE - pulse_size * 2, GRID_SIZE - pulse_size * 2)

        pygame.draw.ellipse(self.screen, RED, food_rect)
        pygame.draw.ellipse(self.screen, YELLOW, food_rect, 2)

    def draw_special_food(self):
        """绘制特殊食物"""
        if not self.special_food:
            return

        x, y = self.special_food
        rect = pygame.Rect(x * GRID_SIZE, y * GRID_SIZE, GRID_SIZE, GRID_SIZE)

        # 彩虹效果
        colors = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE]
        color_index = int(self.food_pulse * 2) % len(colors)

        pygame.draw.ellipse(self.screen, colors[color_index], rect)
        pygame.draw.ellipse(self.screen, WHITE, rect, 3)

        # 闪烁星星效果
        center_x = x * GRID_SIZE + GRID_SIZE // 2
        center_y = y * GRID_SIZE + GRID_SIZE // 2
        star_points = []
        for i in range(8):
            angle = i * math.pi / 4
            if i % 2 == 0:
                radius = 8
            else:
                radius = 4
            point_x = center_x + radius * math.cos(angle)
            point_y = center_y + radius * math.sin(angle)
            star_points.append((point_x, point_y))

        if len(star_points) >= 3:
            pygame.draw.polygon(self.screen, WHITE, star_points)

    def draw_ui(self):
        """绘制用户界面"""
        # 分数显示
        score_text = self.font.render(f"分数: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 10))

        # 速度显示
        speed_text = self.small_font.render(f"速度: {self.speed:.1f}", True, WHITE)
        self.screen.blit(speed_text, (10, 50))

        # 长度显示
        length_text = self.small_font.render(f"长度: {len(self.snake)}", True, WHITE)
        self.screen.blit(length_text, (10, 75))

        if self.paused:
            pause_text = self.font.render("游戏暂停 - 按空格继续", True, YELLOW)
            text_rect = pause_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2))
            pygame.draw.rect(self.screen, BLACK, text_rect.inflate(20, 10))
            self.screen.blit(pause_text, text_rect)

        if self.game_over:
            game_over_text = self.font.render("游戏结束!", True, RED)
            restart_text = self.small_font.render("按空格重新开始 | ESC退出", True, WHITE)

            game_over_rect = game_over_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 30))
            restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 10))

            pygame.draw.rect(self.screen, BLACK, game_over_rect.inflate(40, 20))
            pygame.draw.rect(self.screen, BLACK, restart_rect.inflate(40, 20))

            self.screen.blit(game_over_text, game_over_rect)
            self.screen.blit(restart_text, restart_rect)

    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(BLACK)
        self.draw_grid()
        self.draw_snake()
        self.draw_food()
        self.draw_special_food()
        self.draw_ui()
        pygame.display.flip()

    def run(self):
        """运行游戏主循环"""
        running = True
        while running:
            running = self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(self.speed)

        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = SnakeGame()
    game.run()
