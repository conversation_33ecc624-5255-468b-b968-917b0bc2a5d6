import pygame
import random
import sys
import json
import os
from enum import Enum
from typing import List, <PERSON><PERSON>, Optional
from dataclasses import dataclass
import math
import time

# 初始化pygame
pygame.init()
pygame.mixer.init()

@dataclass
class GameConfig:
    """游戏配置类"""
    # 窗口设置
    WINDOW_WIDTH: int = 800
    WINDOW_HEIGHT: int = 600
    GRID_SIZE: int = 20

    # 游戏设置
    INITIAL_SPEED: float = 8.0
    MAX_SPEED: float = 20.0
    SPEED_INCREMENT: float = 0.3
    SPECIAL_FOOD_PROBABILITY: float = 0.3
    SPECIAL_FOOD_TIMER: int = 300  # 5秒 (60 FPS)

    # 分数设置
    NORMAL_FOOD_SCORE: int = 10
    SPECIAL_FOOD_SCORE: int = 50

    @property
    def GRID_WIDTH(self) -> int:
        return self.WINDOW_WIDTH // self.GRID_SIZE

    @property
    def GRID_HEIGHT(self) -> int:
        return self.WINDOW_HEIGHT // self.GRID_SIZE

class Colors:
    """颜色常量类"""
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    RED = (255, 0, 0)
    GREEN = (0, 255, 0)
    BLUE = (0, 0, 255)
    YELLOW = (255, 255, 0)
    PURPLE = (128, 0, 128)
    ORANGE = (255, 165, 0)
    PINK = (255, 192, 203)
    CYAN = (0, 255, 255)
    DARK_GREEN = (0, 128, 0)
    LIGHT_BLUE = (173, 216, 230)
    GRAY = (128, 128, 128)
    DARK_GRAY = (64, 64, 64)

    # 彩虹色彩序列
    RAINBOW_COLORS = [RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE]
    SNAKE_BODY_COLORS = [RED, BLUE, YELLOW, PURPLE, ORANGE, PINK, CYAN]

class Direction(Enum):
    UP = (0, -1)
    DOWN = (0, 1)
    LEFT = (-1, 0)
    RIGHT = (1, 0)

class SoundManager:
    """音效管理器"""
    def __init__(self):
        self.sounds = {}
        self.sound_enabled = True
        self._load_sounds()

    def _load_sounds(self):
        """加载音效文件（如果存在）"""
        sound_files = {
            'eat': 'eat.wav',
            'special_eat': 'special_eat.wav',
            'game_over': 'game_over.wav',
            'new_record': 'new_record.wav'
        }

        for sound_name, filename in sound_files.items():
            try:
                if os.path.exists(filename):
                    self.sounds[sound_name] = pygame.mixer.Sound(filename)
                else:
                    # 创建简单的程序化音效
                    self.sounds[sound_name] = self._create_sound(sound_name)
            except pygame.error:
                self.sounds[sound_name] = None

    def _create_sound(self, sound_type: str) -> Optional[pygame.mixer.Sound]:
        """创建程序化音效"""
        try:
            # 创建简单的音效（这里只是占位符，实际可以生成更复杂的音效）
            duration = 0.1
            sample_rate = 22050
            frames = int(duration * sample_rate)

            if sound_type == 'eat':
                # 简单的"吃"音效
                frequency = 800
            elif sound_type == 'special_eat':
                frequency = 1200
            elif sound_type == 'game_over':
                frequency = 200
            else:
                frequency = 600

            # 生成简单的正弦波音效
            arr = []
            for i in range(frames):
                wave = 4096 * math.sin(2 * math.pi * frequency * i / sample_rate)
                arr.append([int(wave), int(wave)])

            sound = pygame.sndarray.make_sound(pygame.array.array('i', arr))
            return sound
        except:
            return None

    def play(self, sound_name: str):
        """播放音效"""
        if self.sound_enabled and sound_name in self.sounds and self.sounds[sound_name]:
            try:
                self.sounds[sound_name].play()
            except pygame.error:
                pass

    def toggle_sound(self):
        """切换音效开关"""
        self.sound_enabled = not self.sound_enabled

class HighScoreManager:
    """高分管理器"""
    def __init__(self, filename: str = "high_scores.json"):
        self.filename = filename
        self.high_scores = self.load_high_scores()

    def load_high_scores(self) -> List[int]:
        """加载高分记录"""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass
        return [0] * 10  # 默认10个高分记录

    def save_high_scores(self) -> None:
        """保存高分记录"""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.high_scores, f)
        except IOError:
            pass

    def add_score(self, score: int) -> bool:
        """添加新分数，返回是否创造新纪录"""
        if score > min(self.high_scores):
            self.high_scores.append(score)
            self.high_scores.sort(reverse=True)
            self.high_scores = self.high_scores[:10]
            self.save_high_scores()
            return True
        return False

    def get_high_score(self) -> int:
        """获取最高分"""
        return max(self.high_scores) if self.high_scores else 0

class SnakeGame:
    def __init__(self, config: GameConfig = None):
        self.config = config or GameConfig()
        self.screen = pygame.display.set_mode((self.config.WINDOW_WIDTH, self.config.WINDOW_HEIGHT))
        pygame.display.set_caption("🐍 优化版贪吃蛇游戏 🐍")
        self.clock = pygame.time.Clock()

        # 字体缓存
        self.fonts = {
            'small': pygame.font.Font(None, 24),
            'medium': pygame.font.Font(None, 36),
            'large': pygame.font.Font(None, 48)
        }

        # 管理器
        self.high_score_manager = HighScoreManager()
        self.sound_manager = SoundManager()

        # 性能优化：预计算常用值
        self.grid_size = self.config.GRID_SIZE
        self.grid_width = self.config.GRID_WIDTH
        self.grid_height = self.config.GRID_HEIGHT

        # 渲染优化：创建表面缓存
        self._create_render_cache()

        # 游戏状态
        self.reset_game()

    def _create_render_cache(self):
        """创建渲染缓存以提高性能"""
        # 预渲染网格线
        self.grid_surface = pygame.Surface((self.config.WINDOW_WIDTH, self.config.WINDOW_HEIGHT))
        self.grid_surface.fill(Colors.BLACK)
        grid_color = Colors.DARK_GRAY

        for x in range(0, self.config.WINDOW_WIDTH, self.grid_size):
            pygame.draw.line(self.grid_surface, grid_color, (x, 0), (x, self.config.WINDOW_HEIGHT))
        for y in range(0, self.config.WINDOW_HEIGHT, self.grid_size):
            pygame.draw.line(self.grid_surface, grid_color, (0, y), (self.config.WINDOW_WIDTH, y))

    def reset_game(self):
        """重置游戏状态"""
        self.snake = [(self.config.GRID_WIDTH // 2, self.config.GRID_HEIGHT // 2)]
        self.direction = Direction.RIGHT
        self.score = 0
        self.game_over = False
        self.paused = False
        self.speed = self.config.INITIAL_SPEED

        # 特殊食物
        self.special_food = None
        self.special_food_timer = 0

        # 动画效果
        self.food_pulse = 0
        self.snake_colors = [Colors.GREEN]

        # 性能优化：缓存蛇身位置集合
        self.snake_positions = set(self.snake)

        # 生成食物（在snake_positions初始化之后）
        self.food = self.generate_food()
        
    def generate_food(self) -> Tuple[int, int]:
        """生成食物位置"""
        max_attempts = 100  # 防止无限循环
        attempts = 0
        while attempts < max_attempts:
            food = (random.randint(0, self.config.GRID_WIDTH - 1),
                   random.randint(0, self.config.GRID_HEIGHT - 1))
            if food not in self.snake_positions:
                return food
            attempts += 1
        # 如果找不到空位，返回第一个可用位置
        for x in range(self.config.GRID_WIDTH):
            for y in range(self.config.GRID_HEIGHT):
                if (x, y) not in self.snake_positions:
                    return (x, y)
        return (0, 0)  # 最后的备选方案

    def generate_special_food(self) -> Optional[Tuple[int, int]]:
        """生成特殊食物"""
        if random.random() < self.config.SPECIAL_FOOD_PROBABILITY:
            max_attempts = 50
            attempts = 0
            while attempts < max_attempts:
                special_food = (random.randint(0, self.config.GRID_WIDTH - 1),
                               random.randint(0, self.config.GRID_HEIGHT - 1))
                if (special_food not in self.snake_positions and
                    special_food != self.food):
                    return special_food
                attempts += 1
        return None
        
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False

            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                elif event.key == pygame.K_SPACE:
                    if self.game_over:
                        self.reset_game()
                    else:
                        self.paused = not self.paused
                elif event.key == pygame.K_m:
                    # 切换音效
                    self.sound_manager.toggle_sound()
                elif event.key == pygame.K_r and self.game_over:
                    # R键重新开始
                    self.reset_game()
                elif not self.paused and not self.game_over:
                    # 方向控制（箭头键）
                    if event.key == pygame.K_UP and self.direction != Direction.DOWN:
                        self.direction = Direction.UP
                    elif event.key == pygame.K_DOWN and self.direction != Direction.UP:
                        self.direction = Direction.DOWN
                    elif event.key == pygame.K_LEFT and self.direction != Direction.RIGHT:
                        self.direction = Direction.LEFT
                    elif event.key == pygame.K_RIGHT and self.direction != Direction.LEFT:
                        self.direction = Direction.RIGHT
                    # WASD控制
                    elif event.key == pygame.K_w and self.direction != Direction.DOWN:
                        self.direction = Direction.UP
                    elif event.key == pygame.K_s and self.direction != Direction.UP:
                        self.direction = Direction.DOWN
                    elif event.key == pygame.K_a and self.direction != Direction.RIGHT:
                        self.direction = Direction.LEFT
                    elif event.key == pygame.K_d and self.direction != Direction.LEFT:
                        self.direction = Direction.RIGHT
        return True
        
    def update(self):
        """更新游戏逻辑"""
        if self.paused or self.game_over:
            return

        # 移动蛇头
        head_x, head_y = self.snake[0]
        dx, dy = self.direction.value
        new_head = (head_x + dx, head_y + dy)

        # 检查边界碰撞
        if (new_head[0] < 0 or new_head[0] >= self.config.GRID_WIDTH or
            new_head[1] < 0 or new_head[1] >= self.config.GRID_HEIGHT):
            self.game_over = True
            self._handle_game_over()
            return

        # 检查自身碰撞（使用缓存的位置集合提高性能）
        if new_head in self.snake_positions:
            self.game_over = True
            self._handle_game_over()
            return

        # 添加新蛇头
        self.snake.insert(0, new_head)
        self.snake_positions.add(new_head)

        # 检查食物碰撞
        ate_food = False
        if new_head == self.food:
            self.score += self.config.NORMAL_FOOD_SCORE
            self.food = self.generate_food()
            ate_food = True
            # 播放音效
            self.sound_manager.play('eat')
            # 增加蛇身颜色
            self.snake_colors.append(random.choice(Colors.SNAKE_BODY_COLORS))
            # 提升速度
            if self.speed < self.config.MAX_SPEED:
                self.speed += self.config.SPEED_INCREMENT

        # 检查特殊食物碰撞
        if self.special_food and new_head == self.special_food:
            self.score += self.config.SPECIAL_FOOD_SCORE
            self.special_food = None
            ate_food = True
            # 播放特殊音效
            self.sound_manager.play('special_eat')
            # 添加多个颜色段
            for _ in range(3):
                self.snake_colors.append(random.choice([Colors.YELLOW, Colors.ORANGE, Colors.PINK]))

        if not ate_food:
            # 移除蛇尾
            tail = self.snake.pop()
            self.snake_positions.discard(tail)
            if len(self.snake_colors) > len(self.snake):
                self.snake_colors.pop()

        # 特殊食物逻辑
        self.special_food_timer += 1
        if self.special_food_timer > self.config.SPECIAL_FOOD_TIMER:
            if not self.special_food:
                self.special_food = self.generate_special_food()
            self.special_food_timer = 0

        # 动画效果
        self.food_pulse += 0.2

    def _handle_game_over(self):
        """处理游戏结束"""
        # 检查是否创造新纪录
        self.is_new_record = self.high_score_manager.add_score(self.score)

        # 播放音效
        if self.is_new_record:
            self.sound_manager.play('new_record')
        else:
            self.sound_manager.play('game_over')

    def draw_grid(self):
        """绘制网格背景（使用缓存）"""
        self.screen.blit(self.grid_surface, (0, 0))

    def draw_snake(self):
        """绘制蛇身"""
        for i, (x, y) in enumerate(self.snake):
            rect = pygame.Rect(x * self.config.GRID_SIZE, y * self.config.GRID_SIZE,
                             self.config.GRID_SIZE, self.config.GRID_SIZE)

            # 使用不同颜色绘制蛇身
            if i < len(self.snake_colors):
                color = self.snake_colors[i]
            else:
                color = Colors.GREEN

            # 蛇头特殊效果
            if i == 0:
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, Colors.WHITE, rect, 2)
                self._draw_snake_eyes(x, y)
            else:
                # 蛇身渐变效果
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, Colors.DARK_GREEN, rect, 1)

    def _draw_snake_eyes(self, x: int, y: int):
        """绘制蛇眼睛"""
        eye_size = 3
        grid_size = self.config.GRID_SIZE

        if self.direction == Direction.UP:
            eye1 = (x * grid_size + 5, y * grid_size + 5)
            eye2 = (x * grid_size + 15, y * grid_size + 5)
        elif self.direction == Direction.DOWN:
            eye1 = (x * grid_size + 5, y * grid_size + 15)
            eye2 = (x * grid_size + 15, y * grid_size + 15)
        elif self.direction == Direction.LEFT:
            eye1 = (x * grid_size + 5, y * grid_size + 5)
            eye2 = (x * grid_size + 5, y * grid_size + 15)
        else:  # RIGHT
            eye1 = (x * grid_size + 15, y * grid_size + 5)
            eye2 = (x * grid_size + 15, y * grid_size + 15)

        pygame.draw.circle(self.screen, Colors.BLACK, eye1, eye_size)
        pygame.draw.circle(self.screen, Colors.BLACK, eye2, eye_size)

    def draw_food(self):
        """绘制普通食物"""
        x, y = self.food
        grid_size = self.config.GRID_SIZE

        # 脉冲效果
        pulse_size = int(2 + math.sin(self.food_pulse) * 2)
        food_rect = pygame.Rect(x * grid_size + pulse_size, y * grid_size + pulse_size,
                               grid_size - pulse_size * 2, grid_size - pulse_size * 2)

        pygame.draw.ellipse(self.screen, Colors.RED, food_rect)
        pygame.draw.ellipse(self.screen, Colors.YELLOW, food_rect, 2)

    def draw_special_food(self):
        """绘制特殊食物"""
        if not self.special_food:
            return

        x, y = self.special_food
        rect = pygame.Rect(x * self.config.GRID_SIZE, y * self.config.GRID_SIZE, self.config.GRID_SIZE, self.config.GRID_SIZE)

        # 彩虹效果
        color_index = int(self.food_pulse * 2) % len(Colors.RAINBOW_COLORS)
        pygame.draw.ellipse(self.screen, Colors.RAINBOW_COLORS[color_index], rect)
        pygame.draw.ellipse(self.screen, Colors.WHITE, rect, 3)

        # 闪烁星星效果
        grid_size = self.config.GRID_SIZE
        center_x = x * grid_size + grid_size // 2
        center_y = y * grid_size + grid_size // 2
        star_points = []
        for i in range(8):
            angle = i * math.pi / 4
            if i % 2 == 0:
                radius = 8
            else:
                radius = 4
            point_x = center_x + radius * math.cos(angle)
            point_y = center_y + radius * math.sin(angle)
            star_points.append((point_x, point_y))

        if len(star_points) >= 3:
            pygame.draw.polygon(self.screen, Colors.WHITE, star_points)

    def draw_ui(self):
        """绘制用户界面"""
        # 分数显示
        score_text = self.fonts['medium'].render(f"分数: {self.score}", True, Colors.WHITE)
        self.screen.blit(score_text, (10, 10))

        # 高分显示
        high_score = self.high_score_manager.get_high_score()
        high_score_text = self.fonts['small'].render(f"最高分: {high_score}", True, Colors.YELLOW)
        self.screen.blit(high_score_text, (10, 35))

        # 速度显示
        speed_text = self.fonts['small'].render(f"速度: {self.speed:.1f}", True, Colors.WHITE)
        self.screen.blit(speed_text, (10, 60))

        # 长度显示
        length_text = self.fonts['small'].render(f"长度: {len(self.snake)}", True, Colors.WHITE)
        self.screen.blit(length_text, (10, 85))

        # 控制提示
        controls_text = self.fonts['small'].render("控制: ↑↓←→ 或 WASD | 空格:暂停 | M:音效", True, Colors.GRAY)
        self.screen.blit(controls_text, (10, self.config.WINDOW_HEIGHT - 25))

        # 音效状态
        sound_status = "开" if self.sound_manager.sound_enabled else "关"
        sound_text = self.fonts['small'].render(f"音效: {sound_status}", True, Colors.WHITE)
        self.screen.blit(sound_text, (10, 110))

        if self.paused:
            pause_text = self.font.render("游戏暂停 - 按空格继续", True, Colors.YELLOW)
            text_rect = pause_text.get_rect(center=(self.config.WINDOW_WIDTH // 2, self.config.WINDOW_HEIGHT // 2))
            pygame.draw.rect(self.screen, Colors.BLACK, text_rect.inflate(20, 10))
            self.screen.blit(pause_text, text_rect)

        if self.game_over:
            # 游戏结束界面
            game_over_text = self.fonts['large'].render("游戏结束!", True, Colors.RED)
            restart_text = self.fonts['small'].render("空格/R键重新开始 | ESC退出", True, Colors.WHITE)

            # 显示最终分数
            final_score_text = self.fonts['medium'].render(f"最终分数: {self.score}", True, Colors.WHITE)

            # 新纪录提示
            if hasattr(self, 'is_new_record') and self.is_new_record:
                new_record_text = self.fonts['medium'].render("🎉 新纪录! 🎉", True, Colors.YELLOW)
            else:
                new_record_text = None

            # 计算位置
            center_x = self.config.WINDOW_WIDTH // 2
            center_y = self.config.WINDOW_HEIGHT // 2

            game_over_rect = game_over_text.get_rect(center=(center_x, center_y - 80))
            final_score_rect = final_score_text.get_rect(center=(center_x, center_y - 40))
            restart_rect = restart_text.get_rect(center=(center_x, center_y))

            # 绘制半透明背景
            overlay = pygame.Surface((self.config.WINDOW_WIDTH, self.config.WINDOW_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill(Colors.BLACK)
            self.screen.blit(overlay, (0, 0))

            # 绘制文本背景
            pygame.draw.rect(self.screen, Colors.BLACK, game_over_rect.inflate(40, 20))
            pygame.draw.rect(self.screen, Colors.BLACK, final_score_rect.inflate(40, 20))
            pygame.draw.rect(self.screen, Colors.BLACK, restart_rect.inflate(40, 20))

            # 绘制文本
            self.screen.blit(game_over_text, game_over_rect)
            self.screen.blit(final_score_text, final_score_rect)
            self.screen.blit(restart_text, restart_rect)

            if new_record_text:
                new_record_rect = new_record_text.get_rect(center=(center_x, center_y + 40))
                pygame.draw.rect(self.screen, Colors.BLACK, new_record_rect.inflate(40, 20))
                self.screen.blit(new_record_text, new_record_rect)

    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(Colors.BLACK)
        self.draw_grid()
        self.draw_snake()
        self.draw_food()
        self.draw_special_food()
        self.draw_ui()
        pygame.display.flip()

    def run(self):
        """运行游戏主循环"""
        running = True
        try:
            while running:
                running = self.handle_events()
                self.update()
                self.draw()
                self.clock.tick(int(self.speed))
        except Exception as e:
            print(f"游戏运行时发生错误: {e}")
        finally:
            pygame.quit()
            sys.exit()

def main():
    """主函数"""
    try:
        config = GameConfig()
        game = SnakeGame(config)
        game.run()
    except Exception as e:
        print(f"游戏初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
