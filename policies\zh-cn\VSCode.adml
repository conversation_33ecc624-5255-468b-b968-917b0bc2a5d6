<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
	<displayName />
	<description />
	<resources>
		<stringTable>
			<string id="Application">Visual Studio Code</string>
			<string id="Supported_1_101">Visual Studio Code &gt;= 1.101</string>
			<string id="Supported_1_67">Visual Studio Code &gt;= 1.67</string>
			<string id="Supported_1_96">Visual Studio Code &gt;= 1.96</string>
			<string id="Supported_1_99">Visual Studio Code &gt;= 1.99</string>
			<string id="Category_extensionsConfigurationTitle">扩展</string>
			<string id="Category_interactiveSessionConfigurationTitle">聊天</string>
			<string id="Category_updateConfigurationTitle">更新</string>
			<string id="Category_telemetryConfigurationTitle">遥测</string>
			<string id="ExtensionGalleryServiceUrl">ExtensionGalleryServiceUrl</string>
			<string id="ExtensionGalleryServiceUrl_extensions_gallery_serviceUrl">配置要连接到的市场服务 URL</string>
			<string id="ChatToolsAutoApprove">ChatToolsAutoApprove</string>
			<string id="ChatToolsAutoApprove_chat_tools_autoApprove_description">控制是否应自动批准工具使用。允许所有工具在未经用户确认的情况下自动运行，从而覆盖任何特定于工具的设置(例如终端自动批准)。谨慎使用: 仔细查看所选工具，并特别注意可能的提示注入源!</string>
			<string id="ChatMCP">ChatMCP</string>
			<string id="ChatMCP_chat_mcp_enabled">启用与模型上下文协议服务器的集成，以提供其他工具和功能。</string>
			<string id="ChatAgentExtensionTools">ChatAgentExtensionTools</string>
			<string id="ChatAgentExtensionTools_chat_extensionToolsPolicy">使用由第三方扩展提供的工具启用。</string>
			<string id="ChatAgentMode">ChatAgentMode</string>
			<string id="ChatAgentMode_chat_agent_enabled_description">为 {0} 启用代理模式。启用此模式后，可以通过视图中的下拉列表激活代理模式。</string>
			<string id="McpGalleryServiceUrl">McpGalleryServiceUrl</string>
			<string id="McpGalleryServiceUrl_mcp_gallery_serviceUrl">配置 MCP 库服务 URL 以连接到以下位置:</string>
			<string id="ChatPromptFiles">ChatPromptFiles</string>
			<string id="ChatPromptFiles_chat_promptFiles_policy">在 Chat、Edits 和内联聊天会话中启用可重用提示和指令文件。</string>
			<string id="UpdateMode">UpdateMode</string>
			<string id="UpdateMode_updateMode">配置是否接收自动更新。更改后需要重新启动。更新是从微软在线服务获取的。</string>
			<string id="UpdateMode_none">禁用更新。</string>
			<string id="UpdateMode_manual">禁用自动后台更新检查。如果手动检查更新，更新将可用。</string>
			<string id="UpdateMode_start">仅在启动时检查更新。禁用自动后台更新检查。</string>
			<string id="UpdateMode_default">启用自动更新检查。代码将定期自动检查更新。</string>
			<string id="TelemetryLevel">TelemetryLevel</string>
			<string id="TelemetryLevel_telemetry_telemetryLevel_policyDescription">控制遥测级别。</string>
			<string id="EnableFeedback">EnableFeedback</string>
			<string id="EnableFeedback_telemetry_feedback_enabled">在 Copilot Chat 等功能中启用反馈机制，例如问题报告器、调查和反馈选项。</string>
			<string id="AllowedExtensions">AllowedExtensions</string>
			<string id="AllowedExtensions_extensions_allowed_policy">指定允许使用的扩展列表。这有助于通过限制使用未经授权的扩展来维护安全且一致的开发环境。详细信息: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
		</stringTable>
		<presentationTable>
			<presentation id="ExtensionGalleryServiceUrl"><textBox refId="ExtensionGalleryServiceUrl"><label>ExtensionGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatToolsAutoApprove"><checkBox refId="ChatToolsAutoApprove">ChatToolsAutoApprove</checkBox></presentation>
			<presentation id="ChatMCP"><checkBox refId="ChatMCP">ChatMCP</checkBox></presentation>
			<presentation id="ChatAgentExtensionTools"><checkBox refId="ChatAgentExtensionTools">ChatAgentExtensionTools</checkBox></presentation>
			<presentation id="ChatAgentMode"><checkBox refId="ChatAgentMode">ChatAgentMode</checkBox></presentation>
			<presentation id="McpGalleryServiceUrl"><textBox refId="McpGalleryServiceUrl"><label>McpGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatPromptFiles"><checkBox refId="ChatPromptFiles">ChatPromptFiles</checkBox></presentation>
			<presentation id="UpdateMode"><dropdownList refId="UpdateMode" /></presentation>
			<presentation id="TelemetryLevel"><textBox refId="TelemetryLevel"><label>TelemetryLevel:</label></textBox></presentation>
			<presentation id="EnableFeedback"><checkBox refId="EnableFeedback">EnableFeedback</checkBox></presentation>
			<presentation id="AllowedExtensions"><multiTextBox refId="AllowedExtensions" /></presentation>
		</presentationTable>
	</resources>
</policyDefinitionResources>
