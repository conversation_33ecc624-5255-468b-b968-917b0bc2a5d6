export const id=698;export const ids=[698];export const modules={6773:e=>{const t=function(){};t.prototype=Object.create(null);const r=/; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu,n=/\\([\v\u0020-\u00ff])/gu,s=/^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u,o={type:"",parameters:new t};Object.freeze(o.parameters),Object.freeze(o),e.exports.xL=function(e){if("string"!=typeof e)return o;let a=e.indexOf(";");const c=-1!==a?e.slice(0,a).trim():e.trim();if(!1===s.test(c))return o;const i={type:c.toLowerCase(),parameters:new t};if(-1===a)return i;let u,p,l;for(r.lastIndex=a;p=r.exec(e);){if(p.index!==a)return o;a+=p[0].length,u=p[1].toLowerCase(),l=p[2],'"'===l[0]&&(l=l.slice(1,l.length-1),n.test(l)&&(l=l.replace(n,"$1"))),i.parameters[u]=l}return a!==e.length?o:i}},3698:(e,t,r)=>{r.d(t,{E:()=>q});var n=r(5407),s=`octokit-endpoint.js/0.0.0-development ${(0,n.$)()}`;function o(e,t){const r=Object.assign({},e);return Object.keys(t).forEach((n=>{!function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;const r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.call(r)===Function.prototype.call(e)}(t[n])?Object.assign(r,{[n]:t[n]}):n in e?r[n]=o(e[n],t[n]):Object.assign(r,{[n]:t[n]})})),r}function a(e){for(const t in e)void 0===e[t]&&delete e[t];return e}function c(e,t,r){if("string"==typeof t){let[e,n]=t.split(" ");r=Object.assign(n?{method:e,url:n}:{url:e},r)}else r=Object.assign({},t);var n;r.headers=(n=r.headers)?Object.keys(n).reduce(((e,t)=>(e[t.toLowerCase()]=n[t],e)),{}):{},a(r),a(r.headers);const s=o(e||{},r);return"/graphql"===r.url&&(e&&e.mediaType.previews?.length&&(s.mediaType.previews=e.mediaType.previews.filter((e=>!s.mediaType.previews.includes(e))).concat(s.mediaType.previews)),s.mediaType.previews=(s.mediaType.previews||[]).map((e=>e.replace(/-preview/,"")))),s}var i=/\{[^{}}]+\}/g;function u(e){return e.replace(/(?:^\W+)|(?:(?<!\W)\W+$)/g,"").split(/,/)}function p(e,t){const r={__proto__:null};for(const n of Object.keys(e))-1===t.indexOf(n)&&(r[n]=e[n]);return r}function l(e){return e.split(/(%[0-9A-Fa-f]{2})/g).map((function(e){return/%[0-9A-Fa-f]/.test(e)||(e=encodeURI(e).replace(/%5B/g,"[").replace(/%5D/g,"]")),e})).join("")}function f(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function d(e,t,r){return t="+"===e||"#"===e?l(t):f(t),r?f(r)+"="+t:t}function h(e){return null!=e}function y(e){return";"===e||"&"===e||"?"===e}function m(e,t){var r=["+","#",".","/",";","?","&"];return e=e.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g,(function(e,n,s){if(n){let e="";const s=[];if(-1!==r.indexOf(n.charAt(0))&&(e=n.charAt(0),n=n.substr(1)),n.split(/,/g).forEach((function(r){var n=/([^:\*]*)(?::(\d+)|(\*))?/.exec(r);s.push(function(e,t,r,n){var s=e[r],o=[];if(h(s)&&""!==s)if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)s=s.toString(),n&&"*"!==n&&(s=s.substring(0,parseInt(n,10))),o.push(d(t,s,y(t)?r:""));else if("*"===n)Array.isArray(s)?s.filter(h).forEach((function(e){o.push(d(t,e,y(t)?r:""))})):Object.keys(s).forEach((function(e){h(s[e])&&o.push(d(t,s[e],e))}));else{const e=[];Array.isArray(s)?s.filter(h).forEach((function(r){e.push(d(t,r))})):Object.keys(s).forEach((function(r){h(s[r])&&(e.push(f(r)),e.push(d(t,s[r].toString())))})),y(t)?o.push(f(r)+"="+e.join(",")):0!==e.length&&o.push(e.join(","))}else";"===t?h(s)&&o.push(f(r)):""!==s||"&"!==t&&"?"!==t?""===s&&o.push(""):o.push(f(r)+"=");return o}(t,e,n[1],n[2]||n[3]))})),e&&"+"!==e){var o=",";return"?"===e?o="&":"#"!==e&&(o=e),(0!==s.length?e:"")+s.join(o)}return s.join(",")}return l(s)})),"/"===e?e:e.replace(/\/$/,"")}function g(e){let t,r=e.method.toUpperCase(),n=(e.url||"/").replace(/:([a-z]\w+)/g,"{$1}"),s=Object.assign({},e.headers),o=p(e,["method","baseUrl","url","headers","request","mediaType"]);const a=function(e){const t=e.match(i);return t?t.map(u).reduce(((e,t)=>e.concat(t)),[]):[]}(n);var c;n=(c=n,{expand:m.bind(null,c)}).expand(o),/^http/.test(n)||(n=e.baseUrl+n);const l=p(o,Object.keys(e).filter((e=>a.includes(e))).concat("baseUrl"));if(!/application\/octet-stream/i.test(s.accept)&&(e.mediaType.format&&(s.accept=s.accept.split(/,/).map((t=>t.replace(/application\/vnd(\.\w+)(\.v3)?(\.\w+)?(\+json)?$/,`application/vnd$1$2.${e.mediaType.format}`))).join(",")),n.endsWith("/graphql")&&e.mediaType.previews?.length)){const t=s.accept.match(/(?<![\w-])[\w-]+(?=-preview)/g)||[];s.accept=t.concat(e.mediaType.previews).map((t=>`application/vnd.github.${t}-preview${e.mediaType.format?`.${e.mediaType.format}`:"+json"}`)).join(",")}return["GET","HEAD"].includes(r)?n=function(e,t){const r=/\?/.test(e)?"&":"?",n=Object.keys(t);return 0===n.length?e:e+r+n.map((e=>"q"===e?"q="+t.q.split("+").map(encodeURIComponent).join("+"):`${e}=${encodeURIComponent(t[e])}`)).join("&")}(n,l):"data"in l?t=l.data:Object.keys(l).length&&(t=l),s["content-type"]||void 0===t||(s["content-type"]="application/json; charset=utf-8"),["PATCH","PUT"].includes(r)&&void 0===t&&(t=""),Object.assign({method:r,url:n,headers:s},void 0!==t?{body:t}:null,e.request?{request:e.request}:null)}function b(e,t,r){return g(c(e,t,r))}var j=function e(t,r){const n=c(t,r),s=b.bind(null,n);return Object.assign(s,{DEFAULTS:n,defaults:e.bind(null,n),merge:c.bind(null,n),parse:g})}(null,{method:"GET",baseUrl:"https://api.github.com",headers:{accept:"application/vnd.github.v3+json","user-agent":s},mediaType:{format:""}}),w=r(6773);class v extends Error{name;status;request;response;constructor(e,t,r){super(e),this.name="HttpError",this.status=Number.parseInt(t),Number.isNaN(this.status)&&(this.status=0),"response"in r&&(this.response=r.response);const n=Object.assign({},r.request);r.request.headers.authorization&&(n.headers=Object.assign({},r.request.headers,{authorization:r.request.headers.authorization.replace(/(?<! ) .*$/," [REDACTED]")})),n.url=n.url.replace(/\bclient_secret=\w+/g,"client_secret=[REDACTED]").replace(/\baccess_token=\w+/g,"access_token=[REDACTED]"),this.request=n}}async function O(e){const t=e.request?.fetch||globalThis.fetch;if(!t)throw new Error("fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing");const r=e.request?.log||console,n=!1!==e.request?.parseSuccessResponseBody,s=function(e){if("object"!=typeof e||null===e)return!1;if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);if(null===t)return!0;const r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.call(r)===Function.prototype.call(e)}(e.body)||Array.isArray(e.body)?JSON.stringify(e.body):e.body,o=Object.fromEntries(Object.entries(e.headers).map((([e,t])=>[e,String(t)])));let a;try{a=await t(e.url,{method:e.method,body:s,redirect:e.request?.redirect,headers:o,signal:e.request?.signal,...e.body&&{duplex:"half"}})}catch(t){let r="Unknown Error";if(t instanceof Error){if("AbortError"===t.name)throw t.status=500,t;r=t.message,"TypeError"===t.name&&"cause"in t&&(t.cause instanceof Error?r=t.cause.message:"string"==typeof t.cause&&(r=t.cause))}const n=new v(r,500,{request:e});throw n.cause=t,n}const c=a.status,i=a.url,u={};for(const[e,t]of a.headers)u[e]=t;const p={url:i,status:c,headers:u,data:""};if("deprecation"in u){const t=u.link&&u.link.match(/<([^<>]+)>; rel="deprecation"/),n=t&&t.pop();r.warn(`[@octokit/request] "${e.method} ${e.url}" is deprecated. It is scheduled to be removed on ${u.sunset}${n?`. See ${n}`:""}`)}if(204===c||205===c)return p;if("HEAD"===e.method){if(c<400)return p;throw new v(a.statusText,c,{response:p,request:e})}if(304===c)throw p.data=await $(a),new v("Not modified",c,{response:p,request:e});if(c>=400)throw p.data=await $(a),new v(function(e){if("string"==typeof e)return e;if(e instanceof ArrayBuffer)return"Unknown error";if("message"in e){const t="documentation_url"in e?` - ${e.documentation_url}`:"";return Array.isArray(e.errors)?`${e.message}: ${e.errors.map((e=>JSON.stringify(e))).join(", ")}${t}`:`${e.message}${t}`}return`Unknown error: ${JSON.stringify(e)}`}(p.data),c,{response:p,request:e});return p.data=n?await $(a):a.body,p}async function $(e){const t=e.headers.get("content-type");if(!t)return e.text().catch((()=>""));const r=(0,w.xL)(t);if(!function(e){return"application/json"===e.type||"application/scim+json"===e.type}(r))return r.type.startsWith("text/")||"utf-8"===r.parameters.charset?.toLowerCase()?e.text().catch((()=>"")):e.arrayBuffer().catch((()=>new ArrayBuffer(0)));{let t="";try{return t=await e.text(),JSON.parse(t)}catch(e){return t}}}var q=function e(t,r){const n=t.defaults(r);return Object.assign((function(t,r){const s=n.merge(t,r);if(!s.request||!s.request.hook)return O(n.parse(s));const o=(e,t)=>O(n.parse(n.merge(e,t)));return Object.assign(o,{endpoint:n,defaults:e.bind(null,n)}),s.request.hook(o,s)}),{endpoint:n,defaults:e.bind(null,n)})}(j,{headers:{"user-agent":`octokit-request.js/0.0.0-development ${(0,n.$)()}`}})},5407:(e,t,r)=>{function n(){return"object"==typeof navigator&&"userAgent"in navigator?navigator.userAgent:"object"==typeof process&&void 0!==process.version?`Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`:"<environment undetectable>"}r.d(t,{$:()=>n})}};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/extensions/github/dist/698.js.map