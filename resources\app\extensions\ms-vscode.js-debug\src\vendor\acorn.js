(function(g,Q){typeof exports=="object"&&typeof module<"u"?Q(exports):typeof define=="function"&&define.amd?define(["exports"],Q):(g=typeof globalThis<"u"?globalThis:g||self,Q(g.acorn={}))})(exports,function(g){"use strict";var Q=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,81,2,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,9,5351,0,7,14,13835,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,983,6,110,6,6,9,4759,9,787719,239],we=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,4026,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,757,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],pt="\u200C\u200D\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0898-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",Ee="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",pe={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},fe="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",ft={5:fe,"5module":fe+" export import",6:fe+" const class extends export import super"},dt=/^in(stanceof)?$/,xt=new RegExp("["+Ee+"]"),mt=new RegExp("["+Ee+pt+"]");function de(e,t){for(var i=65536,s=0;s<t.length;s+=2){if(i+=t[s],i>e)return!1;if(i+=t[s+1],i>=e)return!0}return!1}function V(e,t){return e<65?e===36:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&xt.test(String.fromCharCode(e)):t===!1?!1:de(e,we)}function M(e,t){return e<48?e===36:e<58?!0:e<65?!1:e<91?!0:e<97?e===95:e<123?!0:e<=65535?e>=170&&mt.test(String.fromCharCode(e)):t===!1?!1:de(e,we)||de(e,Q)}var v=function(t,i){i===void 0&&(i={}),this.label=t,this.keyword=i.keyword,this.beforeExpr=!!i.beforeExpr,this.startsExpr=!!i.startsExpr,this.isLoop=!!i.isLoop,this.isAssign=!!i.isAssign,this.prefix=!!i.prefix,this.postfix=!!i.postfix,this.binop=i.binop||null,this.updateContext=null};function w(e,t){return new v(e,{beforeExpr:!0,binop:t})}var E={beforeExpr:!0},k={startsExpr:!0},se={};function m(e,t){return t===void 0&&(t={}),t.keyword=e,se[e]=new v(e,t)}var a={num:new v("num",k),regexp:new v("regexp",k),string:new v("string",k),name:new v("name",k),privateId:new v("privateId",k),eof:new v("eof"),bracketL:new v("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new v("]"),braceL:new v("{",{beforeExpr:!0,startsExpr:!0}),braceR:new v("}"),parenL:new v("(",{beforeExpr:!0,startsExpr:!0}),parenR:new v(")"),comma:new v(",",E),semi:new v(";",E),colon:new v(":",E),dot:new v("."),question:new v("?",E),questionDot:new v("?."),arrow:new v("=>",E),template:new v("template"),invalidTemplate:new v("invalidTemplate"),ellipsis:new v("...",E),backQuote:new v("`",k),dollarBraceL:new v("${",{beforeExpr:!0,startsExpr:!0}),eq:new v("=",{beforeExpr:!0,isAssign:!0}),assign:new v("_=",{beforeExpr:!0,isAssign:!0}),incDec:new v("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new v("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:w("||",1),logicalAND:w("&&",2),bitwiseOR:w("|",3),bitwiseXOR:w("^",4),bitwiseAND:w("&",5),equality:w("==/!=/===/!==",6),relational:w("</>/<=/>=",7),bitShift:w("<</>>/>>>",8),plusMin:new v("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:w("%",10),star:w("*",10),slash:w("/",10),starstar:new v("**",{beforeExpr:!0}),coalesce:w("??",1),_break:m("break"),_case:m("case",E),_catch:m("catch"),_continue:m("continue"),_debugger:m("debugger"),_default:m("default",E),_do:m("do",{isLoop:!0,beforeExpr:!0}),_else:m("else",E),_finally:m("finally"),_for:m("for",{isLoop:!0}),_function:m("function",k),_if:m("if"),_return:m("return",E),_switch:m("switch"),_throw:m("throw",E),_try:m("try"),_var:m("var"),_const:m("const"),_while:m("while",{isLoop:!0}),_with:m("with"),_new:m("new",{beforeExpr:!0,startsExpr:!0}),_this:m("this",k),_super:m("super",k),_class:m("class",k),_extends:m("extends",E),_export:m("export"),_import:m("import",k),_null:m("null",k),_true:m("true",k),_false:m("false",k),_in:m("in",{beforeExpr:!0,binop:7}),_instanceof:m("instanceof",{beforeExpr:!0,binop:7}),_typeof:m("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:m("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:m("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},S=/\r\n?|\n|\u2028|\u2029/,Ae=new RegExp(S.source,"g");function U(e){return e===10||e===13||e===8232||e===8233}function Ie(e,t,i){i===void 0&&(i=e.length);for(var s=t;s<i;s++){var r=e.charCodeAt(s);if(U(r))return s<i-1&&r===13&&e.charCodeAt(s+1)===10?s+2:s+1}return-1}var xe=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,A=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Pe=Object.prototype,vt=Pe.hasOwnProperty,gt=Pe.toString,Y=Object.hasOwn||function(e,t){return vt.call(e,t)},Ne=Array.isArray||function(e){return gt.call(e)==="[object Array]"},Ve=Object.create(null);function B(e){return Ve[e]||(Ve[e]=new RegExp("^(?:"+e.replace(/ /g,"|")+")$"))}function F(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode((e>>10)+55296,(e&1023)+56320))}var bt=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,j=function(t,i){this.line=t,this.column=i};j.prototype.offset=function(t){return new j(this.line,this.column+t)};var X=function(t,i,s){this.start=i,this.end=s,t.sourceFile!==null&&(this.source=t.sourceFile)};function me(e,t){for(var i=1,s=0;;){var r=Ie(e,s,t);if(r<0)return new j(i,t-s);++i,s=r}}var ae={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,checkPrivateFields:!0,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},Te=!1;function yt(e){var t={};for(var i in ae)t[i]=e&&Y(e,i)?e[i]:ae[i];if(t.ecmaVersion==="latest"?t.ecmaVersion=1e8:t.ecmaVersion==null?(!Te&&typeof console=="object"&&console.warn&&(Te=!0,console.warn(`Since Acorn 8.0.0, options.ecmaVersion is required.
Defaulting to 2020, but this will stop working in the future.`)),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),t.allowReserved==null&&(t.allowReserved=t.ecmaVersion<5),(!e||e.allowHashBang==null)&&(t.allowHashBang=t.ecmaVersion>=14),Ne(t.onToken)){var s=t.onToken;t.onToken=function(r){return s.push(r)}}return Ne(t.onComment)&&(t.onComment=Ct(t,t.onComment)),t}function Ct(e,t){return function(i,s,r,n,u,o){var h={type:i?"Block":"Line",value:s,start:r,end:n};e.locations&&(h.loc=new X(this,u,o)),e.ranges&&(h.range=[r,n]),t.push(h)}}var Z=1,G=2,ve=4,Le=8,Re=16,Oe=32,ge=64,Be=128,J=256,be=Z|G|J;function ye(e,t){return G|(e?ve:0)|(t?Le:0)}var re=0,Ce=1,R=2,Fe=3,De=4,Me=5,y=function(t,i,s){this.options=t=yt(t),this.sourceFile=t.sourceFile,this.keywords=B(ft[t.ecmaVersion>=6?6:t.sourceType==="module"?"5module":5]);var r="";t.allowReserved!==!0&&(r=pe[t.ecmaVersion>=6?6:t.ecmaVersion===5?5:3],t.sourceType==="module"&&(r+=" await")),this.reservedWords=B(r);var n=(r?r+" ":"")+pe.strict;this.reservedWordsStrict=B(n),this.reservedWordsStrictBind=B(n+" "+pe.strictBind),this.input=String(i),this.containsEsc=!1,s?(this.pos=s,this.lineStart=this.input.lastIndexOf(`
`,s-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(S).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=a.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule=t.sourceType==="module",this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),this.pos===0&&t.allowHashBang&&this.input.slice(0,2)==="#!"&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(Z),this.regexpState=null,this.privateNameStack=[]},T={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};y.prototype.parse=function(){var t=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(t)},T.inFunction.get=function(){return(this.currentVarScope().flags&G)>0},T.inGenerator.get=function(){return(this.currentVarScope().flags&Le)>0&&!this.currentVarScope().inClassFieldInit},T.inAsync.get=function(){return(this.currentVarScope().flags&ve)>0&&!this.currentVarScope().inClassFieldInit},T.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&J)return!1;if(t.flags&G)return(t.flags&ve)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},T.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(t&ge)>0||i||this.options.allowSuperOutsideMethod},T.allowDirectSuper.get=function(){return(this.currentThisScope().flags&Be)>0},T.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},T.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(t&(G|J))>0||i},T.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&J)>0},y.extend=function(){for(var t=[],i=arguments.length;i--;)t[i]=arguments[i];for(var s=this,r=0;r<t.length;r++)s=t[r](s);return s},y.parse=function(t,i){return new this(i,t).parse()},y.parseExpressionAt=function(t,i,s){var r=new this(s,t,i);return r.nextToken(),r.parseExpression()},y.tokenizer=function(t,i){return new this(i,t)},Object.defineProperties(y.prototype,T);var _=y.prototype,_t=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;_.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){A.lastIndex=e,e+=A.exec(this.input)[0].length;var t=_t.exec(this.input.slice(e));if(!t)return!1;if((t[1]||t[2])==="use strict"){A.lastIndex=e+t[0].length;var i=A.exec(this.input),s=i.index+i[0].length,r=this.input.charAt(s);return r===";"||r==="}"||S.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(r)||r==="!"&&this.input.charAt(s+1)==="=")}e+=t[0].length,A.lastIndex=e,e+=A.exec(this.input)[0].length,this.input[e]===";"&&e++}},_.eat=function(e){return this.type===e?(this.next(),!0):!1},_.isContextual=function(e){return this.type===a.name&&this.value===e&&!this.containsEsc},_.eatContextual=function(e){return this.isContextual(e)?(this.next(),!0):!1},_.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},_.canInsertSemicolon=function(){return this.type===a.eof||this.type===a.braceR||S.test(this.input.slice(this.lastTokEnd,this.start))},_.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},_.semicolon=function(){!this.eat(a.semi)&&!this.insertSemicolon()&&this.unexpected()},_.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},_.expect=function(e){this.eat(e)||this.unexpected()},_.unexpected=function(e){this.raise(e??this.start,"Unexpected token")};var ne=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};_.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var i=t?e.parenthesizedAssign:e.parenthesizedBind;i>-1&&this.raiseRecoverable(i,t?"Assigning to rvalue":"Parenthesized pattern")}},_.checkExpressionErrors=function(e,t){if(!e)return!1;var i=e.shorthandAssign,s=e.doubleProto;if(!t)return i>=0||s>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},_.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},_.isSimpleAssignTarget=function(e){return e.type==="ParenthesizedExpression"?this.isSimpleAssignTarget(e.expression):e.type==="Identifier"||e.type==="MemberExpression"};var l=y.prototype;l.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==a.eof;){var i=this.parseStatement(null,!0,t);e.body.push(i)}if(this.inModule)for(var s=0,r=Object.keys(this.undefinedExports);s<r.length;s+=1){var n=r[s];this.raiseRecoverable(this.undefinedExports[n].start,"Export '"+n+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var _e={kind:"loop"},kt={kind:"switch"};l.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;A.lastIndex=this.pos;var t=A.exec(this.input),i=this.pos+t[0].length,s=this.input.charCodeAt(i);if(s===91||s===92)return!0;if(e)return!1;if(s===123||s>55295&&s<56320)return!0;if(V(s,!0)){for(var r=i+1;M(s=this.input.charCodeAt(r),!0);)++r;if(s===92||s>55295&&s<56320)return!0;var n=this.input.slice(i,r);if(!dt.test(n))return!0}return!1},l.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;A.lastIndex=this.pos;var e=A.exec(this.input),t=this.pos+e[0].length,i;return!S.test(this.input.slice(this.pos,t))&&this.input.slice(t,t+8)==="function"&&(t+8===this.input.length||!(M(i=this.input.charCodeAt(t+8))||i>55295&&i<56320))},l.parseStatement=function(e,t,i){var s=this.type,r=this.startNode(),n;switch(this.isLet(e)&&(s=a._var,n="let"),s){case a._break:case a._continue:return this.parseBreakContinueStatement(r,s.keyword);case a._debugger:return this.parseDebuggerStatement(r);case a._do:return this.parseDoStatement(r);case a._for:return this.parseForStatement(r);case a._function:return e&&(this.strict||e!=="if"&&e!=="label")&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(r,!1,!e);case a._class:return e&&this.unexpected(),this.parseClass(r,!0);case a._if:return this.parseIfStatement(r);case a._return:return this.parseReturnStatement(r);case a._switch:return this.parseSwitchStatement(r);case a._throw:return this.parseThrowStatement(r);case a._try:return this.parseTryStatement(r);case a._const:case a._var:return n=n||this.value,e&&n!=="var"&&this.unexpected(),this.parseVarStatement(r,n);case a._while:return this.parseWhileStatement(r);case a._with:return this.parseWithStatement(r);case a.braceL:return this.parseBlock(!0,r);case a.semi:return this.parseEmptyStatement(r);case a._export:case a._import:if(this.options.ecmaVersion>10&&s===a._import){A.lastIndex=this.pos;var u=A.exec(this.input),o=this.pos+u[0].length,h=this.input.charCodeAt(o);if(h===40||h===46)return this.parseExpressionStatement(r,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===a._import?this.parseImport(r):this.parseExport(r,i);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(r,!0,!e);var f=this.value,d=this.parseExpression();return s===a.name&&d.type==="Identifier"&&this.eat(a.colon)?this.parseLabeledStatement(r,f,d,e):this.parseExpressionStatement(r,d)}},l.parseBreakContinueStatement=function(e,t){var i=t==="break";this.next(),this.eat(a.semi)||this.insertSemicolon()?e.label=null:this.type!==a.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var r=this.labels[s];if((e.label==null||r.name===e.label.name)&&(r.kind!=null&&(i||r.kind==="loop")||e.label&&i))break}return s===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,i?"BreakStatement":"ContinueStatement")},l.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},l.parseDoStatement=function(e){return this.next(),this.labels.push(_e),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(a._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(a.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},l.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(_e),this.enterScope(0),this.expect(a.parenL),this.type===a.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var i=this.isLet();if(this.type===a._var||this.type===a._const||i){var s=this.startNode(),r=i?"let":this.value;return this.next(),this.parseVar(s,!0,r),this.finishNode(s,"VariableDeclaration"),(this.type===a._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&s.declarations.length===1?(this.options.ecmaVersion>=9&&(this.type===a._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,s)):(t>-1&&this.unexpected(t),this.parseFor(e,s))}var n=this.isContextual("let"),u=!1,o=new ne,h=this.parseExpression(t>-1?"await":!0,o);return this.type===a._in||(u=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===a._in?t>-1&&this.unexpected(t):e.await=t>-1),n&&u&&this.raise(h.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(h,!1,o),this.checkLValPattern(h),this.parseForIn(e,h)):(this.checkExpressionErrors(o,!0),t>-1&&this.unexpected(t),this.parseFor(e,h))},l.parseFunctionStatement=function(e,t,i){return this.next(),this.parseFunction(e,$|(i?0:ke),!1,t)},l.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(a._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},l.parseReturnStatement=function(e){return!this.inFunction&&!this.options.allowReturnOutsideFunction&&this.raise(this.start,"'return' outside of function"),this.next(),this.eat(a.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},l.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(a.braceL),this.labels.push(kt),this.enterScope(0);for(var t,i=!1;this.type!==a.braceR;)if(this.type===a._case||this.type===a._default){var s=this.type===a._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,t.test=null),this.expect(a.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},l.parseThrowStatement=function(e){return this.next(),S.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var St=[];l.parseCatchClauseParam=function(){var e=this.parseBindingAtom(),t=e.type==="Identifier";return this.enterScope(t?Oe:0),this.checkLValPattern(e,t?De:R),this.expect(a.parenR),e},l.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===a._catch){var t=this.startNode();this.next(),this.eat(a.parenL)?t.param=this.parseCatchClauseParam():(this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0)),t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(a._finally)?this.parseBlock():null,!e.handler&&!e.finalizer&&this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},l.parseVarStatement=function(e,t,i){return this.next(),this.parseVar(e,!1,t,i),this.semicolon(),this.finishNode(e,"VariableDeclaration")},l.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(_e),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},l.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},l.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},l.parseLabeledStatement=function(e,t,i,s){for(var r=0,n=this.labels;r<n.length;r+=1){var u=n[r];u.name===t&&this.raise(i.start,"Label '"+t+"' is already declared")}for(var o=this.type.isLoop?"loop":this.type===a._switch?"switch":null,h=this.labels.length-1;h>=0;h--){var f=this.labels[h];if(f.statementStart===e.start)f.statementStart=this.start,f.kind=o;else break}return this.labels.push({name:t,kind:o,statementStart:this.start}),e.body=this.parseStatement(s?s.indexOf("label")===-1?s+"label":s:"label"),this.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")},l.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},l.parseBlock=function(e,t,i){for(e===void 0&&(e=!0),t===void 0&&(t=this.startNode()),t.body=[],this.expect(a.braceL),e&&this.enterScope(0);this.type!==a.braceR;){var s=this.parseStatement(null);t.body.push(s)}return i&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},l.parseFor=function(e,t){return e.init=t,this.expect(a.semi),e.test=this.type===a.semi?null:this.parseExpression(),this.expect(a.semi),e.update=this.type===a.parenR?null:this.parseExpression(),this.expect(a.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},l.parseForIn=function(e,t){var i=this.type===a._in;return this.next(),t.type==="VariableDeclaration"&&t.declarations[0].init!=null&&(!i||this.options.ecmaVersion<8||this.strict||t.kind!=="var"||t.declarations[0].id.type!=="Identifier")&&this.raise(t.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(a.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")},l.parseVar=function(e,t,i,s){for(e.declarations=[],e.kind=i;;){var r=this.startNode();if(this.parseVarId(r,i),this.eat(a.eq)?r.init=this.parseMaybeAssign(t):!s&&i==="const"&&!(this.type===a._in||this.options.ecmaVersion>=6&&this.isContextual("of"))?this.unexpected():!s&&r.id.type!=="Identifier"&&!(t&&(this.type===a._in||this.isContextual("of")))?this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):r.init=null,e.declarations.push(this.finishNode(r,"VariableDeclarator")),!this.eat(a.comma))break}return e},l.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,t==="var"?Ce:R,!1)};var $=1,ke=2,Ue=4;l.parseFunction=function(e,t,i,s,r){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===a.star&&t&ke&&this.unexpected(),e.generator=this.eat(a.star)),this.options.ecmaVersion>=8&&(e.async=!!s),t&$&&(e.id=t&Ue&&this.type!==a.name?null:this.parseIdent(),e.id&&!(t&ke)&&this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?Ce:R:Fe));var n=this.yieldPos,u=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(ye(e.async,e.generator)),t&$||(e.id=this.type===a.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,i,!1,r),this.yieldPos=n,this.awaitPos=u,this.awaitIdentPos=o,this.finishNode(e,t&$?"FunctionDeclaration":"FunctionExpression")},l.parseFunctionParams=function(e){this.expect(a.parenL),e.params=this.parseBindingList(a.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},l.parseClass=function(e,t){this.next();var i=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var s=this.enterClassBody(),r=this.startNode(),n=!1;for(r.body=[],this.expect(a.braceL);this.type!==a.braceR;){var u=this.parseClassElement(e.superClass!==null);u&&(r.body.push(u),u.type==="MethodDefinition"&&u.kind==="constructor"?(n&&this.raiseRecoverable(u.start,"Duplicate constructor in the same class"),n=!0):u.key&&u.key.type==="PrivateIdentifier"&&wt(s,u)&&this.raiseRecoverable(u.key.start,"Identifier '#"+u.key.name+"' has already been declared"))}return this.strict=i,this.next(),e.body=this.finishNode(r,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},l.parseClassElement=function(e){if(this.eat(a.semi))return null;var t=this.options.ecmaVersion,i=this.startNode(),s="",r=!1,n=!1,u="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(a.braceL))return this.parseClassStaticBlock(i),i;this.isClassElementNameStart()||this.type===a.star?o=!0:s="static"}if(i.static=o,!s&&t>=8&&this.eatContextual("async")&&((this.isClassElementNameStart()||this.type===a.star)&&!this.canInsertSemicolon()?n=!0:s="async"),!s&&(t>=9||!n)&&this.eat(a.star)&&(r=!0),!s&&!n&&!r){var h=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?u=h:s=h)}if(s?(i.computed=!1,i.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),i.key.name=s,this.finishNode(i.key,"Identifier")):this.parseClassElementName(i),t<13||this.type===a.parenL||u!=="method"||r||n){var f=!i.static&&ue(i,"constructor"),d=f&&e;f&&u!=="method"&&this.raise(i.key.start,"Constructor can't have get/set modifier"),i.kind=f?"constructor":u,this.parseClassMethod(i,r,n,d)}else this.parseClassField(i);return i},l.isClassElementNameStart=function(){return this.type===a.name||this.type===a.privateId||this.type===a.num||this.type===a.string||this.type===a.bracketL||this.type.keyword},l.parseClassElementName=function(e){this.type===a.privateId?(this.value==="constructor"&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},l.parseClassMethod=function(e,t,i,s){var r=e.key;e.kind==="constructor"?(t&&this.raise(r.start,"Constructor can't be a generator"),i&&this.raise(r.start,"Constructor can't be an async method")):e.static&&ue(e,"prototype")&&this.raise(r.start,"Classes may not have a static property named prototype");var n=e.value=this.parseMethod(t,i,s);return e.kind==="get"&&n.params.length!==0&&this.raiseRecoverable(n.start,"getter should have no params"),e.kind==="set"&&n.params.length!==1&&this.raiseRecoverable(n.start,"setter should have exactly one param"),e.kind==="set"&&n.params[0].type==="RestElement"&&this.raiseRecoverable(n.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},l.parseClassField=function(e){if(ue(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&ue(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(a.eq)){var t=this.currentThisScope(),i=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=i}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},l.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(J|ge);this.type!==a.braceR;){var i=this.parseStatement(null);e.body.push(i)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},l.parseClassId=function(e,t){this.type===a.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,R,!1)):(t===!0&&this.unexpected(),e.id=null)},l.parseClassSuper=function(e){e.superClass=this.eat(a._extends)?this.parseExprSubscripts(null,!1):null},l.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},l.exitClassBody=function(){var e=this.privateNameStack.pop(),t=e.declared,i=e.used;if(this.options.checkPrivateFields)for(var s=this.privateNameStack.length,r=s===0?null:this.privateNameStack[s-1],n=0;n<i.length;++n){var u=i[n];Y(t,u.name)||(r?r.used.push(u):this.raiseRecoverable(u.start,"Private field '#"+u.name+"' must be declared in an enclosing class"))}};function wt(e,t){var i=t.key.name,s=e[i],r="true";return t.type==="MethodDefinition"&&(t.kind==="get"||t.kind==="set")&&(r=(t.static?"s":"i")+t.kind),s==="iget"&&r==="iset"||s==="iset"&&r==="iget"||s==="sget"&&r==="sset"||s==="sset"&&r==="sget"?(e[i]="true",!1):s?!0:(e[i]=r,!1)}function ue(e,t){var i=e.computed,s=e.key;return!i&&(s.type==="Identifier"&&s.name===t||s.type==="Literal"&&s.value===t)}l.parseExportAllDeclaration=function(e,t){return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==a.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration")},l.parseExport=function(e,t){if(this.next(),this.eat(a.star))return this.parseExportAllDeclaration(e,t);if(this.eat(a._default))return this.checkExport(t,"default",this.lastTokStart),e.declaration=this.parseExportDefaultDeclaration(),this.finishNode(e,"ExportDefaultDeclaration");if(this.shouldParseExportStatement())e.declaration=this.parseExportDeclaration(e),e.declaration.type==="VariableDeclaration"?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==a.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var i=0,s=e.specifiers;i<s.length;i+=1){var r=s[i];this.checkUnreserved(r.local),this.checkLocalExport(r.local),r.local.type==="Literal"&&this.raise(r.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},l.parseExportDeclaration=function(e){return this.parseStatement(null)},l.parseExportDefaultDeclaration=function(){var e;if(this.type===a._function||(e=this.isAsyncFunction())){var t=this.startNode();return this.next(),e&&this.next(),this.parseFunction(t,$|Ue,!1,e)}else if(this.type===a._class){var i=this.startNode();return this.parseClass(i,"nullableID")}else{var s=this.parseMaybeAssign();return this.semicolon(),s}},l.checkExport=function(e,t,i){e&&(typeof t!="string"&&(t=t.type==="Identifier"?t.name:t.value),Y(e,t)&&this.raiseRecoverable(i,"Duplicate export '"+t+"'"),e[t]=!0)},l.checkPatternExport=function(e,t){var i=t.type;if(i==="Identifier")this.checkExport(e,t,t.start);else if(i==="ObjectPattern")for(var s=0,r=t.properties;s<r.length;s+=1){var n=r[s];this.checkPatternExport(e,n)}else if(i==="ArrayPattern")for(var u=0,o=t.elements;u<o.length;u+=1){var h=o[u];h&&this.checkPatternExport(e,h)}else i==="Property"?this.checkPatternExport(e,t.value):i==="AssignmentPattern"?this.checkPatternExport(e,t.left):i==="RestElement"&&this.checkPatternExport(e,t.argument)},l.checkVariableExport=function(e,t){if(e)for(var i=0,s=t;i<s.length;i+=1){var r=s[i];this.checkPatternExport(e,r.id)}},l.shouldParseExportStatement=function(){return this.type.keyword==="var"||this.type.keyword==="const"||this.type.keyword==="class"||this.type.keyword==="function"||this.isLet()||this.isAsyncFunction()},l.parseExportSpecifier=function(e){var t=this.startNode();return t.local=this.parseModuleExportName(),t.exported=this.eatContextual("as")?this.parseModuleExportName():t.local,this.checkExport(e,t.exported,t.exported.start),this.finishNode(t,"ExportSpecifier")},l.parseExportSpecifiers=function(e){var t=[],i=!0;for(this.expect(a.braceL);!this.eat(a.braceR);){if(i)i=!1;else if(this.expect(a.comma),this.afterTrailingComma(a.braceR))break;t.push(this.parseExportSpecifier(e))}return t},l.parseImport=function(e){return this.next(),this.type===a.string?(e.specifiers=St,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===a.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},l.parseImportSpecifier=function(){var e=this.startNode();return e.imported=this.parseModuleExportName(),this.eatContextual("as")?e.local=this.parseIdent():(this.checkUnreserved(e.imported),e.local=e.imported),this.checkLValSimple(e.local,R),this.finishNode(e,"ImportSpecifier")},l.parseImportDefaultSpecifier=function(){var e=this.startNode();return e.local=this.parseIdent(),this.checkLValSimple(e.local,R),this.finishNode(e,"ImportDefaultSpecifier")},l.parseImportNamespaceSpecifier=function(){var e=this.startNode();return this.next(),this.expectContextual("as"),e.local=this.parseIdent(),this.checkLValSimple(e.local,R),this.finishNode(e,"ImportNamespaceSpecifier")},l.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===a.name&&(e.push(this.parseImportDefaultSpecifier()),!this.eat(a.comma)))return e;if(this.type===a.star)return e.push(this.parseImportNamespaceSpecifier()),e;for(this.expect(a.braceL);!this.eat(a.braceR);){if(t)t=!1;else if(this.expect(a.comma),this.afterTrailingComma(a.braceR))break;e.push(this.parseImportSpecifier())}return e},l.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===a.string){var e=this.parseLiteral(this.value);return bt.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},l.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},l.isDirectiveCandidate=function(e){return this.options.ecmaVersion>=5&&e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value=="string"&&(this.input[e.start]==='"'||this.input[e.start]==="'")};var I=y.prototype;I.toAssignable=function(e,t,i){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&e.name==="await"&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var s=0,r=e.properties;s<r.length;s+=1){var n=r[s];this.toAssignable(n,t),n.type==="RestElement"&&(n.argument.type==="ArrayPattern"||n.argument.type==="ObjectPattern")&&this.raise(n.argument.start,"Unexpected token")}break;case"Property":e.kind!=="init"&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),e.argument.type==="AssignmentPattern"&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":e.operator!=="="&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,i);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return e},I.toAssignableList=function(e,t){for(var i=e.length,s=0;s<i;s++){var r=e[s];r&&this.toAssignable(r,t)}if(i){var n=e[i-1];this.options.ecmaVersion===6&&t&&n&&n.type==="RestElement"&&n.argument.type!=="Identifier"&&this.unexpected(n.argument.start)}return e},I.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},I.parseRestBinding=function(){var e=this.startNode();return this.next(),this.options.ecmaVersion===6&&this.type!==a.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},I.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case a.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(a.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case a.braceL:return this.parseObj(!0)}return this.parseIdent()},I.parseBindingList=function(e,t,i,s){for(var r=[],n=!0;!this.eat(e);)if(n?n=!1:this.expect(a.comma),t&&this.type===a.comma)r.push(null);else{if(i&&this.afterTrailingComma(e))break;if(this.type===a.ellipsis){var u=this.parseRestBinding();this.parseBindingListItem(u),r.push(u),this.type===a.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}else r.push(this.parseAssignableListItem(s))}return r},I.parseAssignableListItem=function(e){var t=this.parseMaybeDefault(this.start,this.startLoc);return this.parseBindingListItem(t),t},I.parseBindingListItem=function(e){return e},I.parseMaybeDefault=function(e,t,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(a.eq))return i;var s=this.startNodeAt(e,t);return s.left=i,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},I.checkLValSimple=function(e,t,i){t===void 0&&(t=re);var s=t!==re;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode"),s&&(t===R&&e.name==="let"&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),i&&(Y(i,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),i[e.name]=!0),t!==Me&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":s&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return s&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,i);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}},I.checkLValPattern=function(e,t,i){switch(t===void 0&&(t=re),e.type){case"ObjectPattern":for(var s=0,r=e.properties;s<r.length;s+=1){var n=r[s];this.checkLValInnerPattern(n,t,i)}break;case"ArrayPattern":for(var u=0,o=e.elements;u<o.length;u+=1){var h=o[u];h&&this.checkLValInnerPattern(h,t,i)}break;default:this.checkLValSimple(e,t,i)}},I.checkLValInnerPattern=function(e,t,i){switch(t===void 0&&(t=re),e.type){case"Property":this.checkLValInnerPattern(e.value,t,i);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,i);break;case"RestElement":this.checkLValPattern(e.argument,t,i);break;default:this.checkLValPattern(e,t,i)}};var P=function(t,i,s,r,n){this.token=t,this.isExpr=!!i,this.preserveSpace=!!s,this.override=r,this.generator=!!n},b={b_stat:new P("{",!1),b_expr:new P("{",!0),b_tmpl:new P("${",!1),p_stat:new P("(",!1),p_expr:new P("(",!0),q_tmpl:new P("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new P("function",!1),f_expr:new P("function",!0),f_expr_gen:new P("function",!0,!1,null,!0),f_gen:new P("function",!1,!1,null,!0)},H=y.prototype;H.initialContext=function(){return[b.b_stat]},H.curContext=function(){return this.context[this.context.length-1]},H.braceIsBlock=function(e){var t=this.curContext();return t===b.f_expr||t===b.f_stat?!0:e===a.colon&&(t===b.b_stat||t===b.b_expr)?!t.isExpr:e===a._return||e===a.name&&this.exprAllowed?S.test(this.input.slice(this.lastTokEnd,this.start)):e===a._else||e===a.semi||e===a.eof||e===a.parenR||e===a.arrow?!0:e===a.braceL?t===b.b_stat:e===a._var||e===a._const||e===a.name?!1:!this.exprAllowed},H.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if(t.token==="function")return t.generator}return!1},H.updateContext=function(e){var t,i=this.type;i.keyword&&e===a.dot?this.exprAllowed=!1:(t=i.updateContext)?t.call(this,e):this.exprAllowed=i.beforeExpr},H.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},a.parenR.updateContext=a.braceR.updateContext=function(){if(this.context.length===1){this.exprAllowed=!0;return}var e=this.context.pop();e===b.b_stat&&this.curContext().token==="function"&&(e=this.context.pop()),this.exprAllowed=!e.isExpr},a.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?b.b_stat:b.b_expr),this.exprAllowed=!0},a.dollarBraceL.updateContext=function(){this.context.push(b.b_tmpl),this.exprAllowed=!0},a.parenL.updateContext=function(e){var t=e===a._if||e===a._for||e===a._with||e===a._while;this.context.push(t?b.p_stat:b.p_expr),this.exprAllowed=!0},a.incDec.updateContext=function(){},a._function.updateContext=a._class.updateContext=function(e){e.beforeExpr&&e!==a._else&&!(e===a.semi&&this.curContext()!==b.p_stat)&&!(e===a._return&&S.test(this.input.slice(this.lastTokEnd,this.start)))&&!((e===a.colon||e===a.braceL)&&this.curContext()===b.b_stat)?this.context.push(b.f_expr):this.context.push(b.f_stat),this.exprAllowed=!1},a.colon.updateContext=function(){this.curContext().token==="function"&&this.context.pop(),this.exprAllowed=!0},a.backQuote.updateContext=function(){this.curContext()===b.q_tmpl?this.context.pop():this.context.push(b.q_tmpl),this.exprAllowed=!1},a.star.updateContext=function(e){if(e===a._function){var t=this.context.length-1;this.context[t]===b.f_expr?this.context[t]=b.f_expr_gen:this.context[t]=b.f_gen}this.exprAllowed=!0},a.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==a.dot&&(this.value==="of"&&!this.exprAllowed||this.value==="yield"&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var p=y.prototype;p.checkPropClash=function(e,t,i){if(!(this.options.ecmaVersion>=9&&e.type==="SpreadElement")&&!(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var s=e.key,r;switch(s.type){case"Identifier":r=s.name;break;case"Literal":r=String(s.value);break;default:return}var n=e.kind;if(this.options.ecmaVersion>=6){r==="__proto__"&&n==="init"&&(t.proto&&(i?i.doubleProto<0&&(i.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);return}r="$"+r;var u=t[r];if(u){var o;n==="init"?o=this.strict&&u.init||u.get||u.set:o=u.init||u[n],o&&this.raiseRecoverable(s.start,"Redefinition of property")}else u=t[r]={init:!1,get:!1,set:!1};u[n]=!0}},p.parseExpression=function(e,t){var i=this.start,s=this.startLoc,r=this.parseMaybeAssign(e,t);if(this.type===a.comma){var n=this.startNodeAt(i,s);for(n.expressions=[r];this.eat(a.comma);)n.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(n,"SequenceExpression")}return r},p.parseMaybeAssign=function(e,t,i){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var s=!1,r=-1,n=-1,u=-1;t?(r=t.parenthesizedAssign,n=t.trailingComma,u=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new ne,s=!0);var o=this.start,h=this.startLoc;(this.type===a.parenL||this.type===a.name)&&(this.potentialArrowAt=this.start,this.potentialArrowInForAwait=e==="await");var f=this.parseMaybeConditional(e,t);if(i&&(f=i.call(this,f,o,h)),this.type.isAssign){var d=this.startNodeAt(o,h);return d.operator=this.value,this.type===a.eq&&(f=this.toAssignable(f,!1,t)),s||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=f.start&&(t.shorthandAssign=-1),this.type===a.eq?this.checkLValPattern(f):this.checkLValSimple(f),d.left=f,this.next(),d.right=this.parseMaybeAssign(e),u>-1&&(t.doubleProto=u),this.finishNode(d,"AssignmentExpression")}else s&&this.checkExpressionErrors(t,!0);return r>-1&&(t.parenthesizedAssign=r),n>-1&&(t.trailingComma=n),f},p.parseMaybeConditional=function(e,t){var i=this.start,s=this.startLoc,r=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return r;if(this.eat(a.question)){var n=this.startNodeAt(i,s);return n.test=r,n.consequent=this.parseMaybeAssign(),this.expect(a.colon),n.alternate=this.parseMaybeAssign(e),this.finishNode(n,"ConditionalExpression")}return r},p.parseExprOps=function(e,t){var i=this.start,s=this.startLoc,r=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||r.start===i&&r.type==="ArrowFunctionExpression"?r:this.parseExprOp(r,i,s,-1,e)},p.parseExprOp=function(e,t,i,s,r){var n=this.type.binop;if(n!=null&&(!r||this.type!==a._in)&&n>s){var u=this.type===a.logicalOR||this.type===a.logicalAND,o=this.type===a.coalesce;o&&(n=a.logicalAND.binop);var h=this.value;this.next();var f=this.start,d=this.startLoc,C=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,r),f,d,n,r),q=this.buildBinary(t,i,e,C,h,u||o);return(u&&this.type===a.coalesce||o&&(this.type===a.logicalOR||this.type===a.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(q,t,i,s,r)}return e},p.buildBinary=function(e,t,i,s,r,n){s.type==="PrivateIdentifier"&&this.raise(s.start,"Private identifier can only be left side of binary expression");var u=this.startNodeAt(e,t);return u.left=i,u.operator=r,u.right=s,this.finishNode(u,n?"LogicalExpression":"BinaryExpression")},p.parseMaybeUnary=function(e,t,i,s){var r=this.start,n=this.startLoc,u;if(this.isContextual("await")&&this.canAwait)u=this.parseAwait(s),t=!0;else if(this.type.prefix){var o=this.startNode(),h=this.type===a.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,h,s),this.checkExpressionErrors(e,!0),h?this.checkLValSimple(o.argument):this.strict&&o.operator==="delete"&&o.argument.type==="Identifier"?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):o.operator==="delete"&&qe(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,u=this.finishNode(o,h?"UpdateExpression":"UnaryExpression")}else if(!t&&this.type===a.privateId)(s||this.privateNameStack.length===0)&&this.options.checkPrivateFields&&this.unexpected(),u=this.parsePrivateIdent(),this.type!==a._in&&this.unexpected();else{if(u=this.parseExprSubscripts(e,s),this.checkExpressionErrors(e))return u;for(;this.type.postfix&&!this.canInsertSemicolon();){var f=this.startNodeAt(r,n);f.operator=this.value,f.prefix=!1,f.argument=u,this.checkLValSimple(u),this.next(),u=this.finishNode(f,"UpdateExpression")}}if(!i&&this.eat(a.starstar))if(t)this.unexpected(this.lastTokStart);else return this.buildBinary(r,n,u,this.parseMaybeUnary(null,!1,!1,s),"**",!1);else return u};function qe(e){return e.type==="MemberExpression"&&e.property.type==="PrivateIdentifier"||e.type==="ChainExpression"&&qe(e.expression)}p.parseExprSubscripts=function(e,t){var i=this.start,s=this.startLoc,r=this.parseExprAtom(e,t);if(r.type==="ArrowFunctionExpression"&&this.input.slice(this.lastTokStart,this.lastTokEnd)!==")")return r;var n=this.parseSubscripts(r,i,s,!1,t);return e&&n.type==="MemberExpression"&&(e.parenthesizedAssign>=n.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=n.start&&(e.parenthesizedBind=-1),e.trailingComma>=n.start&&(e.trailingComma=-1)),n},p.parseSubscripts=function(e,t,i,s,r){for(var n=this.options.ecmaVersion>=8&&e.type==="Identifier"&&e.name==="async"&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start,u=!1;;){var o=this.parseSubscript(e,t,i,s,n,u,r);if(o.optional&&(u=!0),o===e||o.type==="ArrowFunctionExpression"){if(u){var h=this.startNodeAt(t,i);h.expression=o,o=this.finishNode(h,"ChainExpression")}return o}e=o}},p.shouldParseAsyncArrow=function(){return!this.canInsertSemicolon()&&this.eat(a.arrow)},p.parseSubscriptAsyncArrow=function(e,t,i,s){return this.parseArrowExpression(this.startNodeAt(e,t),i,!0,s)},p.parseSubscript=function(e,t,i,s,r,n,u){var o=this.options.ecmaVersion>=11,h=o&&this.eat(a.questionDot);s&&h&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var f=this.eat(a.bracketL);if(f||h&&this.type!==a.parenL&&this.type!==a.backQuote||this.eat(a.dot)){var d=this.startNodeAt(t,i);d.object=e,f?(d.property=this.parseExpression(),this.expect(a.bracketR)):this.type===a.privateId&&e.type!=="Super"?d.property=this.parsePrivateIdent():d.property=this.parseIdent(this.options.allowReserved!=="never"),d.computed=!!f,o&&(d.optional=h),e=this.finishNode(d,"MemberExpression")}else if(!s&&this.eat(a.parenL)){var C=new ne,q=this.yieldPos,ie=this.awaitPos,W=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var le=this.parseExprList(a.parenR,this.options.ecmaVersion>=8,!1,C);if(r&&!h&&this.shouldParseAsyncArrow())return this.checkPatternErrors(C,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=q,this.awaitPos=ie,this.awaitIdentPos=W,this.parseSubscriptAsyncArrow(t,i,le,u);this.checkExpressionErrors(C,!0),this.yieldPos=q||this.yieldPos,this.awaitPos=ie||this.awaitPos,this.awaitIdentPos=W||this.awaitIdentPos;var z=this.startNodeAt(t,i);z.callee=e,z.arguments=le,o&&(z.optional=h),e=this.finishNode(z,"CallExpression")}else if(this.type===a.backQuote){(h||n)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var K=this.startNodeAt(t,i);K.tag=e,K.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(K,"TaggedTemplateExpression")}return e},p.parseExprAtom=function(e,t,i){this.type===a.slash&&this.readRegexp();var s,r=this.potentialArrowAt===this.start;switch(this.type){case a._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),s=this.startNode(),this.next(),this.type===a.parenL&&!this.allowDirectSuper&&this.raise(s.start,"super() call outside constructor of a subclass"),this.type!==a.dot&&this.type!==a.bracketL&&this.type!==a.parenL&&this.unexpected(),this.finishNode(s,"Super");case a._this:return s=this.startNode(),this.next(),this.finishNode(s,"ThisExpression");case a.name:var n=this.start,u=this.startLoc,o=this.containsEsc,h=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!o&&h.name==="async"&&!this.canInsertSemicolon()&&this.eat(a._function))return this.overrideContext(b.f_expr),this.parseFunction(this.startNodeAt(n,u),0,!1,!0,t);if(r&&!this.canInsertSemicolon()){if(this.eat(a.arrow))return this.parseArrowExpression(this.startNodeAt(n,u),[h],!1,t);if(this.options.ecmaVersion>=8&&h.name==="async"&&this.type===a.name&&!o&&(!this.potentialArrowInForAwait||this.value!=="of"||this.containsEsc))return h=this.parseIdent(!1),(this.canInsertSemicolon()||!this.eat(a.arrow))&&this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,u),[h],!0,t)}return h;case a.regexp:var f=this.value;return s=this.parseLiteral(f.value),s.regex={pattern:f.pattern,flags:f.flags},s;case a.num:case a.string:return this.parseLiteral(this.value);case a._null:case a._true:case a._false:return s=this.startNode(),s.value=this.type===a._null?null:this.type===a._true,s.raw=this.type.keyword,this.next(),this.finishNode(s,"Literal");case a.parenL:var d=this.start,C=this.parseParenAndDistinguishExpression(r,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(C)&&(e.parenthesizedAssign=d),e.parenthesizedBind<0&&(e.parenthesizedBind=d)),C;case a.bracketL:return s=this.startNode(),this.next(),s.elements=this.parseExprList(a.bracketR,!0,!0,e),this.finishNode(s,"ArrayExpression");case a.braceL:return this.overrideContext(b.b_expr),this.parseObj(!1,e);case a._function:return s=this.startNode(),this.next(),this.parseFunction(s,0);case a._class:return this.parseClass(this.startNode(),!1);case a._new:return this.parseNew();case a.backQuote:return this.parseTemplate();case a._import:return this.options.ecmaVersion>=11?this.parseExprImport(i):this.unexpected();default:return this.parseExprAtomDefault()}},p.parseExprAtomDefault=function(){this.unexpected()},p.parseExprImport=function(e){var t=this.startNode();if(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import"),this.next(),this.type===a.parenL&&!e)return this.parseDynamicImport(t);if(this.type===a.dot){var i=this.startNodeAt(t.start,t.loc&&t.loc.start);return i.name="import",t.meta=this.finishNode(i,"Identifier"),this.parseImportMeta(t)}else this.unexpected()},p.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(a.parenR)){var t=this.start;this.eat(a.comma)&&this.eat(a.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},p.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="meta"&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),this.options.sourceType!=="module"&&!this.options.allowImportExportEverywhere&&this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},p.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),t.raw.charCodeAt(t.raw.length-1)===110&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},p.parseParenExpression=function(){this.expect(a.parenL);var e=this.parseExpression();return this.expect(a.parenR),e},p.shouldParseArrow=function(e){return!this.canInsertSemicolon()},p.parseParenAndDistinguishExpression=function(e,t){var i=this.start,s=this.startLoc,r,n=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var u=this.start,o=this.startLoc,h=[],f=!0,d=!1,C=new ne,q=this.yieldPos,ie=this.awaitPos,W;for(this.yieldPos=0,this.awaitPos=0;this.type!==a.parenR;)if(f?f=!1:this.expect(a.comma),n&&this.afterTrailingComma(a.parenR,!0)){d=!0;break}else if(this.type===a.ellipsis){W=this.start,h.push(this.parseParenItem(this.parseRestBinding())),this.type===a.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element");break}else h.push(this.parseMaybeAssign(!1,C,this.parseParenItem));var le=this.lastTokEnd,z=this.lastTokEndLoc;if(this.expect(a.parenR),e&&this.shouldParseArrow(h)&&this.eat(a.arrow))return this.checkPatternErrors(C,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=q,this.awaitPos=ie,this.parseParenArrowList(i,s,h,t);(!h.length||d)&&this.unexpected(this.lastTokStart),W&&this.unexpected(W),this.checkExpressionErrors(C,!0),this.yieldPos=q||this.yieldPos,this.awaitPos=ie||this.awaitPos,h.length>1?(r=this.startNodeAt(u,o),r.expressions=h,this.finishNodeAt(r,"SequenceExpression",le,z)):r=h[0]}else r=this.parseParenExpression();if(this.options.preserveParens){var K=this.startNodeAt(i,s);return K.expression=r,this.finishNode(K,"ParenthesizedExpression")}else return r},p.parseParenItem=function(e){return e},p.parseParenArrowList=function(e,t,i,s){return this.parseArrowExpression(this.startNodeAt(e,t),i,!1,s)};var Et=[];p.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode();if(this.next(),this.options.ecmaVersion>=6&&this.type===a.dot){var t=this.startNodeAt(e.start,e.loc&&e.loc.start);t.name="new",e.meta=this.finishNode(t,"Identifier"),this.next();var i=this.containsEsc;return e.property=this.parseIdent(!0),e.property.name!=="target"&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var s=this.start,r=this.startLoc;return e.callee=this.parseSubscripts(this.parseExprAtom(null,!1,!0),s,r,!0,!1),this.eat(a.parenL)?e.arguments=this.parseExprList(a.parenR,this.options.ecmaVersion>=8,!1):e.arguments=Et,this.finishNode(e,"NewExpression")},p.parseTemplateElement=function(e){var t=e.isTagged,i=this.startNode();return this.type===a.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value,cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,`
`),cooked:this.value},this.next(),i.tail=this.type===a.backQuote,this.finishNode(i,"TemplateElement")},p.parseTemplate=function(e){e===void 0&&(e={});var t=e.isTagged;t===void 0&&(t=!1);var i=this.startNode();this.next(),i.expressions=[];var s=this.parseTemplateElement({isTagged:t});for(i.quasis=[s];!s.tail;)this.type===a.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(a.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(a.braceR),i.quasis.push(s=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(i,"TemplateLiteral")},p.isAsyncProp=function(e){return!e.computed&&e.key.type==="Identifier"&&e.key.name==="async"&&(this.type===a.name||this.type===a.num||this.type===a.string||this.type===a.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===a.star)&&!S.test(this.input.slice(this.lastTokEnd,this.start))},p.parseObj=function(e,t){var i=this.startNode(),s=!0,r={};for(i.properties=[],this.next();!this.eat(a.braceR);){if(s)s=!1;else if(this.expect(a.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(a.braceR))break;var n=this.parseProperty(e,t);e||this.checkPropClash(n,r,t),i.properties.push(n)}return this.finishNode(i,e?"ObjectPattern":"ObjectExpression")},p.parseProperty=function(e,t){var i=this.startNode(),s,r,n,u;if(this.options.ecmaVersion>=9&&this.eat(a.ellipsis))return e?(i.argument=this.parseIdent(!1),this.type===a.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.finishNode(i,"RestElement")):(i.argument=this.parseMaybeAssign(!1,t),this.type===a.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(i,"SpreadElement"));this.options.ecmaVersion>=6&&(i.method=!1,i.shorthand=!1,(e||t)&&(n=this.start,u=this.startLoc),e||(s=this.eat(a.star)));var o=this.containsEsc;return this.parsePropertyName(i),!e&&!o&&this.options.ecmaVersion>=8&&!s&&this.isAsyncProp(i)?(r=!0,s=this.options.ecmaVersion>=9&&this.eat(a.star),this.parsePropertyName(i)):r=!1,this.parsePropertyValue(i,e,s,r,n,u,t,o),this.finishNode(i,"Property")},p.parseGetterSetter=function(e){e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var t=e.kind==="get"?0:1;if(e.value.params.length!==t){var i=e.value.start;e.kind==="get"?this.raiseRecoverable(i,"getter should have no params"):this.raiseRecoverable(i,"setter should have exactly one param")}else e.kind==="set"&&e.value.params[0].type==="RestElement"&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")},p.parsePropertyValue=function(e,t,i,s,r,n,u,o){(i||s)&&this.type===a.colon&&this.unexpected(),this.eat(a.colon)?(e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,u),e.kind="init"):this.options.ecmaVersion>=6&&this.type===a.parenL?(t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(i,s)):!t&&!o&&this.options.ecmaVersion>=5&&!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&this.type!==a.comma&&this.type!==a.braceR&&this.type!==a.eq?((i||s)&&this.unexpected(),this.parseGetterSetter(e)):this.options.ecmaVersion>=6&&!e.computed&&e.key.type==="Identifier"?((i||s)&&this.unexpected(),this.checkUnreserved(e.key),e.key.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=r),e.kind="init",t?e.value=this.parseMaybeDefault(r,n,this.copyNode(e.key)):this.type===a.eq&&u?(u.shorthandAssign<0&&(u.shorthandAssign=this.start),e.value=this.parseMaybeDefault(r,n,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected()},p.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(a.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(a.bracketR),e.key;e.computed=!1}return e.key=this.type===a.num||this.type===a.string?this.parseExprAtom():this.parseIdent(this.options.allowReserved!=="never")},p.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},p.parseMethod=function(e,t,i){var s=this.startNode(),r=this.yieldPos,n=this.awaitPos,u=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=e),this.options.ecmaVersion>=8&&(s.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(ye(t,s.generator)|ge|(i?Be:0)),this.expect(a.parenL),s.params=this.parseBindingList(a.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0,!1),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=u,this.finishNode(s,"FunctionExpression")},p.parseArrowExpression=function(e,t,i,s){var r=this.yieldPos,n=this.awaitPos,u=this.awaitIdentPos;return this.enterScope(ye(i,!1)|Re),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,s),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=u,this.finishNode(e,"ArrowFunctionExpression")},p.parseFunctionBody=function(e,t,i,s){var r=t&&this.type!==a.braceL,n=this.strict,u=!1;if(r)e.body=this.parseMaybeAssign(s),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);(!n||o)&&(u=this.strictDirective(this.end),u&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list"));var h=this.labels;this.labels=[],u&&(this.strict=!0),this.checkParams(e,!n&&!u&&!t&&!i&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,Me),e.body=this.parseBlock(!1,void 0,u&&!n),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=h}this.exitScope()},p.isSimpleParamList=function(e){for(var t=0,i=e;t<i.length;t+=1){var s=i[t];if(s.type!=="Identifier")return!1}return!0},p.checkParams=function(e,t){for(var i=Object.create(null),s=0,r=e.params;s<r.length;s+=1){var n=r[s];this.checkLValInnerPattern(n,Ce,t?null:i)}},p.parseExprList=function(e,t,i,s){for(var r=[],n=!0;!this.eat(e);){if(n)n=!1;else if(this.expect(a.comma),t&&this.afterTrailingComma(e))break;var u=void 0;i&&this.type===a.comma?u=null:this.type===a.ellipsis?(u=this.parseSpread(s),s&&this.type===a.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):u=this.parseMaybeAssign(!1,s),r.push(u)}return r},p.checkUnreserved=function(e){var t=e.start,i=e.end,s=e.name;if(this.inGenerator&&s==="yield"&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&s==="arguments"&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),this.inClassStaticBlock&&(s==="arguments"||s==="await")&&this.raise(t,"Cannot use "+s+" in class static initialization block"),this.keywords.test(s)&&this.raise(t,"Unexpected keyword '"+s+"'"),!(this.options.ecmaVersion<6&&this.input.slice(t,i).indexOf("\\")!==-1)){var r=this.strict?this.reservedWordsStrict:this.reservedWords;r.test(s)&&(!this.inAsync&&s==="await"&&this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+s+"' is reserved"))}},p.parseIdent=function(e){var t=this.parseIdentNode();return this.next(!!e),this.finishNode(t,"Identifier"),e||(this.checkUnreserved(t),t.name==="await"&&!this.awaitIdentPos&&(this.awaitIdentPos=t.start)),t},p.parseIdentNode=function(){var e=this.startNode();return this.type===a.name?e.name=this.value:this.type.keyword?(e.name=this.type.keyword,(e.name==="class"||e.name==="function")&&(this.lastTokEnd!==this.lastTokStart+1||this.input.charCodeAt(this.lastTokStart)!==46)&&this.context.pop(),this.type=a.name):this.unexpected(),e},p.parsePrivateIdent=function(){var e=this.startNode();return this.type===a.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),this.options.checkPrivateFields&&(this.privateNameStack.length===0?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e)),e},p.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===a.semi||this.canInsertSemicolon()||this.type!==a.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(a.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},p.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var oe=y.prototype;oe.raise=function(e,t){var i=me(this.input,e);t+=" ("+i.line+":"+i.column+")";var s=new SyntaxError(t);throw s.pos=e,s.loc=i,s.raisedAt=this.pos,s},oe.raiseRecoverable=oe.raise,oe.curPosition=function(){if(this.options.locations)return new j(this.curLine,this.pos-this.lineStart)};var D=y.prototype,At=function(t){this.flags=t,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};D.enterScope=function(e){this.scopeStack.push(new At(e))},D.exitScope=function(){this.scopeStack.pop()},D.treatFunctionsAsVarInScope=function(e){return e.flags&G||!this.inModule&&e.flags&Z},D.declareName=function(e,t,i){var s=!1;if(t===R){var r=this.currentScope();s=r.lexical.indexOf(e)>-1||r.functions.indexOf(e)>-1||r.var.indexOf(e)>-1,r.lexical.push(e),this.inModule&&r.flags&Z&&delete this.undefinedExports[e]}else if(t===De){var n=this.currentScope();n.lexical.push(e)}else if(t===Fe){var u=this.currentScope();this.treatFunctionsAsVar?s=u.lexical.indexOf(e)>-1:s=u.lexical.indexOf(e)>-1||u.var.indexOf(e)>-1,u.functions.push(e)}else for(var o=this.scopeStack.length-1;o>=0;--o){var h=this.scopeStack[o];if(h.lexical.indexOf(e)>-1&&!(h.flags&Oe&&h.lexical[0]===e)||!this.treatFunctionsAsVarInScope(h)&&h.functions.indexOf(e)>-1){s=!0;break}if(h.var.push(e),this.inModule&&h.flags&Z&&delete this.undefinedExports[e],h.flags&be)break}s&&this.raiseRecoverable(i,"Identifier '"+e+"' has already been declared")},D.checkLocalExport=function(e){this.scopeStack[0].lexical.indexOf(e.name)===-1&&this.scopeStack[0].var.indexOf(e.name)===-1&&(this.undefinedExports[e.name]=e)},D.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},D.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&be)return t}},D.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&be&&!(t.flags&Re))return t}};var ee=function(t,i,s){this.type="",this.start=i,this.end=0,t.options.locations&&(this.loc=new X(t,s)),t.options.directSourceFile&&(this.sourceFile=t.options.directSourceFile),t.options.ranges&&(this.range=[i,0])},te=y.prototype;te.startNode=function(){return new ee(this,this.start,this.startLoc)},te.startNodeAt=function(e,t){return new ee(this,e,t)};function je(e,t,i,s){return e.type=t,e.end=i,this.options.locations&&(e.loc.end=s),this.options.ranges&&(e.range[1]=i),e}te.finishNode=function(e,t){return je.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},te.finishNodeAt=function(e,t,i,s){return je.call(this,e,t,i,s)},te.copyNode=function(e){var t=new ee(this,e.start,this.startLoc);for(var i in e)t[i]=e[i];return t};var Ge="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",He=Ge+" Extended_Pictographic",We=He,ze=We+" EBase EComp EMod EPres ExtPict",Ke=ze,It=Ke,Pt={9:Ge,10:He,11:We,12:ze,13:Ke,14:It},Nt="Basic_Emoji Emoji_Keycap_Sequence RGI_Emoji_Modifier_Sequence RGI_Emoji_Flag_Sequence RGI_Emoji_Tag_Sequence RGI_Emoji_ZWJ_Sequence RGI_Emoji",Vt={9:"",10:"",11:"",12:"",13:"",14:Nt},Qe="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",Ye="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",Xe=Ye+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Ze=Xe+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Je=Ze+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",$e=Je+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith",Tt=$e+" Hrkt Katakana_Or_Hiragana Kawi Nag_Mundari Nagm Unknown Zzzz",Lt={9:Ye,10:Xe,11:Ze,12:Je,13:$e,14:Tt},et={};function Rt(e){var t=et[e]={binary:B(Pt[e]+" "+Qe),binaryOfStrings:B(Vt[e]),nonBinary:{General_Category:B(Qe),Script:B(Lt[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var Se=0,tt=[9,10,11,12,13,14];Se<tt.length;Se+=1){var Ot=tt[Se];Rt(Ot)}var c=y.prototype,L=function(t){this.parser=t,this.validFlags="gim"+(t.options.ecmaVersion>=6?"uy":"")+(t.options.ecmaVersion>=9?"s":"")+(t.options.ecmaVersion>=13?"d":"")+(t.options.ecmaVersion>=15?"v":""),this.unicodeProperties=et[t.options.ecmaVersion>=14?14:t.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchV=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};L.prototype.reset=function(t,i,s){var r=s.indexOf("v")!==-1,n=s.indexOf("u")!==-1;this.start=t|0,this.source=i+"",this.flags=s,r&&this.parser.options.ecmaVersion>=15?(this.switchU=!0,this.switchV=!0,this.switchN=!0):(this.switchU=n&&this.parser.options.ecmaVersion>=6,this.switchV=!1,this.switchN=n&&this.parser.options.ecmaVersion>=9)},L.prototype.raise=function(t){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+t)},L.prototype.at=function(t,i){i===void 0&&(i=!1);var s=this.source,r=s.length;if(t>=r)return-1;var n=s.charCodeAt(t);if(!(i||this.switchU)||n<=55295||n>=57344||t+1>=r)return n;var u=s.charCodeAt(t+1);return u>=56320&&u<=57343?(n<<10)+u-56613888:n},L.prototype.nextIndex=function(t,i){i===void 0&&(i=!1);var s=this.source,r=s.length;if(t>=r)return r;var n=s.charCodeAt(t),u;return!(i||this.switchU)||n<=55295||n>=57344||t+1>=r||(u=s.charCodeAt(t+1))<56320||u>57343?t+1:t+2},L.prototype.current=function(t){return t===void 0&&(t=!1),this.at(this.pos,t)},L.prototype.lookahead=function(t){return t===void 0&&(t=!1),this.at(this.nextIndex(this.pos,t),t)},L.prototype.advance=function(t){t===void 0&&(t=!1),this.pos=this.nextIndex(this.pos,t)},L.prototype.eat=function(t,i){return i===void 0&&(i=!1),this.current(i)===t?(this.advance(i),!0):!1},L.prototype.eatChars=function(t,i){i===void 0&&(i=!1);for(var s=this.pos,r=0,n=t;r<n.length;r+=1){var u=n[r],o=this.at(s,i);if(o===-1||o!==u)return!1;s=this.nextIndex(s,i)}return this.pos=s,!0},c.validateRegExpFlags=function(e){for(var t=e.validFlags,i=e.flags,s=!1,r=!1,n=0;n<i.length;n++){var u=i.charAt(n);t.indexOf(u)===-1&&this.raise(e.start,"Invalid regular expression flag"),i.indexOf(u,n+1)>-1&&this.raise(e.start,"Duplicate regular expression flag"),u==="u"&&(s=!0),u==="v"&&(r=!0)}this.options.ecmaVersion>=15&&s&&r&&this.raise(e.start,"Invalid regular expression flag")},c.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},c.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,i=e.backReferenceNames;t<i.length;t+=1){var s=i[t];e.groupNames.indexOf(s)===-1&&e.raise("Invalid named capture referenced")}},c.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},c.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},c.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))?(this.regexp_eatQuantifier(e),!0):!1},c.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!i,!0}return e.pos=t,!1},c.regexp_eatQuantifier=function(e,t){return t===void 0&&(t=!1),this.regexp_eatQuantifierPrefix(e,t)?(e.eat(63),!0):!1},c.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},c.regexp_eatBracedQuantifier=function(e,t){var i=e.pos;if(e.eat(123)){var s=0,r=-1;if(this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(r=e.lastIntValue),e.eat(125)))return r!==-1&&r<s&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=i}return!1},c.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},c.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},c.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},c.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):e.current()===63&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},c.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},c.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},c.regexp_eatSyntaxCharacter=function(e){var t=e.current();return it(t)?(e.lastIntValue=t,e.advance(),!0):!1};function it(e){return e===36||e>=40&&e<=43||e===46||e===63||e>=91&&e<=94||e>=123&&e<=125}c.regexp_eatPatternCharacters=function(e){for(var t=e.pos,i=0;(i=e.current())!==-1&&!it(i);)e.advance();return e.pos!==t},c.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return t!==-1&&t!==36&&!(t>=40&&t<=43)&&t!==46&&t!==63&&t!==91&&t!==94&&t!==124?(e.advance(),!0):!1},c.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e)){e.groupNames.indexOf(e.lastStringValue)!==-1&&e.raise("Duplicate capture group name"),e.groupNames.push(e.lastStringValue);return}e.raise("Invalid group")}},c.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},c.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=F(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=F(e.lastIntValue);return!0}return!1},c.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return e.advance(i),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),Bt(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function Bt(e){return V(e,!0)||e===36||e===95}c.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return e.advance(i),s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),Ft(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)};function Ft(e){return M(e,!0)||e===36||e===95||e===8204||e===8205}c.regexp_eatAtomEscape=function(e){return this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e)?!0:(e.switchU&&(e.current()===99&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},c.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var i=e.lastIntValue;if(e.switchU)return i>e.maxBackReference&&(e.maxBackReference=i),!0;if(i<=e.numCapturingParens)return!0;e.pos=t}return!1},c.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},c.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},c.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},c.regexp_eatZero=function(e){return e.current()===48&&!he(e.lookahead())?(e.lastIntValue=0,e.advance(),!0):!1},c.regexp_eatControlEscape=function(e){var t=e.current();return t===116?(e.lastIntValue=9,e.advance(),!0):t===110?(e.lastIntValue=10,e.advance(),!0):t===118?(e.lastIntValue=11,e.advance(),!0):t===102?(e.lastIntValue=12,e.advance(),!0):t===114?(e.lastIntValue=13,e.advance(),!0):!1},c.regexp_eatControlLetter=function(e){var t=e.current();return st(t)?(e.lastIntValue=t%32,e.advance(),!0):!1};function st(e){return e>=65&&e<=90||e>=97&&e<=122}c.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){t===void 0&&(t=!1);var i=e.pos,s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var r=e.lastIntValue;if(s&&r>=55296&&r<=56319){var n=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var u=e.lastIntValue;if(u>=56320&&u<=57343)return e.lastIntValue=(r-55296)*1024+(u-56320)+65536,!0}e.pos=n,e.lastIntValue=r}return!0}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&Dt(e.lastIntValue))return!0;s&&e.raise("Invalid unicode escape"),e.pos=i}return!1};function Dt(e){return e>=0&&e<=1114111}c.regexp_eatIdentityEscape=function(e){if(e.switchU)return this.regexp_eatSyntaxCharacter(e)?!0:e.eat(47)?(e.lastIntValue=47,!0):!1;var t=e.current();return t!==99&&(!e.switchN||t!==107)?(e.lastIntValue=t,e.advance(),!0):!1},c.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do e.lastIntValue=10*e.lastIntValue+(t-48),e.advance();while((t=e.current())>=48&&t<=57);return!0}return!1};var at=0,O=1,N=2;c.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(Mt(t))return e.lastIntValue=-1,e.advance(),O;var i=!1;if(e.switchU&&this.options.ecmaVersion>=9&&((i=t===80)||t===112)){e.lastIntValue=-1,e.advance();var s;if(e.eat(123)&&(s=this.regexp_eatUnicodePropertyValueExpression(e))&&e.eat(125))return i&&s===N&&e.raise("Invalid property name"),s;e.raise("Invalid property name")}return at};function Mt(e){return e===100||e===68||e===115||e===83||e===119||e===87}c.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var i=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,i,s),O}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var r=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,r)}return at},c.regexp_validateUnicodePropertyNameAndValue=function(e,t,i){Y(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(i)||e.raise("Invalid property value")},c.regexp_validateUnicodePropertyNameOrValue=function(e,t){if(e.unicodeProperties.binary.test(t))return O;if(e.switchV&&e.unicodeProperties.binaryOfStrings.test(t))return N;e.raise("Invalid property name")},c.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";rt(t=e.current());)e.lastStringValue+=F(t),e.advance();return e.lastStringValue!==""};function rt(e){return st(e)||e===95}c.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Ut(t=e.current());)e.lastStringValue+=F(t),e.advance();return e.lastStringValue!==""};function Ut(e){return rt(e)||he(e)}c.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},c.regexp_eatCharacterClass=function(e){if(e.eat(91)){var t=e.eat(94),i=this.regexp_classContents(e);return e.eat(93)||e.raise("Unterminated character class"),t&&i===N&&e.raise("Negated character class may contain strings"),!0}return!1},c.regexp_classContents=function(e){return e.current()===93?O:e.switchV?this.regexp_classSetExpression(e):(this.regexp_nonEmptyClassRanges(e),O)},c.regexp_nonEmptyClassRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var i=e.lastIntValue;e.switchU&&(t===-1||i===-1)&&e.raise("Invalid character class"),t!==-1&&i!==-1&&t>i&&e.raise("Range out of order in character class")}}},c.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var i=e.current();(i===99||ot(i))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var s=e.current();return s!==93?(e.lastIntValue=s,e.advance(),!0):!1},c.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},c.regexp_classSetExpression=function(e){var t=O,i;if(!this.regexp_eatClassSetRange(e))if(i=this.regexp_eatClassSetOperand(e)){i===N&&(t=N);for(var s=e.pos;e.eatChars([38,38]);){if(e.current()!==38&&(i=this.regexp_eatClassSetOperand(e))){i!==N&&(t=O);continue}e.raise("Invalid character in character class")}if(s!==e.pos)return t;for(;e.eatChars([45,45]);)this.regexp_eatClassSetOperand(e)||e.raise("Invalid character in character class");if(s!==e.pos)return t}else e.raise("Invalid character in character class");for(;;)if(!this.regexp_eatClassSetRange(e)){if(i=this.regexp_eatClassSetOperand(e),!i)return t;i===N&&(t=N)}},c.regexp_eatClassSetRange=function(e){var t=e.pos;if(this.regexp_eatClassSetCharacter(e)){var i=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassSetCharacter(e)){var s=e.lastIntValue;return i!==-1&&s!==-1&&i>s&&e.raise("Range out of order in character class"),!0}e.pos=t}return!1},c.regexp_eatClassSetOperand=function(e){return this.regexp_eatClassSetCharacter(e)?O:this.regexp_eatClassStringDisjunction(e)||this.regexp_eatNestedClass(e)},c.regexp_eatNestedClass=function(e){var t=e.pos;if(e.eat(91)){var i=e.eat(94),s=this.regexp_classContents(e);if(e.eat(93))return i&&s===N&&e.raise("Negated character class may contain strings"),s;e.pos=t}if(e.eat(92)){var r=this.regexp_eatCharacterClassEscape(e);if(r)return r;e.pos=t}return null},c.regexp_eatClassStringDisjunction=function(e){var t=e.pos;if(e.eatChars([92,113])){if(e.eat(123)){var i=this.regexp_classStringDisjunctionContents(e);if(e.eat(125))return i}else e.raise("Invalid escape");e.pos=t}return null},c.regexp_classStringDisjunctionContents=function(e){for(var t=this.regexp_classString(e);e.eat(124);)this.regexp_classString(e)===N&&(t=N);return t},c.regexp_classString=function(e){for(var t=0;this.regexp_eatClassSetCharacter(e);)t++;return t===1?O:N},c.regexp_eatClassSetCharacter=function(e){var t=e.pos;if(e.eat(92))return this.regexp_eatCharacterEscape(e)||this.regexp_eatClassSetReservedPunctuator(e)?!0:e.eat(98)?(e.lastIntValue=8,!0):(e.pos=t,!1);var i=e.current();return i<0||i===e.lookahead()&&qt(i)||jt(i)?!1:(e.advance(),e.lastIntValue=i,!0)};function qt(e){return e===33||e>=35&&e<=38||e>=42&&e<=44||e===46||e>=58&&e<=64||e===94||e===96||e===126}function jt(e){return e===40||e===41||e===45||e===47||e>=91&&e<=93||e>=123&&e<=125}c.regexp_eatClassSetReservedPunctuator=function(e){var t=e.current();return Gt(t)?(e.lastIntValue=t,e.advance(),!0):!1};function Gt(e){return e===33||e===35||e===37||e===38||e===44||e===45||e>=58&&e<=62||e===64||e===96||e===126}c.regexp_eatClassControlLetter=function(e){var t=e.current();return he(t)||t===95?(e.lastIntValue=t%32,e.advance(),!0):!1},c.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},c.regexp_eatDecimalDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;he(i=e.current());)e.lastIntValue=10*e.lastIntValue+(i-48),e.advance();return e.pos!==t};function he(e){return e>=48&&e<=57}c.regexp_eatHexDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;nt(i=e.current());)e.lastIntValue=16*e.lastIntValue+ut(i),e.advance();return e.pos!==t};function nt(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function ut(e){return e>=65&&e<=70?10+(e-65):e>=97&&e<=102?10+(e-97):e-48}c.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var i=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=t*64+i*8+e.lastIntValue:e.lastIntValue=t*8+i}else e.lastIntValue=t;return!0}return!1},c.regexp_eatOctalDigit=function(e){var t=e.current();return ot(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)};function ot(e){return e>=48&&e<=55}c.regexp_eatFixedHexDigits=function(e,t){var i=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var r=e.current();if(!nt(r))return e.pos=i,!1;e.lastIntValue=16*e.lastIntValue+ut(r),e.advance()}return!0};var ce=function(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,t.options.locations&&(this.loc=new X(t,t.startLoc,t.endLoc)),t.options.ranges&&(this.range=[t.start,t.end])},x=y.prototype;x.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new ce(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},x.getToken=function(){return this.next(),new ce(this)},typeof Symbol<"u"&&(x[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===a.eof,value:t}}}}),x.nextToken=function(){var e=this.curContext();if((!e||!e.preserveSpace)&&this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length)return this.finishToken(a.eof);if(e.override)return e.override(this);this.readToken(this.fullCharCodeAtPos())},x.readToken=function(e){return V(e,this.options.ecmaVersion>=6)||e===92?this.readWord():this.getTokenFromCode(e)},x.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},x.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,i=this.input.indexOf("*/",this.pos+=2);if(i===-1&&this.raise(this.pos-2,"Unterminated comment"),this.pos=i+2,this.options.locations)for(var s=void 0,r=t;(s=Ie(this.input,r,this.pos))>-1;)++this.curLine,r=this.lineStart=s;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,i),t,this.pos,e,this.curPosition())},x.skipLineComment=function(e){for(var t=this.pos,i=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!U(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,i,this.curPosition())},x.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:this.input.charCodeAt(this.pos+1)===10&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&xe.test(String.fromCharCode(e)))++this.pos;else break e}}},x.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=e,this.value=t,this.updateContext(i)},x.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&e===46&&t===46?(this.pos+=3,this.finishToken(a.ellipsis)):(++this.pos,this.finishToken(a.dot))},x.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):e===61?this.finishOp(a.assign,2):this.finishOp(a.slash,1)},x.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),i=1,s=e===42?a.star:a.modulo;return this.options.ecmaVersion>=7&&e===42&&t===42&&(++i,s=a.starstar,t=this.input.charCodeAt(this.pos+2)),t===61?this.finishOp(a.assign,i+1):this.finishOp(s,i)},x.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12){var i=this.input.charCodeAt(this.pos+2);if(i===61)return this.finishOp(a.assign,3)}return this.finishOp(e===124?a.logicalOR:a.logicalAND,2)}return t===61?this.finishOp(a.assign,2):this.finishOp(e===124?a.bitwiseOR:a.bitwiseAND,1)},x.readToken_caret=function(){var e=this.input.charCodeAt(this.pos+1);return e===61?this.finishOp(a.assign,2):this.finishOp(a.bitwiseXOR,1)},x.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?t===45&&!this.inModule&&this.input.charCodeAt(this.pos+2)===62&&(this.lastTokEnd===0||S.test(this.input.slice(this.lastTokEnd,this.pos)))?(this.skipLineComment(3),this.skipSpace(),this.nextToken()):this.finishOp(a.incDec,2):t===61?this.finishOp(a.assign,2):this.finishOp(a.plusMin,1)},x.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),i=1;return t===e?(i=e===62&&this.input.charCodeAt(this.pos+2)===62?3:2,this.input.charCodeAt(this.pos+i)===61?this.finishOp(a.assign,i+1):this.finishOp(a.bitShift,i)):t===33&&e===60&&!this.inModule&&this.input.charCodeAt(this.pos+2)===45&&this.input.charCodeAt(this.pos+3)===45?(this.skipLineComment(4),this.skipSpace(),this.nextToken()):(t===61&&(i=2),this.finishOp(a.relational,i))},x.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return t===61?this.finishOp(a.equality,this.input.charCodeAt(this.pos+2)===61?3:2):e===61&&t===62&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(a.arrow)):this.finishOp(e===61?a.eq:a.prefix,1)},x.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(t===46){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(a.questionDot,2)}if(t===63){if(e>=12){var s=this.input.charCodeAt(this.pos+2);if(s===61)return this.finishOp(a.assign,3)}return this.finishOp(a.coalesce,2)}}return this.finishOp(a.question,1)},x.readToken_numberSign=function(){var e=this.options.ecmaVersion,t=35;if(e>=13&&(++this.pos,t=this.fullCharCodeAtPos(),V(t,!0)||t===92))return this.finishToken(a.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+F(t)+"'")},x.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(a.parenL);case 41:return++this.pos,this.finishToken(a.parenR);case 59:return++this.pos,this.finishToken(a.semi);case 44:return++this.pos,this.finishToken(a.comma);case 91:return++this.pos,this.finishToken(a.bracketL);case 93:return++this.pos,this.finishToken(a.bracketR);case 123:return++this.pos,this.finishToken(a.braceL);case 125:return++this.pos,this.finishToken(a.braceR);case 58:return++this.pos,this.finishToken(a.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(a.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(t===120||t===88)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(t===111||t===79)return this.readRadixNumber(8);if(t===98||t===66)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(a.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+F(e)+"'")},x.finishOp=function(e,t){var i=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,i)},x.readRegexp=function(){for(var e,t,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(S.test(s)&&this.raise(i,"Unterminated regular expression"),e)e=!1;else{if(s==="[")t=!0;else if(s==="]"&&t)t=!1;else if(s==="/"&&!t)break;e=s==="\\"}++this.pos}var r=this.input.slice(i,this.pos);++this.pos;var n=this.pos,u=this.readWord1();this.containsEsc&&this.unexpected(n);var o=this.regexpState||(this.regexpState=new L(this));o.reset(i,r,u),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var h=null;try{h=new RegExp(r,u)}catch{}return this.finishToken(a.regexp,{pattern:r,flags:u,value:h})},x.readInt=function(e,t,i){for(var s=this.options.ecmaVersion>=12&&t===void 0,r=i&&this.input.charCodeAt(this.pos)===48,n=this.pos,u=0,o=0,h=0,f=t??1/0;h<f;++h,++this.pos){var d=this.input.charCodeAt(this.pos),C=void 0;if(s&&d===95){r&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),o===95&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),h===0&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=d;continue}if(d>=97?C=d-97+10:d>=65?C=d-65+10:d>=48&&d<=57?C=d-48:C=1/0,C>=e)break;o=d,u=u*e+C}return s&&o===95&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===n||t!=null&&this.pos-n!==t?null:u};function Ht(e,t){return t?parseInt(e,8):parseFloat(e.replace(/_/g,""))}function ht(e){return typeof BigInt!="function"?null:BigInt(e.replace(/_/g,""))}x.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var i=this.readInt(e);return i==null&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&this.input.charCodeAt(this.pos)===110?(i=ht(this.input.slice(t,this.pos)),++this.pos):V(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(a.num,i)},x.readNumber=function(e){var t=this.pos;!e&&this.readInt(10,void 0,!0)===null&&this.raise(t,"Invalid number");var i=this.pos-t>=2&&this.input.charCodeAt(t)===48;i&&this.strict&&this.raise(t,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!i&&!e&&this.options.ecmaVersion>=11&&s===110){var r=ht(this.input.slice(t,this.pos));return++this.pos,V(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(a.num,r)}i&&/[89]/.test(this.input.slice(t,this.pos))&&(i=!1),s===46&&!i&&(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),(s===69||s===101)&&!i&&(s=this.input.charCodeAt(++this.pos),(s===43||s===45)&&++this.pos,this.readInt(10)===null&&this.raise(t,"Invalid number")),V(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var n=Ht(this.input.slice(t,this.pos),i);return this.finishToken(a.num,n)},x.readCodePoint=function(){var e=this.input.charCodeAt(this.pos),t;if(e===123){this.options.ecmaVersion<6&&this.unexpected();var i=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,t>1114111&&this.invalidStringToken(i,"Code point out of bounds")}else t=this.readHexChar(4);return t},x.readString=function(e){for(var t="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===e)break;s===92?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(!1),i=this.pos):s===8232||s===8233?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(U(s)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(a.string,t)};var ct={};x.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e===ct)this.readInvalidTemplateToken();else throw e}this.inTemplateElement=!1},x.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw ct;this.raise(e,t)},x.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(i===96||i===36&&this.input.charCodeAt(this.pos+1)===123)return this.pos===this.start&&(this.type===a.template||this.type===a.invalidTemplate)?i===36?(this.pos+=2,this.finishToken(a.dollarBraceL)):(++this.pos,this.finishToken(a.backQuote)):(e+=this.input.slice(t,this.pos),this.finishToken(a.template,e));if(i===92)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(U(i)){switch(e+=this.input.slice(t,this.pos),++this.pos,i){case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:e+=`
`;break;default:e+=String.fromCharCode(i);break}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},x.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if(this.input[this.pos+1]!=="{")break;case"`":return this.finishToken(a.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},x.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return`
`;case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return F(this.readCodePoint());case 116:return"	";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:this.input.charCodeAt(this.pos)===10&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var i=this.pos-1;this.invalidStringToken(i,"Invalid escape sequence in template string")}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(s,8);return r>255&&(s=s.slice(0,-1),r=parseInt(s,8)),this.pos+=s.length-1,t=this.input.charCodeAt(this.pos),(s!=="0"||t===56||t===57)&&(this.strict||e)&&this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(r)}return U(t)?"":String.fromCharCode(t)}},x.readHexChar=function(e){var t=this.pos,i=this.readInt(16,e);return i===null&&this.invalidStringToken(t,"Bad character escape sequence"),i},x.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var r=this.fullCharCodeAtPos();if(M(r,s))this.pos+=r<=65535?1:2;else if(r===92){this.containsEsc=!0,e+=this.input.slice(i,this.pos);var n=this.pos;this.input.charCodeAt(++this.pos)!==117&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var u=this.readCodePoint();(t?V:M)(u,s)||this.invalidStringToken(n,"Invalid Unicode escape"),e+=F(u),i=this.pos}else break;t=!1}return e+this.input.slice(i,this.pos)},x.readWord=function(){var e=this.readWord1(),t=a.name;return this.keywords.test(e)&&(t=se[e]),this.finishToken(t,e)};var lt="8.11.3";y.acorn={Parser:y,version:lt,defaultOptions:ae,Position:j,SourceLocation:X,getLineInfo:me,Node:ee,TokenType:v,tokTypes:a,keywordTypes:se,TokContext:P,tokContexts:b,isIdentifierChar:M,isIdentifierStart:V,Token:ce,isNewLine:U,lineBreak:S,lineBreakG:Ae,nonASCIIwhitespace:xe};function Wt(e,t){return y.parse(e,t)}function zt(e,t,i){return y.parseExpressionAt(e,t,i)}function Kt(e,t){return y.tokenizer(e,t)}g.Node=ee,g.Parser=y,g.Position=j,g.SourceLocation=X,g.TokContext=P,g.Token=ce,g.TokenType=v,g.defaultOptions=ae,g.getLineInfo=me,g.isIdentifierChar=M,g.isIdentifierStart=V,g.isNewLine=U,g.keywordTypes=se,g.lineBreak=S,g.lineBreakG=Ae,g.nonASCIIwhitespace=xe,g.parse=Wt,g.parseExpressionAt=zt,g.tokContexts=b,g.tokTypes=a,g.tokenizer=Kt,g.version=lt});
//# sourceMappingURL=acorn.js.map
