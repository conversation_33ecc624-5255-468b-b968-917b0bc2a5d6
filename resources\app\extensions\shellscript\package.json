{"name": "shellscript", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin jeff-hykin/better-shell-syntax autogenerated/shell.tmLanguage.json ./syntaxes/shell-unix-bash.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "shellscript", "aliases": ["<PERSON> Script", "shellscript", "bash", "fish", "sh", "zsh", "ksh", "csh"], "extensions": [".sh", ".bash", ".bashrc", ".bash_aliases", ".bash_profile", ".bash_login", ".ebuild", ".eclass", ".profile", ".bash_logout", ".xprofile", ".xsession", ".xsessionrc", ".Xsession", ".zsh", ".zshrc", ".zprofile", ".zlogin", ".zlogout", ".zshenv", ".zsh-theme", ".fish", ".ksh", ".csh", ".cshrc", ".tcshrc", ".yashrc", ".yash_profile"], "filenames": ["APKBUILD", "PKGBUILD", ".envrc", ".hushlogin", "zshrc", "zshenv", "zlogin", "zprofile", "zlogout", "bashrc_Apple_Terminal", "zshrc_Apple_Terminal"], "filenamePatterns": [".env.*"], "firstLine": "^#!.*\\b(bash|fish|zsh|sh|ksh|dtksh|pdksh|mksh|ash|dash|yash|sh|csh|jcsh|tcsh|itcsh).*|^#\\s*-\\*-[^*]*mode:\\s*shell-script[^*]*-\\*-", "configuration": "./language-configuration.json", "mimetypes": ["text/x-shellscript"]}], "grammars": [{"language": "shellscript", "scopeName": "source.shell", "path": "./syntaxes/shell-unix-bash.tmLanguage.json", "balancedBracketScopes": ["*"], "unbalancedBracketScopes": ["meta.scope.case-pattern.shell"]}], "configurationDefaults": {"[shellscript]": {"files.eol": "\n", "editor.defaultColorDecorators": "never"}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}