name: $(Date:yyyyMMdd)$(Rev:.r)

trigger:
  branches:
    include:
      - main
pr: none

resources:
  repositories:
    - repository: templates
      type: github
      name: microsoft/vscode-engineering
      ref: main
      endpoint: Monaco

parameters:
  - name: publishPackage
    displayName: 🚀 Publish @vscode/ripgrep
    type: boolean
    default: false

extends:
  template: azure-pipelines/npm-package/pipeline.yml@templates
  parameters:
    npmPackages:
      - name: ripgrep

        buildPlatforms:
          - name: Linux
            nodeVersions:
              - 22.x
          - name: MacOS
            nodeVersions:
              - 22.x
          - name: Windows
            nodeVersions:
              - 22.x

        buildSteps:
          - script: npm i
            displayName: Install dependencies

        publishPackage: ${{ parameters.publishPackage }}