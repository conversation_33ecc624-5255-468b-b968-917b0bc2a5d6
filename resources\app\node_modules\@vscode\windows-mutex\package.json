{"name": "@vscode/windows-mutex", "version": "0.5.0", "description": "Expose the Windows CreateMutex API to Node.JS", "main": "index.js", "types": "index.d.ts", "homepage": "https://github.com/microsoft/node-windows-mutex", "bugs": "https://github.com/microsoft/node-windows-mutex/issues", "repository": {"type": "git", "url": "https://github.com/microsoft/node-windows-mutex.git"}, "author": "Microsoft", "license": "MIT", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "7.1.0"}, "devDependencies": {"mocha": "^10.1.0"}, "scripts": {"test": "mocha"}}