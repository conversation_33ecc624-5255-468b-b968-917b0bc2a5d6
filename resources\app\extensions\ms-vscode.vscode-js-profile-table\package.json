{"name": "vscode-js-profile-table", "version": "1.0.10", "displayName": "Table Visualizer for JavaScript Profiles", "description": "Text visualizer for profiles taken from the JavaScript debugger", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/microsoft/vscode-js-profile-visualizer#readme", "license": "MIT", "main": "out/extension.js", "browser": "out/extension.web.js", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-js-profile-visualizer.git"}, "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "icon": "resources/icon.png", "publisher": "ms-vscode", "sideEffects": false, "engines": {"vscode": "^1.74.0"}, "contributes": {"customEditors": [{"viewType": "jsProfileVisualizer.cpuprofile.table", "displayName": "CPU Profile Table Visualizer", "priority": "default", "selector": [{"filenamePattern": "*.cpuprofile"}]}, {"viewType": "jsProfileVisualizer.heapprofile.table", "displayName": "Heap Profile Table Visualizer", "priority": "default", "selector": [{"filenamePattern": "*.heapprofile"}]}, {"viewType": "jsProfileVisualizer.heapsnapshot.table", "displayName": "Heap Snapshot Table Visualizer", "priority": "default", "selector": [{"filenamePattern": "*.heapsnapshot"}]}], "commands": [{"command": "extension.jsProfileVisualizer.table.clearCodeLenses", "title": "Clear Profile Code Lenses"}], "menus": {"commandPalette": [{"command": "extension.jsProfileVisualizer.table.clearCodeLenses", "when": "jsProfileVisualizer.hasCodeLenses == true"}]}}, "bugs": {"url": "https://github.com/microsoft/vscode-js-profile-visualizer/issues"}, "__metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}