# 🐍 彩色贪吃蛇游戏

一个富有趣味性和视觉效果的贪吃蛇游戏，使用Python和Pygame开发。

## 🎮 游戏特色

- **彩色蛇身**: 每吃一个食物，蛇身会增加新的颜色段
- **动画效果**: 食物有脉冲动画，特殊食物有彩虹闪烁效果
- **特殊食物**: 随机出现的高分值特殊食物（50分）
- **蛇头细节**: 带有眼睛的蛇头，根据移动方向调整
- **网格背景**: 清晰的网格线帮助定位
- **实时统计**: 显示分数、速度和蛇的长度
- **渐进难度**: 随着分数增加，游戏速度逐渐提升

## 🎯 游戏玩法

- 使用方向键控制蛇的移动
- 吃红色食物获得10分
- 吃彩虹特殊食物获得50分
- 避免撞墙和撞到自己
- 随着分数增加，游戏速度会提升

## 🎮 控制键

- **方向键**: 控制蛇的移动方向
- **空格键**: 暂停/继续游戏，游戏结束后重新开始
- **ESC键**: 退出游戏

## 🚀 运行游戏

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动游戏

```bash
python snake_game.py
```

## 📋 系统要求

- Python 3.6+
- Pygame 2.0.0+

## 🎨 视觉特效

1. **蛇身渐变**: 蛇身从头到尾颜色逐渐变淡
2. **食物脉冲**: 普通食物有呼吸般的脉冲效果
3. **特殊食物**: 彩虹色循环变化，带星星装饰
4. **蛇头眼睛**: 根据移动方向显示不同位置的眼睛
5. **网格背景**: 深色网格线提供清晰的游戏区域划分

## 🏆 游戏机制

- 初始速度: 8 FPS
- 最高速度: 15 FPS
- 普通食物: 10分
- 特殊食物: 50分，随机出现
- 速度提升: 每吃一个食物速度增加0.5

享受游戏吧！🎉
