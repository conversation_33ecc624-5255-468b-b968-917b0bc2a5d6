{"name": "js-debug-companion", "displayName": "JavaScript Debugger Companion Extension", "description": "Companion extension to js-debug that provides capability for remote debugging", "version": "1.1.3", "publisher": "ms-vscode", "engines": {"vscode": "^1.90.0"}, "icon": "resources/logo.png", "categories": ["Other"], "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-js-debug-companion.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-js-debug-companion/issues"}, "homepage": "https://github.com/microsoft/vscode-js-debug-companion#readme", "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "activationEvents": ["onCommand:js-debug-companion.launchAndAttach", "onCommand:js-debug-companion.kill", "onCommand:js-debug-companion.launch", "onCommand:js-debug-companion.defaultBrowser"], "main": "./out/extension.js", "contributes": {}, "extensionKind": ["ui"], "api": "none", "prettier": {"trailingComma": "all", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "arrowParens": "avoid"}, "__metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}