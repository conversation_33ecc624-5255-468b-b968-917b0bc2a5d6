{"name": "@vscode/deviceid", "version": "0.1.1", "description": "A module for Visual Studio Code to generate a unique device ID", "main": "./dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npm run compile", "compile": "tsc", "lint": "eslint . --max-warnings 0 --report-unused-disable-directives", "lint:fix": "npm run lint -- --fix"}, "keywords": [], "author": "Microsoft Corporation", "license": "MIT", "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^20.12.4", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "eslint": "^8.57.0", "prettier": "^3.2.5"}, "dependencies": {"fs-extra": "^11.2.0", "uuid": "^9.0.1"}}